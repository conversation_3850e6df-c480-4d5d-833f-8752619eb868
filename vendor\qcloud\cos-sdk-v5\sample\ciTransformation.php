<?php

require dirname(__FILE__) . '/../vendor/autoload.php';

$secretId = "SECRETID"; //替换为用户的 secretId，请登录访问管理控制台进行查看和管理，https://console.cloud.tencent.com/cam/capi
$secretKey = "SECRETKEY"; //替换为用户的 secretKey，请登录访问管理控制台进行查看和管理，https://console.cloud.tencent.com/cam/capi
$region = "ap-beijing"; //替换为用户的 region，已创建桶归属的region可以在控制台查看，https://console.cloud.tencent.com/cos5/bucket
$cosClient = new Qcloud\Cos\Client(
    array(
        'region' => $region,
        'schema' => 'https', //协议头部，默认为http
        'credentials'=> array(
            'secretId'  => $secretId ,
            'secretKey' => $secretKey)));
try {
    $imageMogrTemplate = new Qcloud\Cos\ImageParamTemplate\ImageMogrTemplate();
    $imageMogrTemplate->thumbnailByScale(50);
    $imageMogrTemplate->rotate(50);
    $imageViewTemplate = new Qcloud\Cos\ImageParamTemplate\ImageViewTemplate();
    $imageViewTemplate->setMode(1);
    $imageViewTemplate->setWidth(400);
    $imageViewTemplate->setHeight(600);
    $imageViewTemplate->setQuality(1, 85);
    $ciParamTransformation = new Qcloud\Cos\ImageParamTemplate\CIParamTransformation();
    $ciParamTransformation->addRule($imageMogrTemplate);
    $ciParamTransformation->addRule($imageViewTemplate);
    $result = $cosClient->getObject(array(
        'Bucket' => 'examplebucket-125000000', //存储桶名称，由BucketName-Appid 组成，可以在COS控制台查看 https://console.cloud.tencent.com/cos5/bucket
        'Key' => 'exampleobject',
        'ImageHandleParam' => $ciParamTransformation->queryString(),
        'SaveAs' => '/data/exampleobject',
    ));
    // 请求成功
    print_r($result);
} catch (\Exception $e) {
    // 请求失败
    echo($e);
}
