<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\wxapp;

use cores\BaseModel;

/**
 * 微信小程序直播间模型
 * Class LiveRoom
 * @package app\common\model\wxapp
 */
class LiveRoom extends BaseModel
{
    // 定义表名
    protected $name = 'wxapp_live_room';

    // 定义主键
    protected $pk = 'id';

    /**
     * 获取器: 开播时间
     * @param $value
     * @return false|string
     */
    public function getStartTimeAttr($value)
    {
        return date('m/d H:i', $value);
    }

    /**
     * 获取器: 结束时间
     * @param $value
     * @return false|string
     */
    public function getEndTimeAttr($value)
    {
        return date('m/d H:i', $value);
    }

    /**
     * 获取直播间详情
     * @param int $id
     * @return static|array|null
     */
    public static function detail(int $id)
    {
        return static::get($id);
    }
}