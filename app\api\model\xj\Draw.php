<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\api\model\xj;

use app\api\service\User as UserService;
use app\common\model\User as UserModel;
use app\common\model\xj\Draw as DrawModel;
use think\facade\Db;

/**
 * 商品评价模型
 * Class Draw
 * @package app\api\model
 */
class Draw extends DrawModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'is_delete',
        'store_id',
        'update_time',
    ];

    public static function getCount()
    {
        $userId = UserService::getCurrentLoginUserId();
        $time   = strtotime(Date('Y-m-d'));

        $detail = 3;
        $count  = Db::name('xj_draw')->where('user_id', $userId)->where('create_time', '>=', $time)->count();
        $num    = $detail - $count;

        return $num;
    }

    public function add()
    {
        //6个奖项：10积分、20积分、50积分、大米、油、359会员。中奖概率：95%中10个积分，3%中20个积分，2%中50个积分，其它3个0%。
        $userId = UserService::getCurrentLoginUserId();
        $time   = strtotime(Date('Y-m-d'));
        $count  = Db::name('xj_draw')->where('user_id', $userId)->where('create_time', '>=', $time)->count();
        if ($count >= 3) {
            $this->error = '抽奖次数不足';
            return false;
        }
        // 概率分布
        $rand = mt_rand(1, 100);

        if ($rand <= 95) {
            $result = "10积分";
            $value  = 10;
            $angle  = 330;
        } elseif ($rand <= 98) {
            $result = "20积分";
            $value  = 20;
            $angle  = 90;
        } else {
            $result = "50积分";
            $value  = 50;
            $angle  = 210;
        }

        $detail['content']  = $result;
        $detail['value']    = $value;
        $detail['angle']    = $angle;
        $detail['store_id'] = 10001;
        $detail['user_id']  = $userId;

        $this->save($detail);

        //积分记录
        $describe = "抽奖获得：{$result}";
        UserModel::setIncPoints($userId, $value, $describe);

        return $detail;
    }

    /**
     * 获取文章列表
     * @param int $categoryId
     * @param int $limit
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getList(int $limit = 10): \think\Paginator
    {
        // 检索查询条件
        $filter = [];
        $userId = UserService::getCurrentLoginUserId();
        // 获取列表数据
        $list = $this
            ->where($filter)
            ->where('user_id', '=', $userId)

            ->order(['create_time' => 'desc'])
            ->paginate($limit);
        return $list;
    }

}
