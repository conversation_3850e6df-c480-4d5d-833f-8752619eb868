<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\sharp;

use app\common\model\sharp\Goods as GoodsModel;

/**
 * 整点秒杀-商品模型
 * Class Goods
 * @package app\api\model\sharp
 */
class Goods extends GoodsModel
{
    /**
     * 隐藏字段
     * @var array
     */
    public $hidden = [
        // 'seckill_stock',
        'total_sales',
        'sort',
        'status',
        'is_delete',
        'store_id',
        'create_time',
        'update_time',
    ];
}