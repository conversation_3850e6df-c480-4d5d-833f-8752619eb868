<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\fulldiscount;

use app\common\enum\EnumBasics;

/**
 * 枚举类：满额立减活动 - 优惠方式
 * Class Methods
 * @package app\common\enum\fulldiscount
 */
class Methods extends EnumBasics
{
    // 优惠一次
    const ONCE = 10;

//    // 阶梯优惠
//    const LADDER = 20;

    // 循环优惠
    const REPEAT = 30;

    /**
     * 获取枚举类型值
     * @return array
     */
    public static function data(): array
    {
        return [
            self::ONCE => [
                'name' => '优惠一次',
                'value' => self::ONCE,
            ],
//            self::LADDER => [
//                'name' => '阶梯优惠',
//                'value' => self::LADDER,
//            ],
            self::REPEAT => [
                'name' => '循环优惠',
                'value' => self::REPEAT,
            ]
        ];
    }
}
