<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\common\model\xj;

use cores\BaseModel;

/**
 * 用户收货地址模型
 * Class Invite
 * @package app\common\model
 */
class Sign extends BaseModel
{
    // 定义表名
    protected $name = 'xj_sign_log';

    // 定义主键
    protected $pk = 'log_id';
    // 追加字段
    protected $append = ['show_time'];
    public function getShowTimeAttr($value, $data)
    {
        $date         = Date('Y-m-d', $data['create_time']);
        $pattern      = "/\-0(\d{1})/";
        $updated_date = preg_replace($pattern, "-$1", $date);
        return $updated_date;
    }

}
