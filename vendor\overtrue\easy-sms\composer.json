{"name": "overtrue/easy-sms", "description": "The easiest way to send short message.", "type": "library", "require": {"guzzlehttp/guzzle": "^6.2 || ^7.0", "php": ">=5.6", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^7.5 || ^8.5.19 || ^9.5.8", "mockery/mockery": "~1.3.3 || ^1.4.2", "brainmaestro/composer-git-hooks": "^2.8", "jetbrains/phpstorm-attributes": "^1.0"}, "autoload": {"psr-4": {"Overtrue\\EasySms\\": "src"}}, "autoload-dev": {"psr-4": {"Overtrue\\EasySms\\Tests\\": "tests"}}, "license": "MIT", "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "extra": {"hooks": {"pre-commit": ["composer check-style", "composer psalm", "composer test"], "pre-push": ["composer check-style"]}}, "scripts": {"post-update-cmd": ["cghooks remove", "cghooks add --ignore-lock", "cghooks update"], "post-merge": "composer install", "post-install-cmd": ["cghooks remove", "cghooks add --ignore-lock", "cghooks update"], "phpstan": "phpstan analyse", "check-style": "php-cs-fixer fix --using-cache=no --diff --config=.php-cs-fixer.dist.php --dry-run --allow-risky=yes --ansi", "fix-style": "php-cs-fixer fix --using-cache=no --config=.php-cs-fixer.dist.php --allow-risky=yes --ansi", "test": "phpunit --colors", "psalm": "psalm --show-info=true --no-cache", "psalm-fix": "psalm --no-cache --alter --issues=MissingReturnType,MissingParamType"}}