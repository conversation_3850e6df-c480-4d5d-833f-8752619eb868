<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\dealer;

use think\response\Json;
use app\api\controller\Controller;
use app\api\model\dealer\User as DealerUserModel;
use app\api\model\User as UserModel;
use app\api\model\dealer\Withdraw as WithdrawModel;
use app\api\service\User as UserService;
use cores\exception\BaseException;

/**
 * 分销商提现
 * Class Withdraw
 * @package app\api\controller\user\dealer
 */
class Withdraw extends Controller
{
    /**
     * 分销商提现明细
     * @param int $applyStatus 申请状态
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DbException
     */
    public function list(int $applyStatus = -1,int $type=-1): Json
    {
        $model = new WithdrawModel;
        $list = $model->getList($applyStatus,$type);
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 提交提现申请
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function submit(): Json
    {
        // 当前用户ID
        $userId = UserService::getCurrentLoginUserId();
        // 分销商用户详情
        $dealer = DealerUserModel::detail($userId);
        // 提交提现申请
        $model = new WithdrawModel;
        if ($model->submit($dealer, $this->postForm())) {
            return $this->renderSuccess([], '提现申请已提交成功，请等待审核');
        }
        return $this->renderError($model->getError() ?: '提交失败');
    }

       public function submitBalance(): Json
    {
        // 当前用户ID
        $userId = UserService::getCurrentLoginUserId();
        // 分销商用户详情
        $dealer = UserModel::detail($userId);
        // 提交提现申请
        $model = new WithdrawModel;
        if ($model->submitBalance($dealer, $this->postForm())) {
            return $this->renderSuccess([], '提现申请已提交成功，请等待审核');
        }
        return $this->renderError($model->getError() ?: '提交失败');
    }

}