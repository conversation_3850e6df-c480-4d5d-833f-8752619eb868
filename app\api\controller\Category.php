<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller;

use think\response\Json;
use app\api\model\Category as CategoryModel;

/**
 * 商品分类控制器
 * Class Category
 * @package app\api\controller
 */
class Category extends Controller
{
    /**
     * 分类列表
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(): Json
    {
        $model = new CategoryModel;
        $list = $model->getListPublic($this->request->param());
        return $this->renderSuccess(compact('list'));
    }
        public function listVip(): Json
    {
        $model = new CategoryModel;
        $list = $model->getListVip($this->request->param());
        return $this->renderSuccess(compact('list'));
    }
          public function listPoints(): Json
    {
        $model = new CategoryModel;
        $list = $model->getListPoints($this->request->param());
        return $this->renderSuccess(compact('list'));
    }
}
