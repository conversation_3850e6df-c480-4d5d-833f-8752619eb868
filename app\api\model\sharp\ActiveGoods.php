<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\sharp;

use think\model\Collection;
use app\api\model\Goods as GoodsModel;
use app\api\model\sharp\Goods as SharpGoodsModel;
use app\common\model\sharp\ActiveGoods as ActiveGoodsModel;
use app\common\library\helper;
use cores\exception\BaseException;

/**
 * 整点秒杀-活动会场与商品关联模型
 * Class ActiveGoods
 * @package app\api\model\sharp
 */
class ActiveGoods extends ActiveGoodsModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'store_id',
        'create_time',
        'update_time',
    ];

    /**
     * 获取指定商品的活动详情
     * @param int $activeTimeId 秒杀会场场次ID
     * @param int $sharpGoodsId 秒杀商品ID
     * @return array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getGoodsActive(int $activeTimeId, int $sharpGoodsId)
    {
        $data = (new static)->with(['active_time'])
            ->where('active_time_id', '=', $activeTimeId)
            ->where('sharp_goods_id', '=', $sharpGoodsId)
            ->find();
        return static::related($data, ['active']);
    }

    /**
     * 获取活动商品详情
     * @param $active
     * @param int $sharpGoodsId 秒杀商品ID
     * @param bool $isCheckStatus 是否验证秒杀商品的状态
     * @return Collection
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getGoodsActiveDetail($active, int $sharpGoodsId, bool $isCheckStatus = true)
    {
        // 获取商品详情
        $sharpGoods = $this->getGoodsDetail($sharpGoodsId);
        if ($isCheckStatus && ($sharpGoods['is_delete'] || !$sharpGoods['status'])) {
            throwError('很抱歉，秒杀商品不存在或已下架');
        }
        // 活动商品的销量
        $sharpGoods['sales_actual'] = $active['sales_actual'];
        // 商品销售进度
        $sharpGoods['progress'] = $this->getProgress($active['sales_actual'], $sharpGoods['seckill_stock']);
        /* @var $goods Collection */
        return $sharpGoods;
    }

    /**
     * 获取秒杀商品基本信息(用于订单结算)
     * @param int $sharpGoodsId 秒杀商品ID
     * @param bool $isCheckStatus 是否验证秒杀商品的状态
     * @return mixed
     * @throws BaseException
     */
    public function getGoodsBasic(int $sharpGoodsId, bool $isCheckStatus = true)
    {
        // 获取秒杀商品详情
        $sharpGoods = SharpGoodsModel::detail($sharpGoodsId);
        empty($sharpGoods) && throwError('很抱歉，秒杀商品信息不存在');
        if ($isCheckStatus && ($sharpGoods['is_delete'] || !$sharpGoods['status'])) {
            throwError('很抱歉，秒杀商品不存在或已下架');
        }
        // 获取主商品详情
        $goods = (new GoodsModel)->getBasic($sharpGoods['goods_id'], false);
        // 整理秒杀商品信息
        return $this->mergeMainGoods($sharpGoods, $goods);
    }

    /**
     * 获取秒杀商品详情(用于页面详情)
     * @param int $sharpGoodsId 秒杀商品ID
     * @return mixed
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getGoodsDetail(int $sharpGoodsId)
    {
        // 获取秒杀商品详情
        $sharpGoods = SharpGoodsModel::detail($sharpGoodsId, ['skuList']);
        empty($sharpGoods) && throwError('很抱歉，秒杀商品信息不存在');
        // 获取主商品详情
        $goods = (new GoodsModel)->getDetails($sharpGoods['goods_id'], false);
        // 整理秒杀商品信息
        $goods = $this->mergeMainGoods($sharpGoods, $goods);
        // 商品sku信息
        $goods['skuList'] = $this->getSharpSku($sharpGoods['skuList'], $goods['skuList']);
        return $goods;
    }

    /**
     * 合并秒杀商品信息到主商品
     * @param SharpGoodsModel $sharpGoods 秒杀商品详情
     * @param GoodsModel $goods 主商品详情
     * @return mixed
     */
    private function mergeMainGoods(SharpGoodsModel $sharpGoods, GoodsModel $goods)
    {
        $goods['sharp_goods_id'] = $sharpGoods['sharp_goods_id'];
        $goods['deduct_stock_type'] = $sharpGoods['deduct_stock_type'];
        $goods['seckill_price'] = $sharpGoods['seckill_price_min'];
        $goods['original_price'] = $goods['goods_price_min'];
        $goods['limit_num'] = $sharpGoods['limit_num'];
        $goods['seckill_stock'] = $sharpGoods['seckill_stock'];
        $goods['total_sales'] = $sharpGoods['total_sales'];
        return $goods;
    }

    /**
     * 获取秒杀商品的sku信息
     * @param $sharpSku
     * @param $goodsSku
     * @return mixed
     */
    protected function getSharpSku($sharpSku, $goodsSku)
    {
        $sharpSku = helper::arrayColumn2Key($sharpSku, 'goods_sku_id');
        foreach ($goodsSku as &$item) {
            $sharpSkuItem = clone $sharpSku[$item['goods_sku_id']];
            $item['original_price'] = $item['goods_price'];
            $item['seckill_price'] = $sharpSkuItem['seckill_price'];
            $item['seckill_stock'] = $sharpSkuItem['seckill_stock'];
        }
        return $goodsSku;
    }

    /**
     * 根据商品ID集获取商品列表
     * @param array $sharpGoodsIds 秒杀商品ID集
     * @param array $param
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected function getGoodsListByIds(array $sharpGoodsIds, array $param = [])
    {
        return (new SharpGoodsModel)->getListByIds($sharpGoodsIds, $param);
    }
}