<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\wxofficial;

use cores\BaseModel;
use think\facade\Cache;
use app\common\library\helper;

/**
 * 模型类：微信公众号设置
 * Class Setting
 * @package app\common\model\wxofficial
 */
class Setting extends BaseModel
{
    // 定义表名
    protected $name = 'wxofficial_setting';

    protected $createTime = false;

    /**
     * 获取器: 转义数组格式
     * @param $value
     * @return array
     */
    public function getValuesAttr($value): array
    {
        return helper::jsonDecode($value);
    }

    /**
     * 修改器: 转义成json格式
     * @param $value
     * @return string
     */
    public function setValuesAttr($value): string
    {
        return helper::jsonEncode($value);
    }

    /**
     * 获取指定项设置
     * @param string $key
     * @param int|null $storeId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getItem(string $key, int $storeId = null): array
    {
        $data = static::getAll($storeId);
        return isset($data[$key]) ? $data[$key]['values'] : [];
    }

    /**
     * 获取全部设置
     * @param int|null $storeId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getAll(?int $storeId = null): array
    {
        $model = new static;
        is_null($storeId) && $storeId = static::$storeId;
        if (!$data = Cache::get("wxofficial_setting_{$storeId}")) {
            // 获取全部设置
            $data = $model->getList($storeId);
            // 写入缓存中
            Cache::tag('cache')->set("wxofficial_setting_{$storeId}", $data);
        }
        // 重组setting缓存数据 (多维)
        return static::reorganize($model->defaultData(), $data, $type = 'cache');
    }

    /**
     * 获取设置项列表
     * @param int $storeId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getList(int $storeId): array
    {
        // 获取所有设置项
        $data = $this->where('store_id', '=', $storeId)->select();
        return $data->isEmpty() ? [] : helper::arrayColumn2Key($data->toArray(), 'key');
    }

    /**
     * 获取设置项信息
     * @param string $key
     * @return static|array|null
     */
    public static function detail(string $key)
    {
        return static::get(compact('key'));
    }

    /**
     * 默认配置
     * @return array
     */
    public function defaultData(): array
    {
        return [
            'basic' => [
                'key' => 'basic',
                'describe' => '基础设置',
                'values' => [
                    // 是否启用微信公众号端访问
                    'enabled' => true,

                    // 公众号名称
                    'name' => '',
                    // 原始ID
                    'originalId' => '',
                    // 公众号二维码 (图片ID) 废弃
                    'qrcodeImageId' => '',
                    // 公众号二维码 (图片URL)
                    'qrcodeImageUrl' => '',

                    // 开发者AppID
                    'appId' => '',
                    // 开发者AppSecret
                    'appSecret' => '',

                    // 令牌Token
                    'token' => 'YoShopV2',
                    // 消息加解密秘钥 (43位字符组成)
                    'encodingAesKey' => '',
                    // 消息加解密模式 (10明文模式 20兼容模式 30安全模式)
                    'encryptionType' => 10,
                ]
            ],
            'share' => [
                'key' => 'share',
                'describe' => '分享设置',
                'values' => [
                    // 是否开启分享卡片
                    'enabled' => false,
                    // 分享标题
                    'title' => '精选好货，多快好省，集您所需！',
                    // 分享描述
                    'desc' => '微信购物，正品保证，新品首发，手快有手慢无！',
                    // 分享图标
                    'imgUrl' => base_url() . 'assets/store/img/wxofficial/share.jpg',
                ]
            ]
        ];
    }
}