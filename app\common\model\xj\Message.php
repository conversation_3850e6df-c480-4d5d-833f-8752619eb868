<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\common\model\xj;

use cores\BaseModel;
use think\model\relation\BelongsTo;
use think\model\relation\HasOne;

/**
 * 文章模型
 * Class Message
 * @package app\common\model
 */
class Message extends BaseModel
{
    // 定义表名
    protected $name = 'xj_message';

    // 定义主键
    protected $pk = 'log_id';

    // 追加字段
    protected $append = [];



    /**
     * 文章详情
     * @param int $articleId
     * @param array $with
     * @return static|array|null
     */
    public static function detail($where, array $with = [])
    {
        return self::get($where, $with);
    }
}
