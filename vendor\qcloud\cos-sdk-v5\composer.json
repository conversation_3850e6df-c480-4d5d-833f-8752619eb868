{"name": "qcloud/cos-sdk-v5", "description": "PHP SDK for QCloud COS", "keywords": ["qcloud", "cos", "php"], "license": "MIT", "authors": [{"name": "yaozongyou", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tu<PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Qcloud\\Cos\\": "src/"}, "files": ["src/Common.php"]}, "autoload-dev": {"psr-4": {"Qcloud\\Cos\\Tests\\": "tests/"}}, "require": {"php": ">=5.6", "ext-curl": "*", "ext-json": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.2.1 || ^7.0", "guzzlehttp/guzzle-services": "^1.1", "guzzlehttp/psr7": "^1.3.1 || ^2.0"}, "config": {"optimize-autoloader": true}, "scripts": {"test": ["@putenv XDEBUG_MODE=coverage", "phpunit -v --color=always"]}, "extra": {"branch-alias": {"dev-master": "2.4-dev"}}}