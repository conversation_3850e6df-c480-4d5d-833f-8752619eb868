(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["sharp"],{"06ac":function(t,e,a){"use strict";a("2951")},"1bdd":function(t,e,a){"use strict";a.d(e,"a",(function(){return k}));var r=function(){var t=this,e=t._self._c;return e("div",[e("a-form-item",{directives:[{name:"show",rawName:"v-show",value:t.multiSpecData.skuList.length,expression:"multiSpecData.skuList.length"}],attrs:{label:"商品SKU",labelCol:t.labelCol,wrapperCol:t.wrapperCol,required:""}},[t.multiSpecData.skuList.length>1?e("div",{staticClass:"sku-batch"},[e("span",{staticClass:"title"},[t._v("批量设置:")]),e("a-input-number",{attrs:{placeholder:"秒杀价格",min:.01,precision:2},model:{value:t.multiSpecData.skuBatchForm.seckill_price,callback:function(e){t.$set(t.multiSpecData.skuBatchForm,"seckill_price",e)},expression:"multiSpecData.skuBatchForm.seckill_price"}}),e("a-input-number",{attrs:{placeholder:"秒杀库存",min:0,precision:0},model:{value:t.multiSpecData.skuBatchForm.seckill_stock,callback:function(e){t.$set(t.multiSpecData.skuBatchForm,"seckill_stock",e)},expression:"multiSpecData.skuBatchForm.seckill_stock"}}),e("a-button",{on:{click:t.handleSkuBatch}},[t._v("立即设置")])],1):t._e(),e("a-table",{staticClass:"sku-list",attrs:{columns:t.multiSpecData.skuColumns,dataSource:t.multiSpecData.skuList,scroll:{x:!0},pagination:!1,bordered:""},scopedSlots:t._u([{key:"goods_sku_no",fn:function(a){return[e("span",[t._v(t._s(a||"--"))])]}},{key:"seckill_price",fn:function(a,r){return[e("a-input-number",{attrs:{size:"small",min:.01,precision:2},model:{value:r.seckill_price,callback:function(e){t.$set(r,"seckill_price",e)},expression:"item.seckill_price"}})]}},{key:"seckill_stock",fn:function(a,r){return[e("a-input-number",{attrs:{size:"small",min:0,precision:0},model:{value:r.seckill_stock,callback:function(e){t.$set(r,"seckill_stock",e)},expression:"item.seckill_stock"}})]}}])})],1)],1)},i=[],n=a("4d91"),o=a("5530"),s=a("d4ec"),l=a("bee2"),c=(a("d3b7"),a("159b"),a("a15b"),a("d81d"),a("99af"),a("7db0"),a("b64b"),a("a434"),a("b0c0"),a("2ef0")),u=a.n(c),d=a("ca00"),p=[{title:"SKU编码",dataIndex:"goods_sku_no",width:150,scopedSlots:{customRender:"goods_sku_no"}},{title:"商品价格",dataIndex:"goods_price",width:150,scopedSlots:{customRender:"goods_price"}},{title:"商品库存",dataIndex:"stock_num",width:150,scopedSlots:{customRender:"stock_num"}},{title:"秒杀价格",dataIndex:"seckill_price",width:150,scopedSlots:{customRender:"seckill_price"}},{title:"秒杀库存",dataIndex:"seckill_stock",width:150,scopedSlots:{customRender:"seckill_stock"}}],f={goods_sku_id:"",goods_sku_no:"",goods_price:"",stock_num:"",seckill_price:"",seckill_stock:""},m=function(){function t(){Object(s["a"])(this,t),this.multiSpecData={},this.error="",this.multiSpecData={specList:[],skuList:[],skuColumns:u.a.cloneDeep(p),skuBatchForm:u.a.cloneDeep(f)}}return Object(l["a"])(t,[{key:"getData",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];t.length&&(this.multiSpecData.specList=u.a.cloneDeep(t),this.multiSpecData.skuList=u.a.cloneDeep(e));var a=this.specGroupArr(),r=v(a),i=this.rowSpanArr(a,r);return this.buildSkuColumns(i),this.buildSkuList(r),this.multiSpecData}},{key:"isEmpty",value:function(){return 0===this.multiSpecData.specList.length}},{key:"getError",value:function(){return this.error}},{key:"specGroupArr",value:function(){var t=[];return this.multiSpecData.specList.forEach((function(e){var a=[];e.valueList.forEach((function(t){a.push(t)})),t.push(a)})),t}},{key:"rowSpanArr",value:function(t,e){for(var a=[],r=e.length,i=0;i<t.length;i++)a[i]=parseInt(r/t[i].length),r=a[i];return a}},{key:"buildSkuList",value:function(t){for(var e=[],a=function(){var a=Object(o["a"])(Object(o["a"])({},f),{},{key:r,skuKey:t[r].map((function(t){return t.key})).join("_"),skuKeys:t[r].map((function(t){return{groupKey:t.groupKey,valueKey:t.key}}))});t[r].forEach((function(t,e){a["spec_value_".concat(e)]=t.spec_value})),e.push(a)},r=0;r<t.length;r++)a();this.multiSpecData.skuList=this.oldSkuList(e)}},{key:"oldSkuList",value:function(t){var e=this.multiSpecData.skuList.concat();if(!e.length||!t.length)return t;var a=function(a){var r={};r=e.length===t.length?u.a.cloneDeep(e[a]):e.find((function(e){return e.skuKey===t[a].skuKey})),r&&(t[a]=Object(o["a"])(Object(o["a"])({},t[a]),u.a.pick(r,Object.keys(f))))};for(var r in t)a(r);return t}},{key:"buildSkuColumns",value:function(t){for(var e=this.multiSpecData.specList,a=p.concat(),r=function(e,a,r,i){var n={children:a,attrs:{}},o=t[e-1];return n.attrs.rowSpan=i%o===0?o:0,n},i=function(t){var i=e[t-1];a.unshift({title:i.spec_name,dataIndex:"spec_value_".concat(t-1),customRender:function(e,a,i){return r(t,e,a,i)}})},n=e.length;n>0;n--)i(n);this.multiSpecData.skuColumns=a}},{key:"handleAddSpecGroup",value:function(){var t=this.multiSpecData.specList;t.push({key:t.length||0,spec_name:"",valueList:[]});var e=t.length-1;this.handleAddSpecValue(e)}},{key:"handleAddSpecValue",value:function(t){var e=this.multiSpecData.specList[t],a=e.valueList;a.push({key:a.length||0,groupKey:e.key,spec_value:""})}},{key:"handleDeleteSpecGroup",value:function(t){this.multiSpecData.specList.splice(t,1),this.onUpdate(!1)}},{key:"handleDeleteSpecValue",value:function(t,e){this.multiSpecData.specList[t].valueList.splice(e,1),this.onUpdate(!1)}},{key:"handleSkuBatch",value:function(){var t=this.getFilterObject(this.multiSpecData.skuBatchForm),e=this.multiSpecData.skuList;for(var a in e)e[a]=Object(o["a"])(Object(o["a"])({},e[a]),t);this.onUpdate(!1)}},{key:"getFilterObject",value:function(t){var e={};for(var a in t){var r=t[a];Object(d["f"])(r)||(e[a]=r)}return e}},{key:"verifyForm",value:function(){return!!this.verifySpec()&&!!this.verifySkuList()}},{key:"verifySkuList",value:function(){var t=[{field:"seckill_price",name:"秒杀价格"},{field:"seckill_stock",name:"秒杀库存"}],e=this.multiSpecData.skuList;for(var a in e){var r=e[a];for(var i in t){var n=r[t[i].field];if(""===n||null===n)return this.error="".concat(t[i].name,"不能为空"),!1}}return!0}},{key:"verifySpec",value:function(){var t=this.multiSpecData.specList;if(!t.length)return this.error="亲，还没有添加规格组~",!1;for(var e in t){var a=t[e];if(Object(d["f"])(a.spec_name))return this.error="规格组名称不能为空~",!1;var r=a.valueList;if(!r.length)return this.error="还没有添加规格值~",!1;for(var i in r)if(Object(d["f"])(r[i].spec_value))return this.error="规格值不能为空~",!1}return!0}},{key:"getFromSpecData",value:function(){var t=this.multiSpecData,e=(t.specList,t.skuList),a={skuList:u.a.cloneDeep(e)};for(var r in a.skuList){var i=a.skuList[r];delete i.image,delete i.key}return a}},{key:"onUpdate",value:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];t?Object(d["c"])(h,200)(this):h(this)}}]),t}(),h=function(t){return t.getData()},v=function(t){return t.length?Array.prototype.reduce.call(t,(function(t,e){var a=[];return t.forEach((function(t){e.forEach((function(e){a.push(t.concat([e]))}))})),a}),[[]]):[]},b={props:{defaultSpecList:n["a"].array.def([]),defaultSkuList:n["a"].array.def([])},data:function(){return{labelCol:{span:3},wrapperCol:{span:21},MultiSpecModel:new m,multiSpecData:{specList:[],skuList:[]}}},watch:{defaultSpecList:function(t){t.length&&this.MultiSpecModel.isEmpty()&&this.getData()}},created:function(){this.getData()},methods:{getData:function(){var t=this.defaultSpecList,e=this.defaultSkuList;this.multiSpecData=this.MultiSpecModel.getData(t,e)},getFromSpecData:function(){return this.MultiSpecModel.getFromSpecData()},handleSkuBatch:function(){this.MultiSpecModel.handleSkuBatch()},verifyForm:function(){return!!this.MultiSpecModel.verifyForm()||(this.$message.error(this.MultiSpecModel.getError(),2),!1)}}},g=b,_=(a("2c5f"),a("2877")),y=Object(_["a"])(g,r,i,!1,null,"7731882e",null),k=y.exports},"1bf4":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"card-title"},[t._v(t._s(t.$route.meta.title))]),e("div",{staticClass:"table-operator"},[e("a-row",[e("a-col",{attrs:{span:5}},[t.$auth("/apps/sharp/active/time/create")?e("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:function(e){return t.handleAdd()}}},[t._v("新增场次")]):t._e()],1)],1)],1),e("s-table",{ref:"table",attrs:{rowKey:"active_time_id",loading:t.isLoading,columns:t.columns,data:t.loadData,pageSize:15},scopedSlots:t._u([{key:"status",fn:function(a,r){return[e("a-tag",{staticClass:"cur-p",attrs:{color:a?"green":"orange"},on:{click:function(e){return t.handleStatus(r,a?0:1)}}},[t._v(t._s(a?"启用":"禁用"))])]}},{key:"action",fn:function(a,r){return e("span",{staticClass:"actions"},[t.$auth("/apps/sharp/active/time/update")?e("a",{on:{click:function(e){return t.handleEdit(r)}}},[t._v("编辑")]):t._e(),e("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(e){return t.handleDelete(r)}}},[t._v("删除")])])}}])})],1)},i=[],n=a("5530"),o=(a("d3b7"),a("e4f5")),s=a("ab09"),l={name:"Index",components:{STable:s["b"]},data:function(){var t=this;return{queryParam:{},isLoading:!1,columns:[{title:"活动场次ID",dataIndex:"active_time_id"},{title:"场次时间",dataIndex:"active_time",scopedSlots:{customRender:"active_time"}},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"创建时间",dataIndex:"create_time"},{title:"操作",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(e){var a=t.activeId,r=t.queryParam;return o["f"](a,Object(n["a"])(Object(n["a"])({},e),r)).then((function(t){return t.data.list}))},activeId:this.$route.query.activeId}},created:function(){},methods:{handleAdd:function(){this.$router.push({path:"./create",query:{activeId:this.activeId}})},handleEdit:function(t){this.$router.push({path:"./update",query:{activeTimeId:t.active_time_id}})},handleStatus:function(t,e){var a=this,r=e?"启用":"禁用",i=this.$confirm({title:"您确定要".concat(r,"该活动吗?"),onOk:function(){return o["g"](t.active_time_id,e).then((function(t){a.$message.success(t.message,1.5),a.handleRefresh()})).finally((function(){return i.destroy()}))}})},handleDelete:function(t){var e=this,a=e.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return o["b"](t.active_time_id).then((function(t){e.$message.success(t.message,1.5),e.handleRefresh()})).finally((function(t){return a.destroy()}))}})},handleRefresh:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(t)}}},c=l,u=a("2877"),d=Object(u["a"])(c,r,i,!1,null,null,null);e["default"]=d.exports},"1da1":function(t,e,a){"use strict";a.d(e,"a",(function(){return i}));a("d3b7");function r(t,e,a,r,i,n,o){try{var s=t[n](o),l=s.value}catch(c){return void a(c)}s.done?e(l):Promise.resolve(l).then(r,i)}function i(t){return function(){var e=this,a=arguments;return new Promise((function(i,n){var o=t.apply(e,a);function s(t){r(o,i,n,s,l,"next",t)}function l(t){r(o,i,n,s,l,"throw",t)}s(void 0)}))}}},2951:function(t,e,a){},"2c5f":function(t,e,a){"use strict";a("45bc")},"314a":function(t,e,a){},3642:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"card-title"},[t._v(t._s(t.$route.meta.title))]),e("a-spin",{attrs:{spinning:t.isLoading}},[e("a-form",{attrs:{form:t.form},on:{submit:t.handleSubmit}},[e("a-form-item",{attrs:{label:"活动日期",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-date-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["active_date",{rules:[{required:!0,message:"请指定活动日期"}]}],expression:"['active_date', { rules: [{ required: true, message: '请指定活动日期' }] }]"}],attrs:{format:"YYYY-MM-DD"}}),e("p",{staticClass:"form-item-help"},[e("small",[t._v("注：活动日期保存后不能更改")])])],1),e("a-form-item",{attrs:{label:"活动场次",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-checkbox-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["activeTimes",{rules:[{required:!0,message:"请指定活动场次"}]}],expression:"['activeTimes', { rules: [{ required: true, message: '请指定活动场次' }] }]"}],staticClass:"times-checkbox",attrs:{options:t.timesOptions}})],1),e("a-form-item",{attrs:{label:"秒杀商品",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("SelectSharpGoods",{directives:[{name:"decorator",rawName:"v-decorator",value:["sharpGoodsIds"],expression:"['sharpGoodsIds']"}],attrs:{multiple:!0}}),e("p",{staticClass:"form-item-help"},[e("small",[t._v("注：每个活动场次中出售的秒杀商品，此处非必填，可在场次管理中单独设置")])])],1),e("a-form-item",{attrs:{label:"活动状态",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[e("a-radio",{attrs:{value:1}},[t._v("启用")]),e("a-radio",{attrs:{value:0}},[t._v("禁用")])],1)],1),e("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:t.wrapperCol.span,offset:t.labelCol.span}}},[e("a-button",{attrs:{type:"primary","html-type":"submit",loading:t.isBtnLoading}},[t._v("提交")])],1)],1)],1)],1)},i=[],n=(a("fb6a"),a("d3b7"),a("2ef0"),a("9eb5")),o=a("2af9"),s=function(){for(var t=[],e=0;e<=23;e++)t.push({label:("0"+e).slice(-2)+":00",value:e});return t},l={components:{SelectSharpGoods:o["k"]},data:function(){return{isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},form:this.$form.createForm(this),timesOptions:s()}},methods:{handleSubmit:function(t){t.preventDefault();var e=this.form.validateFields,a=this.onFormSubmit;e((function(t,e){!t&&a(e)}))},onFormSubmit:function(t){var e=this;this.isLoading=!0,this.isBtnLoading=!0,n["a"]({form:t}).then((function(t){e.$message.success(t.message,1.5),setTimeout((function(){e.$router.push("./index")}),1200)})).catch((function(){return e.isBtnLoading=!1})).finally((function(){return e.isLoading=!1}))}}},c=l,u=(a("6e4f"),a("2877")),d=Object(u["a"])(c,r,i,!1,null,"351068a8",null);e["default"]=d.exports},"3abe":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"card-title"},[t._v(t._s(t.$route.meta.title))]),e("a-spin",{attrs:{spinning:t.isLoading}},[e("a-form",{attrs:{form:t.form},on:{submit:t.handleSubmit}},[e("a-form-item",{attrs:{label:"选择商品",labelCol:t.labelCol,wrapperCol:t.wrapperCol,required:""}},[e("SelectGoods",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_id",{rules:[{required:!0,message:"请选择指定的商品"}]}],expression:"['goods_id', { rules: [{ required: true, message: '请选择指定的商品' }] }]"}],attrs:{multiple:!1}}),e("p",{staticClass:"form-item-help"},[e("small",[t._v("注：添加秒杀商品后，将不允许修改主商品的规格属性")])])],1),e("a-form-item",{attrs:{label:"限购数量",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["limit_num",{initialValue:1,rules:[{required:!0,message:"请输入限购数量"}]}],expression:"['limit_num', { initialValue: 1, rules: [{ required: true, message: '请输入限购数量' }] }]"}],attrs:{min:0,precision:0}}),e("span",{staticClass:"ml-5"},[t._v("件")]),e("p",{staticClass:"form-item-help"},[e("small",[t._v("注：每人限制购买的数量，如果填写0则不限购")])])],1),e("a-form-item",{attrs:{label:"库存计算方式",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["deduct_stock_type",{initialValue:10,rules:[{required:!0}]}],expression:"['deduct_stock_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[e("a-radio",{attrs:{value:10}},[t._v("下单减库存")]),e("a-radio",{attrs:{value:20,disabled:""}},[t._v("付款减库存")])],1),e("p",{staticClass:"form-item-help"},[e("small",[t._v("注：秒杀商品为防止超卖风险仅支持下单减库存")])])],1),e("a-form-item",{attrs:{label:"商品状态",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[e("a-radio",{attrs:{value:1}},[t._v("上架")]),e("a-radio",{attrs:{value:0}},[t._v("下架")])],1)],1),e("a-form-item",{attrs:{label:"排序",labelCol:t.labelCol,wrapperCol:t.wrapperCol,extra:"数字越小越靠前"}},[e("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1),e("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:t.wrapperCol.span,offset:t.labelCol.span}}},[e("a-button",{attrs:{type:"primary","html-type":"submit",loading:t.isBtnLoading}},[t._v("提交")])],1)],1)],1)],1)},i=[],n=(a("d3b7"),a("2ef0"),a("42af")),o=(a("ca00"),a("2af9")),s=a("1bdd"),l={components:{SelectGoods:o["g"],MultiSpec:s["a"]},data:function(){return{isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},form:this.$form.createForm(this)}},created:function(){},methods:{handleSubmit:function(t){t.preventDefault();this.record;var e=this.form.validateFields,a=this.onFormSubmit;e((function(t,e){!t&&a(e)}))},onFormSubmit:function(t){var e=this;this.isLoading=!0,this.isBtnLoading=!0,n["a"]({form:t}).then((function(t){e.$message.success(t.message,1.5),setTimeout((function(){e.$router.push("./index")}),1200)})).catch((function(){return e.isBtnLoading=!1})).finally((function(){return e.isLoading=!1}))}}},c=l,u=(a("b4bb"),a("2877")),d=Object(u["a"])(c,r,i,!1,null,"972bb6ce",null);e["default"]=d.exports},"45bc":function(t,e,a){},"4ab4":function(t,e,a){"use strict";a("314a")},"568a":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"card-title"},[t._v(t._s(t.$route.meta.title))]),e("a-spin",{attrs:{spinning:t.isLoading}},[e("a-form",{attrs:{form:t.form},on:{submit:t.handleSubmit}},[e("a-form-item",{attrs:{label:"活动日期",labelCol:t.labelCol,wrapperCol:t.wrapperCol,required:""}},[e("span",[t._v(t._s(t.detail?t.detail.active.active_date:"--"))])]),e("a-form-item",{attrs:{label:"活动场次",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("span",[t._v(t._s(t.detail?t.detail.active_time:"--"))])]),e("a-form-item",{attrs:{label:"秒杀商品",labelCol:t.labelCol,wrapperCol:t.wrapperCol,required:""}},[e("SelectSharpGoods",{directives:[{name:"decorator",rawName:"v-decorator",value:["sharpGoodsIds",{rules:[{required:!0,message:"请选择指定的秒杀商品"}]}],expression:"['sharpGoodsIds', { rules: [{ required: true, message: '请选择指定的秒杀商品' }] }]"}],attrs:{multiple:!0,defaultList:t.goodsList}})],1),e("a-form-item",{attrs:{label:"活动状态",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[e("a-radio",{attrs:{value:1}},[t._v("启用")]),e("a-radio",{attrs:{value:0}},[t._v("禁用")])],1)],1),e("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:t.wrapperCol.span,offset:t.labelCol.span}}},[e("a-button",{attrs:{type:"primary","html-type":"submit",loading:t.isBtnLoading}},[t._v("提交")])],1)],1)],1)],1)},i=[],n=(a("fb6a"),a("d3b7"),a("3ca3"),a("ddb0"),a("2ef0")),o=(a("9eb5"),a("e4f5")),s=a("2af9"),l=a("ca00"),c=function(){for(var t=[],e=0;e<=23;e++)t.push({label:("0"+e).slice(-2)+":00",value:e});return t},u={components:{SelectSharpGoods:s["k"]},data:function(){return{isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},form:this.$form.createForm(this),timesOptions:c(),activeTimeId:this.$route.query.activeTimeId,detail:null,goodsList:[]}},created:function(){this.getPageData()},methods:{getPageData:function(){var t=this;t.isLoading=!0,Promise.all([t.getTimeDetail()]).finally((function(){return t.isLoading=!1}))},getTimeDetail:function(){var t=this,e=this;return new Promise((function(a,r){o["c"](e.activeTimeId).then((function(r){e.detail=r.data.detail,e.goodsList=r.data.goodsList,t.setFieldsValue(),a()})).catch(r)}))},setFieldsValue:function(){var t=this.detail,e=this.form,a=this.$nextTick;!Object(l["f"])(e.getFieldsValue())&&a((function(){e.setFieldsValue(Object(n["pick"])(t,["status"]))}))},handleSubmit:function(t){t.preventDefault();var e=this.form.validateFields,a=this.onFormSubmit;e((function(t,e){!t&&a(e)}))},onFormSubmit:function(t){var e=this,a=this;a.isLoading=!0,a.isBtnLoading=!0,o["d"](a.activeTimeId,{form:t}).then((function(t){a.$message.success(t.message,1.5),setTimeout((function(){e.$router.push({path:"./index",query:{activeId:a.detail.active_id}})}),1200)})).catch((function(){return a.isBtnLoading=!1})).finally((function(t){return a.isLoading=!1}))}}},d=u,p=(a("4ab4"),a("2877")),f=Object(p["a"])(d,r,i,!1,null,"9bbe13d4",null);e["default"]=f.exports},"5a5c":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"card-title"},[t._v(t._s(t.$route.meta.title))]),e("a-spin",{attrs:{spinning:t.isLoading}},[e("a-form",{attrs:{form:t.form},on:{submit:t.handleSubmit}},[e("a-form-item",{attrs:{label:"未支付订单",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["order.orderClose",{initialValue:1,rules:[{required:!0,message:"请输入分钟数"}]}],expression:"['order.orderClose', { initialValue: 1, rules: [{ required: true, message: '请输入分钟数' }] }]"}],attrs:{min:0,precision:0}}),e("span",{staticClass:"ml-5"},[t._v("分钟后自动关闭")]),e("p",{staticClass:"form-item-help"},[e("small",[t._v("注：秒杀订单下单未付款，n分钟后自动关闭，设置0则不自动关闭")])])],1),t.$module("apps-dealer")?e("a-form-item",{staticClass:"mt-30",attrs:{label:"是否参与分销",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["isDealer",{rules:[{required:!0}]}],expression:"['isDealer', { rules: [{ required: true }] }]"}]},[e("a-radio",{attrs:{value:!0}},[t._v("开启")]),e("a-radio",{attrs:{value:!1}},[t._v("关闭")])],1),e("div",{staticClass:"form-item-help"},[e("small",[t._v("注：整点秒杀订单是否参与分销（需开启分销功能）")])])],1):t._e(),e("a-form-item",{attrs:{wrapperCol:{span:t.wrapperCol.span,offset:t.labelCol.span}}},[e("a-button",{attrs:{type:"primary","html-type":"submit"}},[t._v("提交")])],1)],1)],1)],1)},i=[],n=(a("d3b7"),a("88bc")),o=a.n(n),s=a("ca00"),l=a("5530"),c=a("b775"),u={detail:"/sharp.setting/detail",update:"/sharp.setting/update"};function d(t){return Object(c["b"])({url:u.detail,method:"get",params:{key:t}})}function p(t,e){return Object(c["b"])({url:u.update,method:"post",data:Object(l["a"])({key:t},e)})}var f={data:function(){return{labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),key:"basic",record:{}}},created:function(){this.getDetail()},methods:{getDetail:function(){var t=this;this.isLoading=!0,d(this.key).then((function(e){t.record=e.data.detail,t.setFieldsValue()})).finally((function(){return t.isLoading=!1}))},setFieldsValue:function(){var t=this.record,e=this.$nextTick,a=this.form;!Object(s["f"])(a.getFieldsValue())&&e((function(){a.setFieldsValue(o()(t,["isDealer","order"]))}))},handleSubmit:function(t){var e=this;t.preventDefault();var a=this.form.validateFields;a((function(t,a){!t&&e.onFormSubmit(a)}))},onFormSubmit:function(t){var e=this;this.isLoading=!0,p(this.key,{form:t}).then((function(t){return e.$message.success(t.message,1.5)})).finally((function(){return e.isLoading=!1}))}}},m=f,h=(a("bfba"),a("2877")),v=Object(h["a"])(m,r,i,!1,null,"3c797252",null);e["default"]=v.exports},"66bc":function(t,e,a){},"6e4f":function(t,e,a){"use strict";a("d61d")},"88bc":function(t,e,a){(function(e){var a=1/0,r=9007199254740991,i="[object Arguments]",n="[object Function]",o="[object GeneratorFunction]",s="[object Symbol]",l="object"==typeof e&&e&&e.Object===Object&&e,c="object"==typeof self&&self&&self.Object===Object&&self,u=l||c||Function("return this")();function d(t,e,a){switch(a.length){case 0:return t.call(e);case 1:return t.call(e,a[0]);case 2:return t.call(e,a[0],a[1]);case 3:return t.call(e,a[0],a[1],a[2])}return t.apply(e,a)}function p(t,e){var a=-1,r=t?t.length:0,i=Array(r);while(++a<r)i[a]=e(t[a],a,t);return i}function f(t,e){var a=-1,r=e.length,i=t.length;while(++a<r)t[i+a]=e[a];return t}var m=Object.prototype,h=m.hasOwnProperty,v=m.toString,b=u.Symbol,g=m.propertyIsEnumerable,_=b?b.isConcatSpreadable:void 0,y=Math.max;function k(t,e,a,r,i){var n=-1,o=t.length;a||(a=L),i||(i=[]);while(++n<o){var s=t[n];e>0&&a(s)?e>1?k(s,e-1,a,r,i):f(i,s):r||(i[i.length]=s)}return i}function w(t,e){return t=Object(t),C(t,e,(function(e,a){return a in t}))}function C(t,e,a){var r=-1,i=e.length,n={};while(++r<i){var o=e[r],s=t[o];a(s,o)&&(n[o]=s)}return n}function S(t,e){return e=y(void 0===e?t.length-1:e,0),function(){var a=arguments,r=-1,i=y(a.length-e,0),n=Array(i);while(++r<i)n[r]=a[e+r];r=-1;var o=Array(e+1);while(++r<e)o[r]=a[r];return o[e]=n,d(t,this,o)}}function L(t){return j(t)||O(t)||!!(_&&t&&t[_])}function x(t){if("string"==typeof t||G(t))return t;var e=t+"";return"0"==e&&1/t==-a?"-0":e}function O(t){return I(t)&&h.call(t,"callee")&&(!g.call(t,"callee")||v.call(t)==i)}var j=Array.isArray;function D(t){return null!=t&&$(t.length)&&!q(t)}function I(t){return T(t)&&D(t)}function q(t){var e=F(t)?v.call(t):"";return e==n||e==o}function $(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=r}function F(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function T(t){return!!t&&"object"==typeof t}function G(t){return"symbol"==typeof t||T(t)&&v.call(t)==s}var V=S((function(t,e){return null==t?{}:w(t,p(k(e,1),x))}));t.exports=V}).call(this,a("c8ba"))},"90a7":function(t,e,a){},"9a9d":function(t,e,a){"use strict";a.r(e);a("ac1f"),a("841c");var r=function(){var t=this,e=t._self._c;return e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"card-title"},[t._v(t._s(t.$route.meta.title))]),e("div",{staticClass:"table-operator"},[e("a-row",[e("a-col",{attrs:{span:5}},[t.$auth("/apps/sharp/goods/create")?e("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:function(e){return t.handleAdd()}}},[t._v("新增")]):t._e()],1),e("a-col",{staticClass:"flex flex-x-end",attrs:{span:11,offset:8}},[e("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px"},attrs:{placeholder:"请输入商品名称"},on:{search:t.onSearch},model:{value:t.queryParam.search,callback:function(e){t.$set(t.queryParam,"search",e)},expression:"queryParam.search"}})],1)],1)],1),e("s-table",{ref:"table",attrs:{rowKey:"sharp_goods_id",loading:t.isLoading,columns:t.columns,data:t.loadData,pageSize:15,scroll:{x:1450}},scopedSlots:t._u([{key:"goods",fn:function(t){return[e("GoodsItem",{attrs:{data:{image:t.goods_image,imageAlt:"商品图片",title:t.goods_name,subtitle:"¥".concat(t.goods_price_min)},subTitleColor:!0}})]}},{key:"seckill_price_min",fn:function(a){return[e("p",{staticClass:"c-p"},[t._v("¥"+t._s(a))])]}},{key:"status",fn:function(a){return[e("a-tag",{attrs:{color:a?"green":"orange"}},[t._v(t._s(a?"上架":"下架"))])]}},{key:"action",fn:function(a,r){return e("span",{staticClass:"actions"},[t.$auth("/apps/sharp/goods/update")?e("a",{on:{click:function(e){return t.handleEdit(r)}}},[t._v("编辑")]):t._e(),e("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(e){return t.handleDelete(r)}}},[t._v("删除")])])}}])})],1)},i=[],n=a("5530"),o=(a("d3b7"),a("42af")),s=a("ab09"),l={name:"Index",components:{STable:s["b"],GoodsItem:s["a"]},data:function(){var t=this;return{queryParam:{search:""},isLoading:!1,columns:[{title:"秒杀商品ID",dataIndex:"sharp_goods_id"},{title:"商品信息",dataIndex:"goods",width:"320px",scopedSlots:{customRender:"goods"}},{title:"秒杀价格",dataIndex:"seckill_price_min",scopedSlots:{customRender:"seckill_price_min"}},{title:"限购数量(件)",dataIndex:"limit_num",scopedSlots:{customRender:"limit_num"}},{title:"累积销量(件)",dataIndex:"total_sales"},{title:"库存总量",dataIndex:"seckill_stock"},{title:"商品状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"创建时间",width:"180px",dataIndex:"create_time"},{title:"操作",width:"150px",fixed:"right",scopedSlots:{customRender:"action"}}],loadData:function(e){return o["e"](Object(n["a"])(Object(n["a"])({},e),t.queryParam)).then((function(t){return t.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$router.push("./create")},handleEdit:function(t){this.$router.push({path:"./update",query:{sharpGoodsId:t.sharp_goods_id}})},handleDelete:function(t){var e=this,a=e.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return o["b"](t.sharp_goods_id).then((function(t){e.$message.success(t.message,1.5),e.handleRefresh()})).finally((function(t){return a.destroy()}))}})},handleRefresh:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(t)},onSearch:function(){this.handleRefresh(!0)}}},c=l,u=a("2877"),d=Object(u["a"])(c,r,i,!1,null,null,null);e["default"]=d.exports},"9c14":function(t,e,a){},"9eb5":function(t,e,a){"use strict";a.d(e,"d",(function(){return o})),a.d(e,"c",(function(){return s})),a.d(e,"e",(function(){return l})),a.d(e,"a",(function(){return c})),a.d(e,"b",(function(){return u}));var r=a("5530"),i=a("b775"),n={list:"/sharp.active/list",detail:"/sharp.active/detail",state:"/sharp.active/state",add:"/sharp.active/add",delete:"/sharp.active/delete"};function o(t){return Object(i["b"])({url:n.list,method:"get",params:t})}function s(t,e){return Object(i["b"])({url:n.detail,method:"get",params:Object(r["a"])({activeId:t},e)})}function l(t,e){return Object(i["b"])({url:n.state,method:"post",data:{activeId:t,state:e}})}function c(t){return Object(i["b"])({url:n.add,method:"post",data:t})}function u(t,e){return Object(i["b"])({url:n.delete,method:"post",data:Object(r["a"])({activeId:t},e)})}},aafc:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"card-title"},[t._v(t._s(t.$route.meta.title))]),e("a-spin",{attrs:{spinning:t.isLoading}},[e("a-form",{attrs:{form:t.form},on:{submit:t.handleSubmit}},[e("a-form-item",{attrs:{label:"活动日期",labelCol:t.labelCol,wrapperCol:t.wrapperCol,required:""}},[e("span",[t._v(t._s(t.active?t.active.active_date:"--"))])]),e("a-form-item",{attrs:{label:"活动场次",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-checkbox-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["activeTimes",{rules:[{required:!0,message:"请指定活动场次"}]}],expression:"['activeTimes', { rules: [{ required: true, message: '请指定活动场次' }] }]"}],staticClass:"times-checkbox",attrs:{options:t.timesOptions}})],1),e("a-form-item",{attrs:{label:"秒杀商品",labelCol:t.labelCol,wrapperCol:t.wrapperCol,required:""}},[e("SelectSharpGoods",{directives:[{name:"decorator",rawName:"v-decorator",value:["sharpGoodsIds",{rules:[{required:!0,message:"请选择指定的秒杀商品"}]}],expression:"['sharpGoodsIds', { rules: [{ required: true, message: '请选择指定的秒杀商品' }] }]"}],attrs:{multiple:!0}})],1),e("a-form-item",{attrs:{label:"活动状态",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[e("a-radio",{attrs:{value:1}},[t._v("启用")]),e("a-radio",{attrs:{value:0}},[t._v("禁用")])],1)],1),e("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:t.wrapperCol.span,offset:t.labelCol.span}}},[e("a-button",{attrs:{type:"primary","html-type":"submit",loading:t.isBtnLoading}},[t._v("提交")])],1)],1)],1)],1)},i=[],n=(a("fb6a"),a("d3b7"),a("3ca3"),a("ddb0"),a("2ef0"),a("9eb5")),o=a("e4f5"),s=a("2af9"),l=a("ca00"),c=function(){for(var t=[],e=0;e<=23;e++)t.push({label:("0"+e).slice(-2)+":00",value:e});return t},u={components:{SelectSharpGoods:s["k"]},data:function(){return{isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},form:this.$form.createForm(this),timesOptions:c(),activeId:this.$route.query.activeId,active:null}},created:function(){this.getPageData()},methods:{getPageData:function(){var t=this;t.isLoading=!0,Promise.all([t.getActiveDetail(),t.getExistTimes()]).finally((function(){return t.isLoading=!1}))},getActiveDetail:function(){var t=this;return new Promise((function(e,a){n["c"](t.activeId).then((function(a){t.active=a.data.detail,e()})).catch(a)}))},getExistTimes:function(){var t=this;return new Promise((function(e,a){o["e"](t.activeId).then((function(a){var r=a.data.existTimes;for(var i in t.timesOptions){var n=t.timesOptions[i];n.disabled=Object(l["e"])(parseInt(n.value),r)}e()})).catch(a)}))},handleSubmit:function(t){t.preventDefault();var e=this.form.validateFields,a=this.onFormSubmit;e((function(t,e){!t&&a(e)}))},onFormSubmit:function(t){var e=this,a=this;a.isLoading=!0,a.isBtnLoading=!0,o["a"](a.activeId,{form:t}).then((function(t){a.$message.success(t.message,1.5),setTimeout((function(){e.$router.push({path:"./index",query:{activeId:a.activeId}})}),1200)})).catch((function(){return a.isBtnLoading=!1})).finally((function(t){return a.isLoading=!1}))}}},d=u,p=(a("06ac"),a("2877")),f=Object(p["a"])(d,r,i,!1,null,"11743671",null);e["default"]=f.exports},ad82:function(t,e,a){"use strict";a("90a7")},b4bb:function(t,e,a){"use strict";a("66bc")},bfba:function(t,e,a){"use strict";a("9c14")},c7eb:function(t,e,a){"use strict";a.d(e,"a",(function(){return i}));a("a4d3"),a("e01a"),a("d3b7"),a("d28b"),a("3ca3"),a("ddb0"),a("b636"),a("944a"),a("0c47"),a("23dc"),a("3410"),a("159b"),a("b0c0"),a("131a"),a("fb6a");var r=a("53ca");function i(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
i=function(){return t};var t={},e=Object.prototype,a=e.hasOwnProperty,n=Object.defineProperty||function(t,e,a){t[e]=a.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function u(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(I){u=function(t,e,a){return t[e]=a}}function d(t,e,a,r){var i=e&&e.prototype instanceof m?e:m,o=Object.create(i.prototype),s=new O(r||[]);return n(o,"_invoke",{value:C(t,a,s)}),o}function p(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(I){return{type:"throw",arg:I}}}t.wrap=d;var f={};function m(){}function h(){}function v(){}var b={};u(b,s,(function(){return this}));var g=Object.getPrototypeOf,_=g&&g(g(j([])));_&&_!==e&&a.call(_,s)&&(b=_);var y=v.prototype=m.prototype=Object.create(b);function k(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function i(n,o,s,l){var c=p(t[n],t,o);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==Object(r["a"])(d)&&a.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,s,l)}),(function(t){i("throw",t,s,l)})):e.resolve(d).then((function(t){u.value=t,s(u)}),(function(t){return i("throw",t,s,l)}))}l(c.arg)}var o;n(this,"_invoke",{value:function(t,a){function r(){return new e((function(e,r){i(t,a,e,r)}))}return o=o?o.then(r,r):r()}})}function C(t,e,a){var r="suspendedStart";return function(i,n){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw n;return D()}for(a.method=i,a.arg=n;;){var o=a.delegate;if(o){var s=S(o,a);if(s){if(s===f)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===r)throw r="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r="executing";var l=p(t,e,a);if("normal"===l.type){if(r=a.done?"completed":"suspendedYield",l.arg===f)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(r="completed",a.method="throw",a.arg=l.arg)}}}function S(t,e){var a=e.method,r=t.iterator[a];if(void 0===r)return e.delegate=null,"throw"===a&&t.iterator["return"]&&(e.method="return",e.arg=void 0,S(t,e),"throw"===e.method)||"return"!==a&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+a+"' method")),f;var i=p(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,f;var n=i.arg;return n?n.done?(e[t.resultName]=n.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):n:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function L(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(L,this),this.reset(!0)}function j(t){if(t){var e=t[s];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function e(){for(;++r<t.length;)if(a.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:D}}function D(){return{value:void 0,done:!0}}return h.prototype=v,n(y,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:h,configurable:!0}),h.displayName=u(v,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,u(t,c,"GeneratorFunction")),t.prototype=Object.create(y),t},t.awrap=function(t){return{__await:t}},k(w.prototype),u(w.prototype,l,(function(){return this})),t.AsyncIterator=w,t.async=function(e,a,r,i,n){void 0===n&&(n=Promise);var o=new w(d(e,a,r,i),n);return t.isGeneratorFunction(a)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},k(y),u(y,c,"Generator"),u(y,s,(function(){return this})),u(y,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),a=[];for(var r in e)a.push(r);return a.reverse(),function t(){for(;a.length;){var r=a.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=j,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(x),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(a,r){return o.type="throw",o.arg=t,e.next=a,r&&(e.method="next",e.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i],o=n.completion;if("root"===n.tryLoc)return r("end");if(n.tryLoc<=this.prev){var s=a.call(n,"catchLoc"),l=a.call(n,"finallyLoc");if(s&&l){if(this.prev<n.catchLoc)return r(n.catchLoc,!0);if(this.prev<n.finallyLoc)return r(n.finallyLoc)}else if(s){if(this.prev<n.catchLoc)return r(n.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return r(n.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&a.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=t,o.arg=e,n?(this.method="next",this.next=n.finallyLoc,f):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),x(a),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var r=a.completion;if("throw"===r.type){var i=r.arg;x(a)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,a){return this.delegate={iterator:j(t),resultName:e,nextLoc:a},"next"===this.method&&(this.arg=void 0),f}},t}},d61d:function(t,e,a){},e349:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"card-title"},[t._v(t._s(t.$route.meta.title))]),e("div",{staticClass:"table-operator"},[e("a-row",[e("a-col",{attrs:{span:5}},[t.$auth("/apps/sharp/active/create")?e("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:function(e){return t.handleAdd()}}},[t._v("新增会场")]):t._e()],1)],1)],1),e("s-table",{ref:"table",attrs:{rowKey:"active_id",loading:t.isLoading,columns:t.columns,data:t.loadData,pageSize:15},scopedSlots:t._u([{key:"status",fn:function(a,r){return[e("a-tag",{staticClass:"cur-p",attrs:{color:a?"green":"orange"},on:{click:function(e){return t.handleStatus(r,a?0:1)}}},[t._v(t._s(a?"启用":"禁用"))])]}},{key:"action",fn:function(a,r){return e("span",{staticClass:"actions"},[t.$auth("/apps/sharp/active/time/index")?e("a",{on:{click:function(e){return t.handleTime(r)}}},[t._v("场次管理")]):t._e(),e("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(e){return t.handleDelete(r)}}},[t._v("删除")])])}}])})],1)},i=[],n=a("5530"),o=(a("d3b7"),a("9eb5")),s=a("ab09"),l={name:"Index",components:{STable:s["b"]},data:function(){var t=this;return{queryParam:{},isLoading:!1,columns:[{title:"会场ID",dataIndex:"active_id"},{title:"活动日期",dataIndex:"active_date"},{title:"场次数量",dataIndex:"activeTimeCount"},{title:"活动状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"创建时间",dataIndex:"create_time"},{title:"操作",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(e){return o["d"](Object(n["a"])(Object(n["a"])({},e),t.queryParam)).then((function(t){return t.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$router.push("./create")},handleTime:function(t){this.$router.push({path:"./time/index",query:{activeId:t.active_id}})},handleStatus:function(t,e){var a=this,r=e?"启用":"禁用",i=this.$confirm({title:"您确定要".concat(r,"该活动吗?"),onOk:function(){return o["e"](t.active_id,e).then((function(t){a.$message.success(t.message,1.5),a.handleRefresh()})).finally((function(){return i.destroy()}))}})},handleDelete:function(t){var e=this,a=e.$confirm({title:"您确定要删除该记录吗?",onOk:function(){return o["b"](t.active_id).then((function(t){e.$message.success(t.message,1.5),e.handleRefresh()})).finally((function(t){return a.destroy()}))}})},handleRefresh:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(t)}}},c=l,u=a("2877"),d=Object(u["a"])(c,r,i,!1,null,null,null);e["default"]=d.exports},e4f5:function(t,e,a){"use strict";a.d(e,"f",(function(){return o})),a.d(e,"e",(function(){return s})),a.d(e,"c",(function(){return l})),a.d(e,"g",(function(){return c})),a.d(e,"a",(function(){return u})),a.d(e,"d",(function(){return d})),a.d(e,"b",(function(){return p}));var r=a("5530"),i=a("b775"),n={list:"/sharp.activeTime/list",existTimes:"/sharp.activeTime/existTimes",detail:"/sharp.activeTime/detail",state:"/sharp.activeTime/state",add:"/sharp.activeTime/add",edit:"/sharp.activeTime/edit",delete:"/sharp.activeTime/delete"};function o(t,e){return Object(i["b"])({url:n.list,method:"get",params:Object(r["a"])({activeId:t},e)})}function s(t,e){return Object(i["b"])({url:n.existTimes,method:"get",params:Object(r["a"])({activeId:t},e)})}function l(t,e){return Object(i["b"])({url:n.detail,method:"get",params:Object(r["a"])({activeTimeId:t},e)})}function c(t,e){return Object(i["b"])({url:n.state,method:"post",data:{activeTimeId:t,state:e}})}function u(t,e){return Object(i["b"])({url:n.add,method:"post",data:Object(r["a"])({activeId:t},e)})}function d(t,e){return Object(i["b"])({url:n.edit,method:"post",data:Object(r["a"])({activeTimeId:t},e)})}function p(t,e){return Object(i["b"])({url:n.delete,method:"post",data:Object(r["a"])({activeTimeId:t},e)})}},f674:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"card-title"},[t._v(t._s(t.$route.meta.title))]),e("a-spin",{attrs:{spinning:t.isLoading}},[e("a-form",{attrs:{form:t.form},on:{submit:t.handleSubmit}},[e("a-form-item",{attrs:{label:"商品信息",labelCol:t.labelCol,wrapperCol:t.wrapperCol,required:""}},[t.goods?e("GoodsItem",{attrs:{data:{image:t.goods.goods_image,imageAlt:"商品图片",title:t.goods.goods_name,subtitle:"¥".concat(t.goods.goods_price_min)},subTitleColor:!0}}):t._e()],1),t.goods?e("a-form-item",{attrs:{label:"商品编码",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("span",[t._v(t._s(t.goods.goods_no?t.goods.goods_no:"-"))])]):t._e(),20==t.record.spec_type&&t.goods&&t.record?e("div",[e("MultiSpec",{ref:"MultiSpec",attrs:{defaultSpecList:t.record.specList,defaultSkuList:t.record.skuList}})],1):t._e(),e("div",{directives:[{name:"show",rawName:"v-show",value:10==t.record.spec_type&&t.goods&&t.record,expression:"record.spec_type == 10 &&  goods && record"}]},[e("a-form-item",{attrs:{label:"秒杀价格",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["seckill_price",{rules:[{required:!0,message:"请输入秒杀价格"}]}],expression:"['seckill_price', { rules: [{ required: true, message: '请输入秒杀价格' }] }]"}],attrs:{min:.01,precision:2}}),e("span",{staticClass:"ml-5"},[t._v("元")])],1),e("a-form-item",{attrs:{label:"秒杀库存数量",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["seckill_stock",{rules:[{required:!0,message:"请输入秒杀库存数量"}]}],expression:"['seckill_stock', { rules: [{ required: true, message: '请输入秒杀库存数量' }] }]"}],attrs:{min:0,precision:0}}),e("span",{staticClass:"ml-5"},[t._v("件")]),e("p",{staticClass:"form-item-help"},[e("small",[t._v("注：秒杀库存为独立库存，与主商品库存不同步")])])],1)],1),e("a-form-item",{attrs:{label:"限购数量",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["limit_num",{rules:[{required:!0,message:"请输入限购数量"}]}],expression:"['limit_num', { rules: [{ required: true, message: '请输入限购数量' }] }]"}],attrs:{min:0,precision:0}}),e("span",{staticClass:"ml-5"},[t._v("件")]),e("p",{staticClass:"form-item-help"},[e("small",[t._v("注：每人限制购买的数量，如果填写0则不限购")])])],1),e("a-form-item",{attrs:{label:"库存计算方式",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["deduct_stock_type",{initialValue:10,rules:[{required:!0}]}],expression:"['deduct_stock_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[e("a-radio",{attrs:{value:10}},[t._v("下单减库存")]),e("a-radio",{attrs:{value:20,disabled:""}},[t._v("付款减库存")])],1),e("p",{staticClass:"form-item-help"},[e("small",[t._v("注：秒杀商品为防止超卖风险仅支持下单减库存")])])],1),e("a-form-item",{attrs:{label:"商品状态",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[e("a-radio",{attrs:{value:1}},[t._v("上架")]),e("a-radio",{attrs:{value:0}},[t._v("下架")])],1)],1),e("a-form-item",{attrs:{label:"排序",labelCol:t.labelCol,wrapperCol:t.wrapperCol,extra:"数字越小越靠前"}},[e("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1),e("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:t.wrapperCol.span,offset:t.labelCol.span}}},[e("a-button",{attrs:{type:"primary","html-type":"submit",loading:t.isBtnLoading}},[t._v("提交")])],1)],1)],1)],1)},i=[],n=a("5530"),o=a("c7eb"),s=a("1da1"),l=(a("d3b7"),a("2ef0")),c=a("42af"),u=a("d084"),d=a("ca00"),p=a("ab09"),f=a("1bdd"),m={components:{GoodsItem:p["a"],MultiSpec:f["a"]},data:function(){return{isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},form:this.$form.createForm(this),sharpGoodsId:null,record:null,goods:null}},created:function(){var t=this;return Object(s["a"])(Object(o["a"])().mark((function e(){return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.sharpGoodsId=t.$route.query.sharpGoodsId,t.isLoading=!0,e.next=4,t.getDetail();case 4:return e.next=6,t.getGoodsInfo();case 6:t.isLoading=!1,t.setFieldsValue();case 8:case"end":return e.stop()}}),e)})))()},methods:{getDetail:function(){var t=this;return Object(s["a"])(Object(o["a"])().mark((function e(){var a;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.sharpGoodsId,e.next=3,c["c"]({sharpGoodsId:a}).then((function(e){return t.record=e.data.detail}));case 3:case"end":return e.stop()}}),e)})))()},getGoodsInfo:function(){var t=this;return Object(s["a"])(Object(o["a"])().mark((function e(){var a;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.record,e.next=3,u["b"](a.goods_id).then((function(e){return t.goods=e.data.detail}));case 3:case"end":return e.stop()}}),e)})))()},setFieldsValue:function(){this.record;var t=this.form,e=this.$nextTick,a=this.getFieldsValue;!Object(d["f"])(t.getFieldsValue())&&e((function(){t.setFieldsValue(a())}))},getFieldsValue:function(){var t=this.record,e=(this.goods,Object(l["pick"])(t,["deduct_stock_type","limit_num","sort","status"])),a=Object(l["pick"])(t.skuList[0],["seckill_price","seckill_stock"]);return Object(n["a"])(Object(n["a"])({},e),a)},handleSubmit:function(t){var e=this;t.preventDefault();var a=this.record,r=this.form.validateFields,i=this.onFormSubmit;r((function(t,r){if(t)return!1;if(20===a.spec_type){var n=e.$refs.MultiSpec;if(!n.verifyForm())return!1;r.specData=n.getFromSpecData()}i(r)}))},onFormSubmit:function(t){var e=this;this.isLoading=!0,this.isBtnLoading=!0,c["d"](this.sharpGoodsId,{form:t}).then((function(t){e.$message.success(t.message,1.5),setTimeout((function(){e.$router.push("./index")}),1200)})).catch((function(){return e.isBtnLoading=!1})).finally((function(){return e.isLoading=!1}))}}},h=m,v=(a("ad82"),a("2877")),b=Object(v["a"])(h,r,i,!1,null,"1f4f7daa",null);e["default"]=b.exports}}]);