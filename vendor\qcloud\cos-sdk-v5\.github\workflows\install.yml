name: Support Multiple Versions

on:
  push:
  pull_request:

jobs:
  install:
    name: PHP${{ matrix.php-version }} Test Install
    runs-on: "${{ matrix.os }}"
    strategy:
      matrix:
        os: [ubuntu-latest]
        php-version: ['5.6', '7.0', '7.1', '7.2', '7.3', '7.4', '8.0', '8.1']
      max-parallel: 10
      fail-fast: false
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          coverage: none

      - name: Setup Dependencies
        run:
          composer install -o
