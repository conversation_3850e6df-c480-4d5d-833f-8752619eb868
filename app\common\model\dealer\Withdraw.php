<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\common\model\dealer;

use cores\BaseModel;
use think\model\relation\BelongsTo;

/**
 * 分销商提现明细模型
 * Class Withdraw
 * @package app\common\model\dealer
 */
class Withdraw extends BaseModel
{
    // 定义表名
    protected $name = 'dealer_withdraw';

    // 定义主键
    protected $pk = 'id';

    // 追加字段
    protected $append = ['source', 'status_text'];

    public function getStatusTextAttr($value, $data): string
    {

        switch ($data['apply_status']) {
            case 10:
                $source = '待审核';
                break;
            case 20:
                $source = '审核通过';
                break;
            case 30:
                $source = '驳回';
                break;
            case 40:
                $source = '已打款';
                break;
            case 50:
                $source = '已完成';
                break;
            case 60:
                $source = '未收款';
                break;

            default:
                // code...
                break;
        }
        return $source;
    }

    public function getSourceAttr($value, $data): string
    {

        switch ($data['type']) {
            case 1:
                $source = '佣金';
                break;
            case 2:
                $source = '余额';
                break;
            case 3:
                $source = '积分兑现';
                break;

            default:
                // code...
                break;
        }
        return $source;
    }

    /**
     * 关联分销商用户表
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        $module = self::getCalledModule();
        return $this->belongsTo("app\\{$module}\\model\\User");
    }

    /**
     * 提现详情
     * @param int $id
     * @return static|array|null
     */
    public static function detail(int $id)
    {
        return self::get($id);
    }
}
