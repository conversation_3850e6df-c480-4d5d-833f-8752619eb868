<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\admin\controller\cloud;

use think\response\Json;
use app\admin\controller\Controller;
use app\admin\service\cloud\Authorize as AuthorizeService;
use cores\exception\BaseException;

/**
 * 授权信息
 * Class Authorize
 * @package app\admin\controller
 */
class Authorize extends Controller
{
    /**
     * 获取授权用户信息
     * @return Json
     * @throws BaseException
     */
    public function info(): Json
    {
        $service = new AuthorizeService;
        $authorize = $service->getInfo();
        return $this->renderSuccess(compact('authorize'));
    }
}
