<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\goods;

use app\common\enum\EnumBasics;

/**
 * 枚举类：商品来源
 * Class GoodsSource
 * @package app\common\enum\goods
 */
class GoodsSource extends EnumBasics
{
    // 普通商品
    const MAIN = 10;

    // 砍价商品
    const BARGAIN = 20;

    // 秒杀商品
    const SHARP = 30;

    // 拼团商品
    const GROUPON = 40;

    /**
     * 获取枚举数据
     * @return array
     */
    public static function data(): array
    {
        return [
            self::MAIN => [
                'name' => '普通商品',
                'value' => self::MAIN,
            ],
            self::BARGAIN => [
                'name' => '砍价商品',
                'value' => self::BARGAIN,
            ],
            self::SHARP => [
                'name' => '秒杀商品',
                'value' => self::SHARP,
            ],
            self::GROUPON => [
                'name' => '拼团商品',
                'value' => self::GROUPON,
            ]
        ];
    }
}