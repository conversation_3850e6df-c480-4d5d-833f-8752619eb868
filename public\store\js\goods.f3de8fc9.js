(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["goods"],{"03eb":function(e,a,t){},"10ce":function(e,a,t){"use strict";t("3bf5")},1457:function(e,a,t){"use strict";t("a53d")},1788:function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-row",{staticClass:"row-item-search"},[a("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[a("a-form-item",{attrs:{label:"商品名称"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goodsName"],expression:"['goodsName']"}],attrs:{placeholder:"请输入商品名称"}})],1),a("a-form-item",{attrs:{label:"商品编码"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goodsNo"],expression:"['goodsNo']"}],attrs:{placeholder:"请输入商品编码"}})],1),a("a-form-item",{attrs:{label:"商品分类"}},[a("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["categoryId",{initialValue:0}],expression:"['categoryId', { initialValue: 0 }]"}],attrs:{treeData:e.categoryListTree,dropdownStyle:{maxHeight:"500px",overflow:"auto"},allowClear:""}})],1),a("a-form-item",{staticClass:"search-btn"},[a("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1),a("div",{staticClass:"row-item-tab clearfix"},[a("div",{staticClass:"tab-list fl-l"},[a("a-radio-group",{attrs:{defaultValue:e.queryParam.listType},on:{change:e.handleTabs}},[a("a-radio-button",{attrs:{value:"all"}},[e._v("全部")]),a("a-radio-button",{attrs:{value:"on_sale"}},[e._v("出售中")]),a("a-radio-button",{attrs:{value:"off_sale"}},[e._v("已下架")]),a("a-radio-button",{attrs:{value:"sold_out"}},[e._v("已售罄")])],1)],1),e.$auth("/goods/create")?a("a-button",{staticClass:"fl-l",attrs:{type:"primary",icon:"plus"},on:{click:function(a){return e.handleCreate()}}},[e._v("创建商品")]):e._e(),e.$module("goods-import")&&e.$auth("/goods/import/batch")?a("a-button",{staticClass:"fl-l",attrs:{icon:"arrow-up"},on:{click:function(a){return e.handleImport()}}},[e._v("批量导入")]):e._e(),e.selectedRowKeys.length?a("div",{staticClass:"button-group"},[a("a-button-group",{staticClass:"ml-10"},[a("a-button",{directives:[{name:"action",rawName:"v-action:status",arg:"status"}],attrs:{icon:"arrow-up"},on:{click:function(a){return e.handleUpdateStatus(e.selectedRowKeys,!0)}}},[e._v("上架")]),a("a-button",{directives:[{name:"action",rawName:"v-action:status",arg:"status"}],attrs:{icon:"arrow-down"},on:{click:function(a){return e.handleUpdateStatus(e.selectedRowKeys,!1)}}},[e._v("下架")]),a("a-button",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],attrs:{icon:"delete"},on:{click:function(a){return e.handleDelete(e.selectedRowKeys)}}},[e._v("删除")])],1)],1):e._e()],1)],1),a("s-table",{ref:"table",attrs:{rowKey:"goods_id",loading:e.isLoading,columns:e.columns,data:e.loadData,rowSelection:e.rowSelection,pageSize:15,scroll:{x:1450}},scopedSlots:e._u([{key:"goods_image",fn:function(e){return a("span",{},[a("a",{attrs:{title:"点击查看原图",href:e,target:"_blank"}},[a("img",{attrs:{width:"50",height:"50",src:e,alt:"商品图片"}})])])}},{key:"goods_name",fn:function(t){return a("span",{},[a("p",{staticClass:"twoline-hide",staticStyle:{width:"270px"}},[e._v(e._s(t))])])}},{key:"status",fn:function(t,r){return a("span",{},[a("a-tag",{staticClass:"cur-p",attrs:{color:10==t?"green":"red"},on:{click:function(a){return e.handleUpdateStatus([r.goods_id],10!=t)}}},[e._v(e._s(10==t?"上架":"下架"))])],1)}},{key:"action",fn:function(t,r){return a("div",{staticClass:"actions"},[e.$auth("/goods/update")?a("router-link",{attrs:{to:{path:"/goods/update",query:{goodsId:r.goods_id}}}},[e._v("编辑")]):e._e(),e.$module("goods-copy")&&e.$auth("/goods/copy")?a("router-link",{attrs:{to:{path:"/goods/copy",query:{goodsId:r.goods_id}}}},[e._v("复制")]):e._e(),a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete([r.goods_id])}}},[e._v("删除")])],1)}}])})],1)},i=[],o=t("5530"),s=(t("d3b7"),t("d084")),l=t("2af9"),n=t("8243"),d=[{title:"商品ID",dataIndex:"goods_id"},{title:"商品图片",dataIndex:"goods_image",scopedSlots:{customRender:"goods_image"}},{title:"商品名称",dataIndex:"goods_name",width:"302px",scopedSlots:{customRender:"goods_name"}},{title:"商品价格",dataIndex:"goods_price_min",scopedSlots:{customRender:"goods_price_min"}},{title:"总销量",dataIndex:"sales_actual"},{title:"库存总量",dataIndex:"stock_total"},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",width:"180px",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"150px",fixed:"right",scopedSlots:{customRender:"action"}}],u={name:"Index",components:{ContentHeader:l["a"],STable:l["d"]},data:function(){var e=this;return{searchForm:this.$form.createForm(this),categoryListTree:[],queryParam:{listType:"all"},isLoading:!1,columns:d,selectedRowKeys:[],loadData:function(a){return s["f"](Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){this.$route.query.listType&&(this.queryParam.listType=this.$route.query.listType),this.getCategoryList()},computed:{rowSelection:function(){return{selectedRowKeys:this.selectedRowKeys,onChange:this.onSelectChange}}},methods:{onSelectChange:function(e){this.selectedRowKeys=e},handleTabs:function(e){this.queryParam.listType=e.target.value,this.handleRefresh(!0)},handleSearch:function(e){var a=this;e.preventDefault(),this.searchForm.validateFields((function(e,t){e||(a.queryParam=Object(o["a"])(Object(o["a"])({},a.queryParam),t),a.handleRefresh(!0))}))},handleReset:function(){this.searchForm.resetFields()},getCategoryList:function(){var e=this;this.isLoading=!0,n["a"].getListFromScreen().then((function(a){e.categoryListTree=a})).finally((function(){return e.isLoading=!1}))},handleUpdateStatus:function(e){var a=this,t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(!this.$auth("/goods/index.status"))return!1;this.isLoading=!0,s["h"]({goodsIds:e,state:t}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){a.isLoading=!1}))},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return s["c"]({goodsIds:e}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleCreate:function(){this.$router.push("/goods/create")},handleImport:function(){this.$router.push("/goods/import/batch")},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.selectedRowKeys=[],this.$refs.table.refresh(e)}}},c=u,m=(t("dd3e"),t("2877")),p=Object(m["a"])(c,r,i,!1,null,"5cdd2d32",null);a["default"]=p.exports},1827:function(e,a,t){"use strict";t.d(a,"c",(function(){return o})),t.d(a,"a",(function(){return s})),t.d(a,"b",(function(){return l}));var r=t("b775"),i={list:"/goods.import/list",batch:"/goods.import/batch",delete:"/goods.import/delete"};function o(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function s(e){return Object(r["b"])({url:i.batch,method:"post",data:e})}function l(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},"19d3":function(e,a,t){"use strict";t.d(a,"e",(function(){return o})),t.d(a,"b",(function(){return s})),t.d(a,"a",(function(){return l})),t.d(a,"d",(function(){return n})),t.d(a,"c",(function(){return d}));var r=t("b775"),i={list:"/goods.service/list",all:"/goods.service/all",add:"/goods.service/add",edit:"/goods.service/edit",delete:"/goods.service/delete"};function o(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function s(e){return Object(r["b"])({url:i.all,method:"get",params:e})}function l(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function n(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function d(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},"22a0":function(e,a,t){},2742:function(e,a,t){"use strict";t("fe55")},3686:function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e.isLoading?e._e():a("a-table",{attrs:{rowKey:"category_id",columns:e.columns,dataSource:e.categoryList,defaultExpandAllRows:!0,expandIconColumnIndex:1,pagination:!1,loading:e.isLoading},scopedSlots:e._u([{key:"status",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:t?"green":""}},[e._v(e._s(t?"显示":"隐藏"))])],1)}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(a){return e.handleEdit(r)}}},[e._v("编辑")]),a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete(r)}}},[e._v("删除")])])}}],null,!1,1032135766)}),a("AddForm",{ref:"AddForm",attrs:{categoryList:e.categoryList},on:{handleSubmit:e.handleRefresh}}),a("EditForm",{ref:"EditForm",attrs:{categoryList:e.categoryList},on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=(t("d3b7"),t("2f71")),s=t("2af9"),l=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:"新增商品分类",width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"分类名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),a("a-form-item",{attrs:{label:"上级分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["parent_id",{initialValue:0}],expression:"['parent_id', { initialValue: 0}]"}],attrs:{treeData:e.categoryListTree,dropdownStyle:{maxHeight:"400px",overflow:"auto"},allowClear:""}})],1),a("a-form-item",{attrs:{label:"分类图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["image_id"],expression:"['image_id']"}]})],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("显示")]),a("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},n=[],d=(t("99af"),t("8243")),u={components:{SelectImage:s["h"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),categoryListTree:[]}},methods:{add:function(){this.visible=!0,this.getCategoryList()},getCategoryList:function(){var e=this.categoryList,a=d["a"].formatTreeData(e);this.categoryListTree=[{title:"顶级分类",key:0,value:0}].concat(a)},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){e||a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,o["a"]({form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(e){a.confirmLoading=!1}))}}},c=u,m=t("2877"),p=Object(m["a"])(c,l,n,!1,null,null,null),v=p.exports,f=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:"编辑商品分类",width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"分类名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),a("a-form-item",{attrs:{label:"上级分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["parent_id"],expression:"['parent_id']"}],attrs:{treeData:e.categoryListTree,dropdownStyle:{maxHeight:"400px",overflow:"auto"},allowClear:""}})],1),a("a-form-item",{attrs:{label:"分类图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["image_id"],expression:"['image_id']"}],attrs:{defaultList:e.record.image?[e.record.image]:[]}})],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("显示")]),a("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},g=[],h=t("2ef0"),_=t.n(h),b={components:{SelectImage:s["h"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),categoryListTree:[],record:{}}},methods:{edit:function(e){this.visible=!0,this.record=e,this.getCategoryList(),this.setFieldsValue()},setFieldsValue:function(){var e=this.record,a=this.form.setFieldsValue;this.$nextTick((function(){a(_.a.pick(e,["name","parent_id","image_id","status","sort"]))}))},getCategoryList:function(){var e=this.categoryList,a=d["a"].formatTreeData(e);this.categoryListTree=[{title:"顶级分类",key:0,value:0}].concat(a)},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){e||a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,o["c"]({categoryId:this.record["category_id"],form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(e){a.confirmLoading=!1}))}}},C=b,w=Object(m["a"])(C,f,g,!1,null,null,null),y=w.exports,x={name:"Index",components:{STable:s["d"],AddForm:v,EditForm:y},data:function(){return{categoryList:[],queryParam:{},isLoading:!1,columns:[{title:"分类ID",dataIndex:"category_id"},{title:"分类名称",dataIndex:"name"},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}]}},created:function(){this.getCategoryList(!0)},methods:{getCategoryList:function(e){var a=this;e&&(this.isLoading=!0),o["d"]().then((function(e){a.categoryList=e.data.list})).finally((function(){return a.isLoading=!1}))},handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return o["b"]({categoryId:e["category_id"]}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleRefresh:function(){this.getCategoryList()}}},S=x,k=Object(m["a"])(S,r,i,!1,null,null,null);a["default"]=k.exports},"3bf5":function(e,a,t){},"3f8b":function(e,a,t){},"48d0":function(e,a,t){"use strict";t.r(a);t("b0c0");var r,i=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.pageTitle))]),a("div",{staticClass:"table-operator"},[a("a-row",{staticClass:"row-item-search"},[a("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[a("a-form-item",{attrs:{label:"导入状态"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:-1}],expression:"['status', { initialValue: -1 }]"}]},[a("a-select-option",{attrs:{value:-1}},[e._v("全部")]),e._l(e.ImportStatusEnum.data,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.value}},[e._v(e._s(t.name))])}))],2)],1),a("a-form-item",{staticClass:"search-btn"},[a("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1),a("div",{staticClass:"row-item-tab clearfix"},[e.$auth("/goods/import/batch")?a("a-button",{staticClass:"fl-l",attrs:{type:"primary",icon:"arrow-up"},on:{click:function(a){return e.handleImport()}}},[e._v("批量导入")]):e._e()],1)],1),a("s-table",{ref:"table",attrs:{rowKey:"id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"start_time",fn:function(t){return[a("span",[e._v(e._s(t||"--"))])]}},{key:"end_time",fn:function(t){return[a("span",[e._v(e._s(t||"--"))])]}},{key:"status",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:e.ImportStatusColorEnum[t]}},[e._v(e._s(e.ImportStatusEnum[t].name))])],1)}},{key:"action",fn:function(t,r){return a("span",{staticClass:"actions"},[a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete(r)}}},[e._v("删除")]),r.fail_count>0?a("a",{on:{click:function(a){return e.handleFailLog(r)}}},[e._v("失败日志")]):e._e()])}}])}),a("a-modal",{attrs:{title:"失败日志"},on:{ok:function(a){e.visibleFailLog=!1}},model:{value:e.visibleFailLog,callback:function(a){e.visibleFailLog=a},expression:"visibleFailLog"}},[a("div",{staticClass:"modal-content"},e._l(e.failLogContent,(function(t,r){return a("p",{key:r,staticClass:"log-item"},[a("span",{staticClass:"mr-5"},[e._v("序号["+e._s(t.goodsSn)+"]")]),a("span",[e._v(e._s(t.message))])])})),0)])],1)},o=[],s=t("5530"),l=t("ade3"),n=(t("d3b7"),t("1827")),d=t("2af9"),u=t("59aa"),c=(r={},Object(l["a"])(r,u["c"].NORMAL.value,""),Object(l["a"])(r,u["c"].COMPLETED.value,"green"),r),m={name:"Index",components:{STable:d["d"]},data:function(){var e=this;return{searchForm:this.$form.createForm(this),queryParam:{},isLoading:!1,ImportStatusEnum:u["c"],ImportStatusColorEnum:c,columns:[{title:"记录ID",dataIndex:"id"},{title:"导入总数量",dataIndex:"total_count"},{title:"导入成功数量",dataIndex:"success_count"},{title:"导入失败数量",dataIndex:"fail_count"},{title:"开始时间",dataIndex:"start_time",scopedSlots:{customRender:"start_time"}},{title:"结束时间",dataIndex:"end_time",scopedSlots:{customRender:"end_time"}},{title:"导入状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(a){return n["c"](Object(s["a"])(Object(s["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))},visibleFailLog:!1,failLogContent:[]}},created:function(){},methods:{handleImport:function(){this.$router.push("/goods/import/batch")},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return n["b"]({id:e["id"]}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleFailLog:function(e){this.visibleFailLog=!0,this.failLogContent=e.fail_log},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},handleSearch:function(e){var a=this;e.preventDefault(),this.searchForm.validateFields((function(e,t){e||(a.queryParam=Object(s["a"])(Object(s["a"])({},a.queryParam),t),a.handleRefresh(!0))}))}}},p=m,v=(t("861f1"),t("2877")),f=Object(v["a"])(p,i,o,!1,null,"4e067966",null);a["default"]=f.exports},"52e0":function(e,a,t){"use strict";t("3f8b")},"540d":function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[a("span",[e._v(e._s(e.$route.meta.pageTitle))])]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("div",{staticClass:"container"},[a("div",{staticClass:"upload-dragger"},[a("a-upload-dragger",{attrs:{accept:".xls, .xlsx",multiple:!1,fileList:e.fileList,showUploadList:!1,beforeUpload:e.beforeUpload,remove:e.handleRemove}},[a("p",{staticClass:"ant-upload-drag-icon"},[a("a-icon",{attrs:{type:"cloud-upload"}})],1),e.fileList.length?a("div",[a("p",{staticClass:"ant-upload-text"},[a("span",[e._v(e._s(e.fileList[0].name))]),a("a",{staticClass:"ml-10",attrs:{href:"javascript:void(0);"},on:{click:function(a){return a.stopPropagation(),e.handleRemove(e.fileList[0])}}},[e._v("删除")])]),a("a-button",{staticClass:"mt-20",attrs:{type:"primary"},on:{click:function(a){return a.stopPropagation(),e.onFormSubmit.apply(null,arguments)}}},[e._v("立即导入")])],1):a("div",[a("p",{staticClass:"ant-upload-text"},[e._v("点击选择文件，或者将文件拖拽至此区域")]),a("p",{staticClass:"ant-upload-hint"},[e._v("仅支持 .xls, .xlsx 格式的excel文件，限2M以内")])])])],1),a("div",{staticClass:"import-explain"},[a("h2",{staticClass:"title"},[e._v("导入说明")]),a("a-timeline",{staticClass:"timeline"},[a("a-timeline-item",{staticClass:"timeline-item"},[a("p",{staticClass:"name"},[e._v("下载模板")]),a("ul",{staticClass:"content"},[a("li",{staticClass:"content-li"},[a("span",[e._v("批量导入商品需要系统开启队列服务，可在超管后台中查看是否开启")])]),a("li",{staticClass:"content-li"},[a("span",{staticClass:"mr-5"},[e._v("商品导入前需要您用Excel整理需要导入的商品资料，请先")]),a("a",{attrs:{href:"static/template/batch-goods.xlsx",target:"_blank"}},[e._v("下载商品导入模板")])])])]),a("a-timeline-item",{staticClass:"timeline-item"},[a("p",{staticClass:"name"},[e._v("使用模板")]),a("ul",{staticClass:"content"},[a("li",{staticClass:"content-li"},[a("span",[e._v("模板中最多不能超过500个商品，如超过500个商品，请分批导入")])]),a("li",{staticClass:"content-li"},[a("span",[e._v("模板中的字段含义以及填写规则，可查看模板文件中标题栏的标注")])])])]),a("a-timeline-item",{staticClass:"timeline-item"},[a("p",{staticClass:"name"},[e._v("上传图片")]),a("ul",{staticClass:"content"},[a("li",{staticClass:"content-li"},[a("span",[e._v('需提前在后台上传商品图片，然后填写到模板文件的 "商品图片" 列中')])]),a("li",{staticClass:"content-li"},[a("span",{staticClass:"mr-5"},[e._v("通过文件库上传图片，并复制图片ID集")]),a("a",{attrs:{href:"javascript:void(0);"},on:{click:function(a){return e.handleSelectImage()}}},[e._v("点击上传图片")])])])])],1)],1),a("FilesModal",{ref:"FilesModal",attrs:{multiple:!0,maxNum:10,actions:["delete","move","copyIds"]}})],1)])],1)},i=[],o=(t("a434"),t("d3b7"),t("1827")),s=t("fd0d"),l={components:{FilesModal:s["d"]},data:function(){return{isLoading:!1,fileList:[],uploadSizeLimit:"2"}},created:function(){},methods:{handleSelectImage:function(){this.$refs.FilesModal.show()},beforeUpload:function(e){var a=e.size/1024/1024;return a>this.uploadSizeLimit?(this.$message.error("上传的文件大小不能超出".concat(this.uploadSizeLimit,"MB")),!1):(this.fileList=[e],!1)},handleRemove:function(e){var a=this.fileList,t=a.indexOf(e);t>-1&&a.splice(t,1)},onFormSubmit:function(){var e=this,a=this.fileList,t=new FormData;t.append("file",a[0]),this.isLoading=!0,o["a"](t).then((function(a){e.fileList=[],e.$message.success(a.message,1.8),setTimeout((function(){return e.$router.push("/goods/import/list")}),1500)})).finally((function(){return e.isLoading=!1}))}}},n=l,d=(t("10ce"),t("2877")),u=Object(d["a"])(n,r,i,!1,null,"152bc87a",null);a["default"]=u.exports},5633:function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("a-form",{attrs:{form:e.form,selfUpdate:!0},on:{submit:e.handleSubmit}},[a("a-tabs",{attrs:{activeKey:e.tabKey,tabBarStyle:{marginBottom:"30px"}},on:{change:e.handleTabs}},[a("a-tab-pane",{key:0,attrs:{tab:"基本信息"}}),a("a-tab-pane",{key:1,attrs:{tab:"规格/库存"}}),a("a-tab-pane",{key:2,attrs:{tab:"商品详情"}}),a("a-tab-pane",{key:3,attrs:{tab:"更多设置"}})],1),a("div",{staticClass:"tabs-content"},[a("div",{directives:[{name:"show",rawName:"v-show",value:0==e.tabKey,expression:"tabKey == 0"}],staticClass:"tab-pane"},[a("a-form-item",{attrs:{label:"商品类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("GoodsType",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_type",{rules:[{required:!0}]}],expression:"['goods_type', { rules: [{ required: true }] }]"}],attrs:{onlyShowChecked:!0},on:{change:function(a){return e.onForceUpdate(!0)}}})],1),a("a-form-item",{attrs:{label:"商品名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['goods_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入商品名称"}})],1),a("a-form-item",{attrs:{label:"商品分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["categorys",{rules:[{required:!0,message:"请至少选择1个商品分类"}]}],expression:"['categorys', { rules: [{ required: true, message: '请至少选择1个商品分类' }] }]"}],attrs:{placeholder:"请选择商品分类",dropdownStyle:{maxHeight:"500px",overflow:"auto"},treeData:e.formData.categoryList,treeCheckable:"",treeCheckStrictly:"",allowClear:""}}),a("div",{staticClass:"form-item-help"},[a("router-link",{attrs:{target:"_blank",to:{path:"/goods/category/index"}}},[e._v("去新增")]),a("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadCategoryList}},[e._v("刷新")])],1)],1),a("a-form-item",{attrs:{label:"商品图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸：750*750像素, 最多上传10张, 可拖拽图片调整顺序, 第1张将作为商品首图"}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["imagesIds",{rules:[{required:!0,message:"请至少上传1张商品图片"}]}],expression:"['imagesIds', { rules: [{ required: true, message: '请至少上传1张商品图片' }] }]"}],attrs:{multiple:"",maxNum:10,defaultList:e.formData.goods.goods_images}})],1),a("a-form-item",{attrs:{label:"商品编码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_no"],expression:"['goods_no']"}],attrs:{placeholder:"请输入商品编码"}})],1),10==e.form.getFieldValue("goods_type")?a("a-form-item",{attrs:{label:"配送方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_ind_delivery_type",{initialValue:0,rules:[{required:!0}]}],expression:"['is_ind_delivery_type', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate()}}},[a("a-radio",{attrs:{value:0}},[e._v("系统默认")]),a("a-radio",{attrs:{value:1}},[e._v("单独配置")])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_ind_delivery_type"),expression:"form.getFieldValue('is_ind_delivery_type')"}]},[a("a-form-item",{attrs:{extra:"需在 [设置 - 配送方式] 中，开启支持的配送方式才可生效"}},[a("a-checkbox-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["delivery_type",{initialValue:[10,20],rules:[{required:!0,message:"配送方式至少选择一个"}]}],expression:"['delivery_type', { initialValue: [10, 20], rules: [{ required: true, message: '配送方式至少选择一个' }] }]"}],attrs:{options:[{label:"快递配送",value:10},{label:"上门自提",value:20}]}})],1)],1)],1):e._e(),10==e.form.getFieldValue("goods_type")?a("a-form-item",{attrs:{label:"运费模板",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["delivery_id",{rules:[{required:!0,message:"请选择运费模板"}]}],expression:"['delivery_id', { rules: [{ required: true, message: '请选择运费模板' }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请选择运费模板"}},e._l(e.formData.deliveryList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.delivery_id}},[e._v(e._s(t.name))])})),1),a("div",{staticClass:"form-item-help"},[a("router-link",{attrs:{target:"_blank",to:{path:"/setting/delivery/template/create"}}},[e._v("新增模板")]),a("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadDeliveryList}},[e._v("刷新")])],1)],1):e._e(),a("a-form-item",{attrs:{label:"商品状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:10,rules:[{required:!0}]}],expression:"['status', { initialValue: 10, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:10}},[e._v("上架")]),a("a-radio",{attrs:{value:20}},[e._v("下架")])],1)],1),a("a-form-item",{attrs:{label:"商品排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true }] }]"}],attrs:{min:0}})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:1==e.tabKey,expression:"tabKey == 1"}],staticClass:"tab-pane"},[a("a-form-item",{attrs:{label:"规格类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["spec_type",{initialValue:10,rules:[{required:!0}]}],expression:"['spec_type', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate()}}},[a("a-radio",{attrs:{value:10}},[e._v("单规格")]),a("a-radio",{attrs:{value:20}},[e._v("多规格")])],1)],1),20==e.form.getFieldValue("spec_type")?a("div",[a("MultiSpec",{ref:"MultiSpec",attrs:{defaultSpecList:e.formData.goods.specList,defaultSkuList:e.formData.goods.skuList}})],1):e._e(),10==e.form.getFieldValue("spec_type")?a("div",[a("a-form-item",{attrs:{label:"商品价格",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际购买金额，最低0.01"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_price",{rules:[{required:!0,message:"请输入商品价格"}]}],expression:"['goods_price', { rules: [{ required: true, message: '请输入商品价格' }] }]"}],attrs:{min:.01,precision:2}}),a("span",{staticClass:"ml-10"},[e._v("元")])],1),a("a-form-item",{attrs:{label:"划线价",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"划线价仅用于商品页展示"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["line_price"],expression:"['line_price']"}],attrs:{min:0,precision:2}}),a("span",{staticClass:"ml-10"},[e._v("元")])],1),a("a-form-item",{attrs:{label:"当前库存数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际库存数量，为0时用户无法下单"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["stock_num",{initialValue:100,rules:[{required:!0,message:"请输入库存数量"}]}],expression:"['stock_num', { initialValue: 100, rules: [{ required: true, message: '请输入库存数量' }] }]"}],attrs:{min:0,precision:0}}),a("span",{staticClass:"ml-10"},[e._v("件")])],1),a("a-form-item",{attrs:{label:"商品重量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际重量，用于计算运费"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_weight",{initialValue:0,rules:[{required:!0,message:"请输入库存数量"}]}],expression:"['goods_weight', { initialValue: 0, rules: [{ required: true, message: '请输入库存数量' }] }]"}],attrs:{min:0}}),a("span",{staticClass:"ml-10"},[e._v("千克 (Kg)")])],1)],1):e._e(),a("a-form-item",{attrs:{label:"库存计算方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["deduct_stock_type",{initialValue:10,rules:[{required:!0}]}],expression:"['deduct_stock_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:10}},[e._v("下单减库存")]),a("a-radio",{attrs:{value:20}},[e._v("付款减库存")])],1)],1),a("a-form-item",{attrs:{label:"商品限购",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用于限制每人购买该商品的数量"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_restrict",{initialValue:0,rules:[{required:!0}]}],expression:"['is_restrict', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate()}}},[a("a-radio",{attrs:{value:0}},[e._v("关闭")]),a("a-radio",{attrs:{value:1}},[e._v("开启")])],1),e.form.getFieldValue("is_restrict")?a("div",{staticClass:"mt-10"},[a("a-form-item",[a("span",{staticClass:"mr-10"},[e._v("总限购")]),a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_total",{rules:[{required:!0,message:"请输入总限购数量"}]}],expression:"['restrict_total', { rules: [{ required: true, message: '请输入总限购数量' }] }]"}],attrs:{min:1,precision:0}}),a("span",{staticClass:"ml-10"},[e._v("件/人")])],1),a("a-form-item",[a("span",{staticClass:"mr-10"},[e._v("每单限购")]),a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_single",{rules:[{required:!0,message:"请输入每单限购数量"}]}],expression:"['restrict_single', { rules: [{ required: true, message: '请输入每单限购数量' }] }]"}],attrs:{min:1,precision:0}}),a("span",{staticClass:"ml-10"},[e._v("件/人")])],1)],1):e._e()],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:2==e.tabKey,expression:"tabKey == 2"}],staticClass:"tab-pane"},[a("a-form-item",{attrs:{label:"商品详情",labelCol:e.labelCol,wrapperCol:{span:16}}},[a("Ueditor",{directives:[{name:"decorator",rawName:"v-decorator",value:["content",{rules:[{required:!0,message:"商品详情不能为空"}]}],expression:"['content', { rules: [{ required: true, message: '商品详情不能为空' }] }]"}]})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:3==e.tabKey,expression:"tabKey == 3"}],staticClass:"tab-pane"},[a("a-form-item",{attrs:{label:"主图视频",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议视频宽高比19:9，建议时长8-45秒"}},[a("SelectVideo",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_id"],expression:"['video_id']"}],attrs:{multiple:!1,defaultList:e.formData.goods.video?[e.formData.goods.video]:[]}})],1),a("a-form-item",{attrs:{label:"视频封面",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸：750像素*750像素"}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_cover_id"],expression:"['video_cover_id']"}],attrs:{multiple:!1,defaultList:e.formData.goods.videoCover?[e.formData.goods.videoCover]:[]}})],1),a("a-form-item",{attrs:{label:"商品卖点",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"一句话简述，例如：此款商品美观大方 性价比较高 不容错过"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["selling_point"],expression:"['selling_point']"}],attrs:{placeholder:"请输入商品卖点"}})],1),a("a-form-item",{attrs:{label:"服务与承诺",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[e.formData.serviceList?a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["serviceIds"],expression:"['serviceIds']"}],attrs:{mode:"multiple",placeholder:"请选择服务与承诺"}},e._l(e.formData.serviceList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.service_id}},[e._v(e._s(t.name))])})),1):e._e(),a("div",{staticClass:"form-item-help"},[a("router-link",{attrs:{target:"_blank",to:{path:"/goods/service/index"}}},[e._v("去新增")]),a("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadServiceList}},[e._v("刷新")])],1)],1),a("a-form-item",{attrs:{label:"初始销量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端展示的销量 = 初始销量 + 实际销量"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sales_initial",{initialValue:0}],expression:"['sales_initial', { initialValue: 0 }]"}]})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("market-points"),expression:"$module('market-points')"}]},[a("a-divider",{attrs:{orientation:"left"}},[e._v("积分设置")]),a("a-form-item",{attrs:{label:"积分赠送",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后用户购买此商品将获得积分"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_points_gift",{initialValue:1,rules:[{required:!0}]}],expression:"['is_points_gift', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("开启")]),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),a("a-form-item",{attrs:{label:"积分抵扣",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后用户购买此商品可以使用积分进行抵扣"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_points_discount",{initialValue:1,rules:[{required:!0}]}],expression:"['is_points_discount', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("开启")]),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("user-grade"),expression:"$module('user-grade')"}]},[a("a-divider",{attrs:{orientation:"left"}},[e._v("会员折扣设置")]),a("a-form-item",{attrs:{label:"会员折扣",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后会员折扣，会员购买此商品可以享受会员等级折扣价"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_enable_grade",{initialValue:1,rules:[{required:!0}]}],expression:"['is_enable_grade', { initialValue: 1, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate(!0)}}},[a("a-radio",{attrs:{value:1}},[e._v("开启")]),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),a("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_enable_grade"),expression:"form.getFieldValue('is_enable_grade')"}],attrs:{label:"会员折扣设置",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_alone_grade",{initialValue:0,rules:[{required:!0}]}],expression:"['is_alone_grade', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate(!0)}}},[a("a-radio",{attrs:{value:0}},[e._v("默认等级折扣")]),a("a-radio",{attrs:{value:1}},[e._v("单独设置折扣")])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_alone_grade"),expression:"form.getFieldValue('is_alone_grade')"}]},e._l(e.formData.userGradeList,(function(t){return a("a-form-item",{key:t.grade_id},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["alone_grade_equity[grade_id:".concat(t.grade_id,"]"),{initialValue:e.formData.defaultUserGradeValue[t.grade_id],rules:[{required:!0,message:"折扣率不能为空"}]}],expression:"[`alone_grade_equity[grade_id:${item.grade_id}]`, {\n                    initialValue: formData.defaultUserGradeValue[item.grade_id], rules: [{ required: true, message: '折扣率不能为空' }]\n                  }]"}],attrs:{addonBefore:t.name,addonAfter:"折",inputProps:{min:0,max:9.9}}})],1)})),1),a("div",{staticClass:"form-item-help"},[e.form.getFieldValue("is_alone_grade")?a("p",{staticClass:"extra"},[e._v("单独折扣：折扣率范围0.0-9.9，例如: 9.8代表98折，0代表不折扣")]):a("p",{staticClass:"extra"},[e._v("默认折扣：默认为用户所属会员等级的折扣率")])])],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("apps-dealer"),expression:"$module('apps-dealer')"}]},[a("a-divider",{attrs:{orientation:"left"}},[e._v("分销设置")]),a("a-form-item",{attrs:{label:"分销佣金",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_ind_dealer",{initialValue:0,rules:[{required:!0}]}],expression:"['is_ind_dealer', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate(!0)}}},[a("a-radio",{attrs:{value:0}},[e._v("系统默认")]),a("a-radio",{attrs:{value:1}},[e._v("单独设置")])],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("若使用分销功能必须在 [分销中心 - 分销设置] 中开启")])])],1),a("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_ind_dealer"),expression:"form.getFieldValue('is_ind_dealer')"}],attrs:{label:"分销佣金类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["dealer_money_type",{initialValue:10,rules:[{required:!0}]}],expression:"['dealer_money_type', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate(!0)}}},[a("a-radio",{attrs:{value:10}},[e._v("百分比")]),a("a-radio",{attrs:{value:20}},[e._v("固定金额")])],1),e._l(e.formData.dealer.levelList,(function(t,r){return a("a-form-item",{key:r},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:[t.value,{rules:[{required:!0,message:"佣金不能为空"}]}],expression:"[item.value, { rules: [{ required: true, message: '佣金不能为空'}] }]"}],attrs:{addonBefore:t.name,addonAfter:10==e.form.getFieldValue("dealer_money_type")?"%":"元",inputProps:{min:0,precision:2}}})],1)}))],2)],1)],1)]),a("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[a("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1)],1)],1)},i=[],o=(t("d81d"),t("b64b"),t("d3b7"),t("d084")),s=t("2af9"),l=t("e1fe"),n=t("b78d"),d=t("ca00"),u={components:{GoodsType:n["a"],SelectImage:s["h"],SelectVideo:s["m"],Ueditor:s["n"],InputNumberGroup:s["c"],MultiSpec:n["b"]},data:function(){return{tabKey:0,labelCol:{span:3},wrapperCol:{span:10},isLoading:!1,isBtnLoading:!1,form:this.$form.createForm(this),goodsId:null,formData:l["a"].formData}},created:function(){this.initData()},beforeDestroy:function(){l["a"].formData.goods={}},methods:{initData:function(){var e=this;this.goodsId=this.$route.query.goodsId,this.isLoading=!0,l["a"].getFromData(this.goodsId).then((function(){Object(d["g"])(e.form.getFieldsValue())||(e.form.setFieldsValue(l["a"].getFieldsValue()),e.$nextTick((function(){e.form.setFieldsValue(l["a"].getFieldsValue2()),e.onForceUpdate()}))),e.isLoading=!1}))},onForceUpdate:function(){var e=this,a=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$forceUpdate(),a&&setTimeout((function(){e.$forceUpdate()}),10)},handleTabs:function(e){this.tabKey=e},onReloadCategoryList:function(){var e=this;this.isLoading=!0,l["a"].getCategoryList().then((function(){e.isLoading=!1}))},onReloadServiceList:function(){var e=this;this.isLoading=!0,l["a"].getServiceList().then((function(){e.isLoading=!1}))},onReloadDeliveryList:function(){var e=this;this.isLoading=!0,l["a"].getDeliveryList().then((function(){e.isLoading=!1}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){if(e)return a.onTargetTabError(e),!1;if(20===t.spec_type){var r=a.$refs.MultiSpec;if(!r.verifyForm())return a.tabKey=1,!1;t.specData=r.getFromSpecData()}return t.categoryIds=t.categorys.map((function(e){return e.value})),delete t.categorys,a.onFormSubmit(t),!0}))},onTargetTabError:function(e){var a=[["goods_type","goods_name","categorys","imagesIds","delivery_id","is_ind_delivery_type","delivery_type"],["spec_type","goods_price","is_restrict","restrict_total","restrict_single"],["content"],["alone_grade_equity","first_money","second_money","third_money"]],t=Object.keys(e).shift();for(var r in a)if(a[r].indexOf(t)>-1){this.tabKey=parseInt(r);break}},onFormSubmit:function(e){var a=this;this.isLoading=!0,this.isBtnLoading=!0,o["a"]({form:e}).then((function(e){a.$message.success(e.message,1.5),setTimeout((function(){a.$router.push("./index")}),1500)})).catch((function(){a.isBtnLoading=!1})).finally((function(){a.isLoading=!1}))}}},c=u,m=(t("7d2f"),t("2877")),p=Object(m["a"])(c,r,i,!1,null,"7fb115af",null);a["default"]=p.exports},"59aa":function(e,a,t){"use strict";t.d(a,"b",(function(){return i})),t.d(a,"c",(function(){return o})),t.d(a,"a",(function(){return s}));var r=t("5c06"),i=new r["a"]([{key:"PHYSICAL",name:"实物商品",value:10},{key:"VIRTUAL",name:"虚拟商品",value:20}]),o=(new r["a"]([{key:"SINGLE",name:"单规格",value:10},{key:"MULTI",name:"多规格",value:20}]),new r["a"]([{key:"NORMAL",name:"进行中",value:10},{key:"COMPLETED",name:"导入完成",value:20}])),s=new r["a"]([{key:"NORMAL",name:"进行中",value:10},{key:"COMPLETED",name:"采集完成",value:20}])},"62da":function(e,a,t){"use strict";t("03eb")},"7d2f":function(e,a,t){"use strict";t("99ce")},"861f1":function(e,a,t){"use strict";t("8a9f")},"88bc":function(e,a,t){(function(a){var t=1/0,r=9007199254740991,i="[object Arguments]",o="[object Function]",s="[object GeneratorFunction]",l="[object Symbol]",n="object"==typeof a&&a&&a.Object===Object&&a,d="object"==typeof self&&self&&self.Object===Object&&self,u=n||d||Function("return this")();function c(e,a,t){switch(t.length){case 0:return e.call(a);case 1:return e.call(a,t[0]);case 2:return e.call(a,t[0],t[1]);case 3:return e.call(a,t[0],t[1],t[2])}return e.apply(a,t)}function m(e,a){var t=-1,r=e?e.length:0,i=Array(r);while(++t<r)i[t]=a(e[t],t,e);return i}function p(e,a){var t=-1,r=a.length,i=e.length;while(++t<r)e[i+t]=a[t];return e}var v=Object.prototype,f=v.hasOwnProperty,g=v.toString,h=u.Symbol,_=v.propertyIsEnumerable,b=h?h.isConcatSpreadable:void 0,C=Math.max;function w(e,a,t,r,i){var o=-1,s=e.length;t||(t=k),i||(i=[]);while(++o<s){var l=e[o];a>0&&t(l)?a>1?w(l,a-1,t,r,i):p(i,l):r||(i[i.length]=l)}return i}function y(e,a){return e=Object(e),x(e,a,(function(a,t){return t in e}))}function x(e,a,t){var r=-1,i=a.length,o={};while(++r<i){var s=a[r],l=e[s];t(l,s)&&(o[s]=l)}return o}function S(e,a){return a=C(void 0===a?e.length-1:a,0),function(){var t=arguments,r=-1,i=C(t.length-a,0),o=Array(i);while(++r<i)o[r]=t[a+r];r=-1;var s=Array(a+1);while(++r<a)s[r]=t[r];return s[a]=o,c(e,this,s)}}function k(e){return q(e)||V(e)||!!(b&&e&&e[b])}function L(e){if("string"==typeof e||j(e))return e;var a=e+"";return"0"==a&&1/e==-t?"-0":a}function V(e){return F(e)&&f.call(e,"callee")&&(!_.call(e,"callee")||g.call(e)==i)}var q=Array.isArray;function D(e){return null!=e&&I(e.length)&&!N(e)}function F(e){return O(e)&&D(e)}function N(e){var a=$(e)?g.call(e):"";return a==o||a==s}function I(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=r}function $(e){var a=typeof e;return!!e&&("object"==a||"function"==a)}function O(e){return!!e&&"object"==typeof e}function j(e){return"symbol"==typeof e||O(e)&&g.call(e)==l}var R=S((function(e,a){return null==e?{}:y(e,m(w(a,1),L))}));e.exports=R}).call(this,t("c8ba"))},"8a9f":function(e,a,t){},"967a":function(e,a,t){"use strict";t.d(a,"f",(function(){return o})),t.d(a,"b",(function(){return s})),t.d(a,"d",(function(){return l})),t.d(a,"a",(function(){return n})),t.d(a,"e",(function(){return d})),t.d(a,"c",(function(){return u}));var r=t("b775"),i={list:"/setting.delivery/list",all:"/setting.delivery/all",detail:"/setting.delivery/detail",add:"/setting.delivery/add",edit:"/setting.delivery/edit",delete:"/setting.delivery/delete"};function o(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function s(e){return Object(r["b"])({url:i.all,method:"get",params:e})}function l(e){return Object(r["b"])({url:i.detail,method:"get",params:e})}function n(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function d(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function u(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},"99ce":function(e,a,t){},a53d:function(e,a,t){},acbe:function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-row",{staticClass:"row-item-search"},[a("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[a("a-form-item",{attrs:{label:"商品名称"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goodsName"],expression:"['goodsName']"}],attrs:{placeholder:"请输入商品名称/编码"}})],1),a("a-form-item",{attrs:{label:"订单号"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["orderNo"],expression:"['orderNo']"}],attrs:{placeholder:"请输入订单号"}})],1),a("a-form-item",{attrs:{label:"会员ID"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["userId"],expression:"['userId']"}],attrs:{placeholder:"请输入会员ID"}})],1),a("a-form-item",{attrs:{label:"状态"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:-1}],expression:"['status', { initialValue: -1 }]"}]},[a("a-select-option",{attrs:{value:-1}},[e._v("全部")]),a("a-select-option",{attrs:{value:1}},[e._v("显示")]),a("a-select-option",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{staticClass:"search-btn"},[a("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1),a("div",{staticClass:"row-item-tab clearfix"},[a("div",{staticClass:"tab-list fl-l"},[a("a-radio-group",{attrs:{defaultValue:e.queryParam.score},on:{change:e.handleTabs}},[a("a-radio-button",{attrs:{value:0}},[e._v("全部")]),a("a-radio-button",{attrs:{value:10}},[e._v("好评")]),a("a-radio-button",{attrs:{value:20}},[e._v("中评")]),a("a-radio-button",{attrs:{value:30}},[e._v("差评")])],1)],1)])],1),a("s-table",{ref:"table",attrs:{rowKey:"comment_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15,scroll:{x:1450}},scopedSlots:e._u([{key:"orderGoods",fn:function(e){return a("span",{},[a("GoodsItem",{attrs:{data:{image:e.goods_image,imageAlt:"商品图片",title:e.goods_name,goodsProps:e.goods_props}}})],1)}},{key:"user",fn:function(e){return a("span",{},[a("UserItem",{attrs:{user:e}})],1)}},{key:"score",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:{10:"green",20:"",30:"red"}[t]}},[e._v(e._s({10:"好评",20:"中评",30:"差评"}[t]))])],1)}},{key:"content",fn:function(t){return a("span",{},[a("p",{staticClass:"twoline-hide",staticStyle:{width:"260px"}},[e._v(e._s(t))])])}},{key:"is_picture",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:t?"green":""}},[e._v(e._s(t?"是":"否"))])],1)}},{key:"status",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:t?"green":""}},[e._v(e._s(t?"显示":"隐藏"))])],1)}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(a){return e.handleEdit(r)}}},[e._v("编辑")]),a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),a("EditForm",{ref:"EditForm",on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=t("5530"),s=(t("d3b7"),t("b775")),l={list:"/goods.comment/list",detail:"/goods.comment/detail",edit:"/goods.comment/edit",delete:"/goods.comment/delete"};function n(e){return Object(s["b"])({url:l.list,method:"get",params:e})}function d(e){return Object(s["b"])({url:l.detail,method:"get",params:e})}function u(e){return Object(s["b"])({url:l.edit,method:"post",data:e})}function c(e){return Object(s["b"])({url:l.delete,method:"post",data:e})}var m=t("ab09"),p=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"评分",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["score",{initialValue:10,rules:[{required:!0}]}],expression:"['score', { initialValue: 10, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:10}},[e._v("好评")]),a("a-radio",{attrs:{value:20}},[e._v("中评")]),a("a-radio",{attrs:{value:30}},[e._v("差评")])],1)],1),a("a-form-item",{attrs:{label:"评价内容",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["content"],expression:"['content']"}],attrs:{placeholder:"请输入评价内容 (300个字符以内)",autoSize:{minRows:4}}})],1),a("a-form-item",{attrs:{label:"评价图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"最多允许6张，可拖拽调整显示顺序"}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["imageIds"],expression:"['imageIds']"}],attrs:{multiple:"",maxNum:6,defaultList:e.record.imageList}})],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("显示")]),a("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},v=[],f=t("88bc"),g=t.n(f),h=t("2af9"),_={components:{SelectImage:h["h"]},data:function(){return{title:"编辑评价",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),commentId:null,record:{}}},methods:{edit:function(e){this.visible=!0,this.commentId=e,this.getRecordData()},getRecordData:function(){var e=this;this.confirmLoading=!0,d({commentId:this.commentId}).then((function(a){e.record=a.data.detail,e.setFieldsValue()})).finally((function(a){e.confirmLoading=!1}))},setFieldsValue:function(){var e=this,a=this.form.setFieldsValue;this.$nextTick((function(){a(g()(e.record,["score","content","status","sort","imageIds"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.record={},this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,u({commentId:this.commentId,form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(e){a.confirmLoading=!1}))}}},b=_,C=t("2877"),w=Object(C["a"])(b,p,v,!1,null,null,null),y=w.exports,x=[{title:"ID",dataIndex:"comment_id"},{title:"商品信息",dataIndex:"orderGoods",width:"320px",scopedSlots:{customRender:"orderGoods"}},{title:"买家",dataIndex:"user",width:"180px",scopedSlots:{customRender:"user"}},{title:"评分",dataIndex:"score",scopedSlots:{customRender:"score"}},{title:"评价内容",dataIndex:"content",width:"280px",scopedSlots:{customRender:"content"}},{title:"图片评价",dataIndex:"is_picture",scopedSlots:{customRender:"is_picture"}},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"评价时间",width:"180px",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"150px",fixed:"right",scopedSlots:{customRender:"action"}}],S={name:"Index",components:{STable:m["b"],GoodsItem:m["a"],UserItem:m["c"],EditForm:y},data:function(){var e=this;return{searchForm:this.$form.createForm(this),categoryListTree:[],queryParam:{score:0},isLoading:!1,columns:x,loadData:function(a){return n(Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleTabs:function(e){this.queryParam.score=e.target.value,this.handleRefresh(!0)},handleSearch:function(e){var a=this;e.preventDefault(),this.searchForm.validateFields((function(e,t){e||(a.queryParam=Object(o["a"])(Object(o["a"])({},a.queryParam),t),a.handleRefresh(!0))}))},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return c({commentId:e.comment_id}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleEdit:function(e){this.$refs.EditForm.edit(e.comment_id)},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)}}},k=S,L=(t("2742"),Object(C["a"])(k,r,i,!1,null,"669c44a3",null));a["default"]=L.exports},b48d:function(e,a,t){"use strict";t("e84a")},b78d:function(e,a,t){"use strict";t.d(a,"a",(function(){return c})),t.d(a,"b",(function(){return D}));var r=function(){var e=this,a=e._self._c;return a("div",{staticClass:"goods-type"},e._l(e.list,(function(t,r){return a("div",{directives:[{name:"show",rawName:"v-show",value:!e.onlyShowChecked||e.value==t.value,expression:"!onlyShowChecked || value == item.value"}],key:r,staticClass:"type-item",class:{checked:e.value==t.value},on:{click:function(a){return e.handleItem(t.value)}}},[a("div",{staticClass:"type-title"},[e._v(e._s(t.title))]),a("div",{staticClass:"type-help"},[e._v(e._s(t.help))]),e.value==t.value?a("a-icon",{staticClass:"icon-checked",attrs:{type:"check"}}):e._e()],1)})),0)},i=[],o=(t("b0c0"),t("4d91")),s=t("59aa"),l={name:"GoodsType",model:{prop:"value",event:"change"},props:{value:o["a"].any,onlyShowChecked:o["a"].bool.def(!1)},data:function(){return{defaultValue:s["b"].PHYSICAL.value,list:[{title:s["b"].PHYSICAL.name,help:"物流发货",value:s["b"].PHYSICAL.value},{title:s["b"].VIRTUAL.name,help:"无需物流",value:s["b"].VIRTUAL.value}]}},methods:{handleItem:function(e){this.$emit("change",e)}}},n=l,d=(t("1457"),t("2877")),u=Object(d["a"])(n,r,i,!1,null,"7fa6c589",null),c=u.exports,m=function(){var e=this,a=e._self._c;return a("div",[a("a-form-item",{attrs:{label:"商品规格",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("div",{staticClass:"form-item-help",staticStyle:{"line-height":"36px"}},[a("small",[e._v("最多添加3个商品规格组，生成的SKU数量不能超出50个")])]),e._l(e.multiSpecData.specList,(function(t,r){return a("div",{key:r,staticClass:"spec-group"},[a("div",{staticClass:"spec-group-item clearfix"},[a("a-input",{staticClass:"group-item-input",attrs:{readOnly:e.isSpecLocked,placeholder:"请输入规格名称"},on:{change:e.onChangeSpecGroupIpt},model:{value:t.spec_name,callback:function(a){e.$set(t,"spec_name",a)},expression:"item.spec_name"}}),e.isSpecLocked?e._e():a("a",{staticClass:"group-item-delete",attrs:{href:"javascript:;"},on:{click:function(a){return e.handleDeleteSpecGroup(r)}}},[e._v("删除规格组")])],1),a("div",{staticClass:"spec-value clearfix"},[e._l(t.valueList,(function(t,i){return a("div",{key:i,staticClass:"spec-value-item"},[a("a-input",{staticClass:"value-item-input",attrs:{readOnly:e.isSpecLocked,placeholder:"请输入规格值"},on:{change:e.onChangeSpecValueIpt},model:{value:t.spec_value,callback:function(a){e.$set(t,"spec_value",a)},expression:"itm.spec_value"}}),e.isSpecLocked?e._e():a("a-icon",{staticClass:"icon-close",attrs:{theme:"filled",type:"close-circle"},on:{click:function(a){return e.handleDeleteSpecValue(r,i)}}})],1)})),e.isSpecLocked?e._e():a("div",{staticClass:"spec-value-add"},[a("a",{staticClass:"group-item-delete",attrs:{href:"javascript:;"},on:{click:function(a){return e.handleAddSpecValue(r)}}},[e._v("新增规格值")])])],2)])})),!e.isSpecLocked&&e.multiSpecData.specList.length<3?a("a-button",{staticClass:"spec-group-add-btn",attrs:{icon:"plus"},on:{click:e.handleAddSpecGroup}},[e._v("添加规格组")]):e._e()],2),a("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.multiSpecData.skuList.length,expression:"multiSpecData.skuList.length"}],attrs:{label:"SKU列表",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[e.multiSpecData.skuList.length>1?a("div",{staticClass:"sku-batch"},[a("span",{staticClass:"title"},[e._v("批量设置:")]),a("a-input-number",{attrs:{placeholder:"商品价格",min:.01,precision:2},model:{value:e.multiSpecData.skuBatchForm.goods_price,callback:function(a){e.$set(e.multiSpecData.skuBatchForm,"goods_price",a)},expression:"multiSpecData.skuBatchForm.goods_price"}}),a("a-input-number",{attrs:{placeholder:"划线价格",min:0,precision:2},model:{value:e.multiSpecData.skuBatchForm.line_price,callback:function(a){e.$set(e.multiSpecData.skuBatchForm,"line_price",a)},expression:"multiSpecData.skuBatchForm.line_price"}}),a("a-input-number",{attrs:{placeholder:"库存数量",min:0,precision:0},model:{value:e.multiSpecData.skuBatchForm.stock_num,callback:function(a){e.$set(e.multiSpecData.skuBatchForm,"stock_num",a)},expression:"multiSpecData.skuBatchForm.stock_num"}}),a("a-input-number",{attrs:{placeholder:"商品重量",min:0,precision:2},model:{value:e.multiSpecData.skuBatchForm.goods_weight,callback:function(a){e.$set(e.multiSpecData.skuBatchForm,"goods_weight",a)},expression:"multiSpecData.skuBatchForm.goods_weight"}}),a("a-input",{attrs:{placeholder:"sku编码"},model:{value:e.multiSpecData.skuBatchForm.goods_sku_no,callback:function(a){e.$set(e.multiSpecData.skuBatchForm,"goods_sku_no",a)},expression:"multiSpecData.skuBatchForm.goods_sku_no"}}),a("a-button",{on:{click:e.handleSkuBatch}},[e._v("立即设置")])],1):e._e(),a("a-table",{staticClass:"sku-list",attrs:{columns:e.multiSpecData.skuColumns,dataSource:e.multiSpecData.skuList,scroll:{x:!0},pagination:!1,bordered:""},scopedSlots:e._u([{key:"image",fn:function(t,r){return[a("SelectImage",{attrs:{defaultList:r.image_id>0&&r.image?[r.image]:[],width:50},model:{value:r.image_id,callback:function(a){e.$set(r,"image_id",a)},expression:"item.image_id"}})]}},{key:"goods_price",fn:function(t,r){return[a("a-input-number",{attrs:{size:"small",min:.01,precision:2},model:{value:r.goods_price,callback:function(a){e.$set(r,"goods_price",a)},expression:"item.goods_price"}})]}},{key:"line_price",fn:function(t,r){return[a("a-input-number",{attrs:{size:"small",min:0,precision:2},model:{value:r.line_price,callback:function(a){e.$set(r,"line_price",a)},expression:"item.line_price"}})]}},{key:"stock_num",fn:function(t,r){return[a("a-input-number",{attrs:{size:"small",min:0,precision:0},model:{value:r.stock_num,callback:function(a){e.$set(r,"stock_num",a)},expression:"item.stock_num"}})]}},{key:"goods_weight",fn:function(t,r){return[a("a-input-number",{attrs:{size:"small",min:0,precision:2},model:{value:r.goods_weight,callback:function(a){e.$set(r,"goods_weight",a)},expression:"item.goods_weight"}})]}},{key:"goods_sku_no",fn:function(t,r){return[a("a-input",{attrs:{size:"small"},model:{value:r.goods_sku_no,callback:function(a){e.$set(r,"goods_sku_no",a)},expression:"item.goods_sku_no"}})]}}])})],1)],1)},p=[],v=t("5530"),f=t("d4ec"),g=t("bee2"),h=(t("d3b7"),t("159b"),t("a15b"),t("d81d"),t("99af"),t("7db0"),t("b64b"),t("a434"),t("2ef0")),_=t.n(h),b=t("ca00"),C=[{title:"预览图",dataIndex:"image",width:90,scopedSlots:{customRender:"image"}},{title:"商品价格",dataIndex:"goods_price",width:120,scopedSlots:{customRender:"goods_price"}},{title:"划线价格",dataIndex:"line_price",width:120,scopedSlots:{customRender:"line_price"}},{title:"库存数量",dataIndex:"stock_num",width:120,scopedSlots:{customRender:"stock_num"}},{title:"商品重量 (KG)",dataIndex:"goods_weight",width:120,scopedSlots:{customRender:"goods_weight"}},{title:"SKU编码",dataIndex:"goods_sku_no",width:140,scopedSlots:{customRender:"goods_sku_no"}}],w={image_id:0,image:{},goods_price:"",line_price:"",stock_num:"",goods_weight:"",goods_sku_no:""},y=function(){function e(){Object(f["a"])(this,e),this.multiSpecData={},this.error="",this.multiSpecData={specList:[],skuList:[],skuColumns:_.a.cloneDeep(C),skuBatchForm:_.a.cloneDeep(w)}}return Object(g["a"])(e,[{key:"getData",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.length&&(this.multiSpecData.specList=_.a.cloneDeep(e),this.multiSpecData.skuList=_.a.cloneDeep(a));var t=this.specGroupArr(),r=S(t),i=this.rowSpanArr(t,r);return this.buildSkuColumns(i),this.buildSkuList(r),this.multiSpecData}},{key:"isEmpty",value:function(){return 0===this.multiSpecData.specList.length}},{key:"getError",value:function(){return this.error}},{key:"specGroupArr",value:function(){var e=[];return this.multiSpecData.specList.forEach((function(a){var t=[];a.valueList.forEach((function(e){t.push(e)})),e.push(t)})),e}},{key:"rowSpanArr",value:function(e,a){for(var t=[],r=a.length,i=0;i<e.length;i++)t[i]=parseInt(r/e[i].length),r=t[i];return t}},{key:"buildSkuList",value:function(e){for(var a=[],t=function(){var t=Object(v["a"])(Object(v["a"])({},w),{},{key:r,tempId:e[r].map((function(e){return e.spec_value})).join("_"),skuKeys:e[r].map((function(e){return{groupKey:e.groupKey,valueKey:e.key}}))});e[r].forEach((function(e,a){t["spec_value_".concat(a)]=e.spec_value})),a.push(t)},r=0;r<e.length;r++)t();this.multiSpecData.skuList=this.oldSkuList(a)}},{key:"oldSkuList",value:function(e){var a=this.multiSpecData.skuList.concat();if(!a.length||!e.length)return e;var t=function(t){var r={};r=a.length===e.length?_.a.cloneDeep(a[t]):a.find((function(a){return a.tempId===e[t].tempId})),r&&(e[t]=Object(v["a"])(Object(v["a"])({},e[t]),_.a.pick(r,Object.keys(w))))};for(var r in e)t(r);return e}},{key:"buildSkuColumns",value:function(e){for(var a=this.multiSpecData.specList,t=C.concat(),r=function(a,t,r,i){var o={children:t,attrs:{}},s=e[a-1];return o.attrs.rowSpan=i%s===0?s:0,o},i=function(e){var i=a[e-1];t.unshift({title:i.spec_name,dataIndex:"spec_value_".concat(e-1),customRender:function(a,t,i){return r(e,a,t,i)}})},o=a.length;o>0;o--)i(o);this.multiSpecData.skuColumns=t}},{key:"handleAddSpecGroup",value:function(){var e=this.multiSpecData.specList;e.push({key:e.length||0,spec_name:"",valueList:[]});var a=e.length-1;this.handleAddSpecValue(a)}},{key:"handleAddSpecValue",value:function(e){var a=this.multiSpecData.specList[e],t=a.valueList;t.push({key:t.length||0,groupKey:a.key,spec_value:""}),this.onRefreshSpecValueKey(e)}},{key:"handleDeleteSpecGroup",value:function(e){this.multiSpecData.specList.splice(e,1),this.onUpdate(!1)}},{key:"handleDeleteSpecValue",value:function(e,a){this.multiSpecData.specList[e].valueList.splice(a,1),this.onRefreshSpecValueKey(e),this.onUpdate(!1)}},{key:"onRefreshSpecValueKey",value:function(e){var a=this.multiSpecData.specList[e],t=a.valueList;t.forEach((function(e,a){t[a].key=a}))}},{key:"handleSkuBatch",value:function(){var e=this.getFilterObject(this.multiSpecData.skuBatchForm),a=this.multiSpecData.skuList;for(var t in a)a[t]=Object(v["a"])(Object(v["a"])({},a[t]),e);this.onUpdate(!1)}},{key:"getFilterObject",value:function(e){var a={};for(var t in e){var r=e[t];Object(b["f"])(r)||(a[t]=r)}return a}},{key:"verifyForm",value:function(){return!!this.verifySpec()&&!!this.verifySkuList()}},{key:"verifySkuList",value:function(){var e=[{field:"goods_price",name:"商品价格"},{field:"stock_num",name:"库存数量"},{field:"goods_weight",name:"商品重量"}],a=this.multiSpecData.skuList;for(var t in a){var r=a[t];for(var i in e){var o=r[e[i].field];if(""===o||null===o)return this.error="".concat(e[i].name,"不能为空"),!1}}return!0}},{key:"verifySpec",value:function(){var e=this.multiSpecData.specList;if(!e.length)return this.error="亲，还没有添加规格组~",!1;for(var a in e){var t=e[a];if(Object(b["f"])(t.spec_name))return this.error="规格组名称不能为空~",!1;var r=t.valueList;if(!r.length)return this.error="还没有添加规格值~",!1;for(var i in r)if(Object(b["f"])(r[i].spec_value))return this.error="规格值不能为空~",!1}return!0}},{key:"getFromSpecData",value:function(){var e=this.multiSpecData,a=e.specList,t=e.skuList,r={specList:_.a.cloneDeep(a),skuList:_.a.cloneDeep(t)};for(var i in r.skuList){var o=r.skuList[i];delete o.image,delete o.key}return r}},{key:"onUpdate",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e?Object(b["c"])(x,200)(this):x(this)}}]),e}(),x=function(e){return e.getData()},S=function(e){return e.length?Array.prototype.reduce.call(e,(function(e,a){var t=[];return e.forEach((function(e){a.forEach((function(a){t.push(e.concat([a]))}))})),t}),[[]]):[]},k=t("2af9"),L={components:{SelectImage:k["h"]},props:{defaultSpecList:o["a"].array.def([]),defaultSkuList:o["a"].array.def([]),isSpecLocked:o["a"].bool.def(!1)},data:function(){return{labelCol:{span:3},wrapperCol:{span:21},MultiSpecModel:new y,multiSpecData:{specList:[],skuList:[]}}},watch:{defaultSpecList:function(e){e.length&&this.MultiSpecModel.isEmpty()&&this.getData()}},created:function(){this.getData()},methods:{getData:function(){var e=this.defaultSpecList,a=this.defaultSkuList;this.multiSpecData=this.MultiSpecModel.getData(e,a)},getFromSpecData:function(){return this.MultiSpecModel.getFromSpecData()},handleAddSpecGroup:function(){this.checkSkuMaxNum()&&this.MultiSpecModel.handleAddSpecGroup()},handleDeleteSpecGroup:function(e){var a=this,t=this.$confirm({title:"您确定要删除该规格组吗?",content:"删除后不可恢复",onOk:function(){a.MultiSpecModel.handleDeleteSpecGroup(e),t.destroy()}})},handleAddSpecValue:function(e){this.checkSkuMaxNum()&&this.MultiSpecModel.handleAddSpecValue(e)},handleDeleteSpecValue:function(e,a){var t=this,r=this.$confirm({title:"您确定要删除该规格值吗?",content:"删除后不可恢复",onOk:function(){t.MultiSpecModel.handleDeleteSpecValue(e,a),r.destroy()}})},onChangeSpecGroupIpt:function(){this.MultiSpecModel.onUpdate(!0)},onChangeSpecValueIpt:function(e,a){this.MultiSpecModel.onUpdate(!0)},checkSkuMaxNum:function(){var e=this.multiSpecData.skuList;return!(e.length>=50)||(this.$message.error("生成的sku列表数量不能大于50个，当前数量：".concat(e.length,"个"),2.5),!1)},handleSkuBatch:function(){this.MultiSpecModel.handleSkuBatch()},verifyForm:function(){return!!this.MultiSpecModel.verifyForm()||(this.$message.error(this.MultiSpecModel.getError(),2),!1)}}},V=L,q=(t("62da"),Object(d["a"])(V,m,p,!1,null,"b63f0ed6",null)),D=q.exports},c1df6:function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("a-form",{attrs:{form:e.form,selfUpdate:!0},on:{submit:e.handleSubmit}},[a("a-tabs",{attrs:{activeKey:e.tabKey,tabBarStyle:{marginBottom:"30px"}},on:{change:e.handleTabs}},[a("a-tab-pane",{key:0,attrs:{tab:"基本信息"}}),a("a-tab-pane",{key:1,attrs:{tab:"规格/库存"}}),a("a-tab-pane",{key:2,attrs:{tab:"商品详情"}}),a("a-tab-pane",{key:3,attrs:{tab:"更多设置"}})],1),a("div",{staticClass:"tabs-content"},[a("div",{directives:[{name:"show",rawName:"v-show",value:0==e.tabKey,expression:"tabKey == 0"}],staticClass:"tab-pane"},[a("a-form-item",{attrs:{label:"商品类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("GoodsType",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_type",{rules:[{required:!0}]}],expression:"['goods_type', { rules: [{ required: true }] }]"}],attrs:{onlyShowChecked:!0},on:{change:function(a){return e.onForceUpdate(!0)}}})],1),a("a-form-item",{attrs:{label:"商品名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['goods_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入商品名称"}})],1),a("a-form-item",{attrs:{label:"商品分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["categorys",{rules:[{required:!0,message:"请至少选择1个商品分类"}]}],expression:"['categorys', { rules: [{ required: true, message: '请至少选择1个商品分类' }] }]"}],attrs:{placeholder:"请选择商品分类",dropdownStyle:{maxHeight:"500px",overflow:"auto"},treeData:e.formData.categoryList,treeCheckable:"",treeCheckStrictly:"",allowClear:""}}),a("div",{staticClass:"form-item-help"},[a("router-link",{attrs:{target:"_blank",to:{path:"/goods/category/index"}}},[e._v("去新增")]),a("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadCategoryList}},[e._v("刷新")])],1)],1),a("a-form-item",{attrs:{label:"商品图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸：750*750像素, 最多上传10张, 可拖拽图片调整顺序, 第1张将作为商品首图"}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["imagesIds",{rules:[{required:!0,message:"请至少上传1张商品图片"}]}],expression:"['imagesIds', { rules: [{ required: true, message: '请至少上传1张商品图片' }] }]"}],attrs:{multiple:"",maxNum:10,defaultList:e.formData.goods.goods_images}})],1),a("a-form-item",{attrs:{label:"商品编码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_no"],expression:"['goods_no']"}],attrs:{placeholder:"请输入商品编码"}})],1),10==e.form.getFieldValue("goods_type")?a("a-form-item",{attrs:{label:"配送方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_ind_delivery_type",{initialValue:0,rules:[{required:!0}]}],expression:"['is_ind_delivery_type', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate()}}},[a("a-radio",{attrs:{value:0}},[e._v("系统默认")]),a("a-radio",{attrs:{value:1}},[e._v("单独配置")])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_ind_delivery_type"),expression:"form.getFieldValue('is_ind_delivery_type')"}]},[a("a-form-item",{attrs:{extra:"需在 [设置 - 配送方式] 中，开启支持的配送方式才可生效"}},[a("a-checkbox-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["delivery_type",{initialValue:[10,20],rules:[{required:!0,message:"配送方式至少选择一个"}]}],expression:"['delivery_type', { initialValue: [10, 20], rules: [{ required: true, message: '配送方式至少选择一个' }] }]"}],attrs:{options:[{label:"快递配送",value:10},{label:"上门自提",value:20}]}})],1)],1)],1):e._e(),10==e.form.getFieldValue("goods_type")?a("a-form-item",{attrs:{label:"运费模板",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["delivery_id",{rules:[{required:!0,message:"请选择运费模板"}]}],expression:"['delivery_id', { rules: [{ required: true, message: '请选择运费模板' }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请选择运费模板"}},e._l(e.formData.deliveryList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.delivery_id}},[e._v(e._s(t.name))])})),1),a("div",{staticClass:"form-item-help"},[a("router-link",{attrs:{target:"_blank",to:{path:"/setting/delivery/template/create"}}},[e._v("新增模板")]),a("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadDeliveryList}},[e._v("刷新")])],1)],1):e._e(),a("a-form-item",{attrs:{label:"商品状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:10,rules:[{required:!0}]}],expression:"['status', { initialValue: 10, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:10}},[e._v("上架")]),a("a-radio",{attrs:{value:20}},[e._v("下架")])],1)],1),a("a-form-item",{attrs:{label:"商品排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true }] }]"}],attrs:{min:0}})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:1==e.tabKey,expression:"tabKey == 1"}],staticClass:"tab-pane"},[a("a-form-item",{attrs:{label:"规格类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["spec_type",{initialValue:10,rules:[{required:!0}]}],expression:"['spec_type', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate()}}},[a("a-radio",{attrs:{value:10,disabled:e.formData.goods.isSpecLocked}},[e._v("单规格")]),a("a-radio",{attrs:{value:20,disabled:e.formData.goods.isSpecLocked}},[e._v("多规格")])],1),e.formData.goods.isSpecLocked?a("p",{staticClass:"form-item-help"},[a("small",{staticClass:"c-red"},[e._v("注：该商品当前正在参与其他活动，商品规格不允许更改")])]):e._e()],1),20==e.form.getFieldValue("spec_type")?a("div",[a("MultiSpec",{ref:"MultiSpec",attrs:{isSpecLocked:e.formData.goods.isSpecLocked,defaultSpecList:e.formData.goods.specList,defaultSkuList:e.formData.goods.skuList}})],1):e._e(),10==e.form.getFieldValue("spec_type")?a("div",[a("a-form-item",{attrs:{label:"商品价格",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际购买金额，最低0.01"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_price",{rules:[{required:!0,message:"请输入商品价格"}]}],expression:"['goods_price', { rules: [{ required: true, message: '请输入商品价格' }] }]"}],attrs:{min:.01,precision:2}}),a("span",{staticClass:"ml-10"},[e._v("元")])],1),a("a-form-item",{attrs:{label:"划线价",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"划线价仅用于商品页展示"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["line_price"],expression:"['line_price']"}],attrs:{min:0,precision:2}}),a("span",{staticClass:"ml-10"},[e._v("元")])],1),a("a-form-item",{attrs:{label:"当前库存数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际库存数量，为0时用户无法下单"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["stock_num",{initialValue:100,rules:[{required:!0,message:"请输入库存数量"}]}],expression:"['stock_num', { initialValue: 100, rules: [{ required: true, message: '请输入库存数量' }] }]"}],attrs:{min:0,precision:0}}),a("span",{staticClass:"ml-10"},[e._v("件")])],1),a("a-form-item",{attrs:{label:"商品重量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际重量，用于计算运费"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_weight",{initialValue:0,rules:[{required:!0,message:"请输入库存数量"}]}],expression:"['goods_weight', { initialValue: 0, rules: [{ required: true, message: '请输入库存数量' }] }]"}],attrs:{min:0}}),a("span",{staticClass:"ml-10"},[e._v("千克 (Kg)")])],1)],1):e._e(),a("a-form-item",{attrs:{label:"库存计算方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["deduct_stock_type",{initialValue:10,rules:[{required:!0}]}],expression:"['deduct_stock_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:10}},[e._v("下单减库存")]),a("a-radio",{attrs:{value:20}},[e._v("付款减库存")])],1)],1),a("a-form-item",{attrs:{label:"商品限购",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用于限制每人购买该商品的数量"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_restrict",{initialValue:0,rules:[{required:!0}]}],expression:"['is_restrict', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate()}}},[a("a-radio",{attrs:{value:0}},[e._v("关闭")]),a("a-radio",{attrs:{value:1}},[e._v("开启")])],1),e.form.getFieldValue("is_restrict")?a("div",{staticClass:"mt-10"},[a("a-form-item",[a("span",{staticClass:"mr-10"},[e._v("总限购")]),a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_total",{rules:[{required:!0,message:"请输入总限购数量"}]}],expression:"['restrict_total', { rules: [{ required: true, message: '请输入总限购数量' }] }]"}],attrs:{min:1,precision:0}}),a("span",{staticClass:"ml-10"},[e._v("件/人")])],1),a("a-form-item",[a("span",{staticClass:"mr-10"},[e._v("每单限购")]),a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_single",{rules:[{required:!0,message:"请输入每单限购数量"}]}],expression:"['restrict_single', { rules: [{ required: true, message: '请输入每单限购数量' }] }]"}],attrs:{min:1,precision:0}}),a("span",{staticClass:"ml-10"},[e._v("件/人")])],1)],1):e._e()],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:2==e.tabKey,expression:"tabKey == 2"}],staticClass:"tab-pane"},[a("a-form-item",{attrs:{label:"商品详情",labelCol:e.labelCol,wrapperCol:{span:16}}},[a("Ueditor",{directives:[{name:"decorator",rawName:"v-decorator",value:["content",{rules:[{required:!0,message:"商品详情不能为空"}]}],expression:"['content', { rules: [{ required: true, message: '商品详情不能为空' }] }]"}]})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:3==e.tabKey,expression:"tabKey == 3"}],staticClass:"tab-pane"},[a("a-form-item",{attrs:{label:"主图视频",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议视频宽高比19:9，建议时长8-45秒"}},[a("SelectVideo",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_id"],expression:"['video_id']"}],attrs:{multiple:!1,defaultList:e.formData.goods.video?[e.formData.goods.video]:[]}})],1),a("a-form-item",{attrs:{label:"视频封面",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸：750像素*750像素"}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_cover_id"],expression:"['video_cover_id']"}],attrs:{multiple:!1,defaultList:e.formData.goods.videoCover?[e.formData.goods.videoCover]:[]}})],1),a("a-form-item",{attrs:{label:"商品卖点",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"一句话简述，例如：此款商品美观大方 性价比较高 不容错过"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["selling_point"],expression:"['selling_point']"}],attrs:{placeholder:"请输入商品卖点"}})],1),a("a-form-item",{attrs:{label:"服务与承诺",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[e.formData.serviceList?a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["serviceIds"],expression:"['serviceIds']"}],attrs:{mode:"multiple",placeholder:"请选择服务与承诺"}},e._l(e.formData.serviceList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.service_id}},[e._v(e._s(t.name))])})),1):e._e(),a("div",{staticClass:"form-item-help"},[a("router-link",{attrs:{target:"_blank",to:{path:"/goods/service/index"}}},[e._v("去新增")]),a("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadServiceList}},[e._v("刷新")])],1)],1),a("a-form-item",{attrs:{label:"初始销量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端展示的销量 = 初始销量 + 实际销量"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sales_initial",{initialValue:0}],expression:"['sales_initial', { initialValue: 0}]"}]})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("market-points"),expression:"$module('market-points')"}]},[a("a-divider",{attrs:{orientation:"left"}},[e._v("积分设置")]),a("a-form-item",{attrs:{label:"积分赠送",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后用户购买此商品将获得积分"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_points_gift",{initialValue:1,rules:[{required:!0}]}],expression:"['is_points_gift', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("开启")]),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),a("a-form-item",{attrs:{label:"积分抵扣",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后用户购买此商品可以使用积分进行抵扣"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_points_discount",{initialValue:1,rules:[{required:!0}]}],expression:"['is_points_discount', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("开启")]),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("user-grade"),expression:"$module('user-grade')"}]},[a("a-divider",{attrs:{orientation:"left"}},[e._v("会员折扣设置")]),a("a-form-item",{attrs:{label:"会员折扣",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后会员折扣，会员购买此商品可以享受会员等级折扣价"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_enable_grade",{initialValue:1,rules:[{required:!0}]}],expression:"['is_enable_grade', { initialValue: 1, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate(!0)}}},[a("a-radio",{attrs:{value:1}},[e._v("开启")]),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),a("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_enable_grade"),expression:"form.getFieldValue('is_enable_grade')"}],attrs:{label:"会员折扣设置",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_alone_grade",{initialValue:0,rules:[{required:!0}]}],expression:"['is_alone_grade', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate(!0)}}},[a("a-radio",{attrs:{value:0}},[e._v("默认等级折扣")]),a("a-radio",{attrs:{value:1}},[e._v("单独设置折扣")])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_alone_grade"),expression:"form.getFieldValue('is_alone_grade')"}]},e._l(e.formData.userGradeList,(function(t){return a("a-form-item",{key:t.grade_id},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["alone_grade_equity[grade_id:".concat(t.grade_id,"]"),{initialValue:e.formData.defaultUserGradeValue[t.grade_id],rules:[{required:!0,message:"折扣率不能为空"}]}],expression:"[`alone_grade_equity[grade_id:${item.grade_id}]`, {\n                    initialValue: formData.defaultUserGradeValue[item.grade_id], rules: [{ required: true, message: '折扣率不能为空'}]\n                  }]"}],attrs:{addonBefore:t.name,addonAfter:"折",inputProps:{min:0,max:9.9}}})],1)})),1),a("div",{staticClass:"form-item-help"},[e.form.getFieldValue("is_alone_grade")?a("p",{staticClass:"extra"},[e._v("单独折扣：折扣率范围0.0-9.9，例如: 9.8代表98折，0代表不折扣")]):a("p",{staticClass:"extra"},[e._v("默认折扣：默认为用户所属会员等级的折扣率")])])],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("apps-dealer"),expression:"$module('apps-dealer')"}]},[a("a-divider",{attrs:{orientation:"left"}},[e._v("分销设置")]),a("a-form-item",{attrs:{label:"分销佣金",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_ind_dealer",{initialValue:0,rules:[{required:!0}]}],expression:"['is_ind_dealer', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate(!0)}}},[a("a-radio",{attrs:{value:0}},[e._v("系统默认")]),a("a-radio",{attrs:{value:1}},[e._v("单独设置")])],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("若使用分销功能必须在 [分销中心 - 分销设置] 中开启")])])],1),a("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_ind_dealer"),expression:"form.getFieldValue('is_ind_dealer')"}],attrs:{label:"分销佣金类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["dealer_money_type",{initialValue:10,rules:[{required:!0}]}],expression:"['dealer_money_type', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate(!0)}}},[a("a-radio",{attrs:{value:10}},[e._v("百分比")]),a("a-radio",{attrs:{value:20}},[e._v("固定金额")])],1),e._l(e.formData.dealer.levelList,(function(t,r){return a("a-form-item",{key:r},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:[t.value,{rules:[{required:!0,message:"佣金不能为空"}]}],expression:"[item.value, { rules: [{ required: true, message: '佣金不能为空'}] }]"}],attrs:{addonBefore:t.name,addonAfter:10==e.form.getFieldValue("dealer_money_type")?"%":"元",inputProps:{min:0,precision:2}}})],1)}))],2)],1)],1)]),a("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[a("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1)],1)],1)},i=[],o=(t("d81d"),t("b64b"),t("d3b7"),t("d084")),s=t("2af9"),l=t("e1fe"),n=t("b78d"),d=t("ca00"),u={components:{GoodsType:n["a"],SelectImage:s["h"],SelectVideo:s["m"],Ueditor:s["n"],InputNumberGroup:s["c"],MultiSpec:n["b"]},data:function(){return{tabKey:0,labelCol:{span:3},wrapperCol:{span:10},isLoading:!1,isBtnLoading:!1,form:this.$form.createForm(this),goodsId:null,formData:l["a"].formData}},created:function(){this.initData()},beforeDestroy:function(){l["a"].formData.goods={}},methods:{initData:function(){var e=this;this.goodsId=this.$route.query.goodsId,this.isLoading=!0,l["a"].getFromData(this.goodsId).then((function(){Object(d["g"])(e.form.getFieldsValue())||(e.form.setFieldsValue(l["a"].getFieldsValue()),e.$nextTick((function(){e.form.setFieldsValue(l["a"].getFieldsValue2()),e.onForceUpdate()}))),e.isLoading=!1}))},onForceUpdate:function(){var e=this,a=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$forceUpdate(),a&&setTimeout((function(){e.$forceUpdate()}),10)},handleTabs:function(e){this.tabKey=e},onReloadCategoryList:function(){var e=this;this.isLoading=!0,l["a"].getCategoryList().then((function(){e.isLoading=!1}))},onReloadServiceList:function(){var e=this;this.isLoading=!0,l["a"].getServiceList().then((function(){e.isLoading=!1}))},onReloadDeliveryList:function(){var e=this;this.isLoading=!0,l["a"].getDeliveryList().then((function(){e.isLoading=!1}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){if(e)return a.onTargetTabError(e),!1;if(20===t.spec_type){var r=a.$refs.MultiSpec;if(!r.verifyForm())return a.tabKey=1,!1;t.specData=r.getFromSpecData()}return t.categoryIds=t.categorys.map((function(e){return e.value})),delete t.categorys,a.onFormSubmit(t),!0}))},onTargetTabError:function(e){var a=[["goods_type","goods_name","categorys","imagesIds","delivery_id","is_ind_delivery_type","delivery_type"],["spec_type","goods_price","is_restrict","restrict_total","restrict_single"],["content"],["alone_grade_equity","first_money","second_money","third_money"]],t=Object.keys(e).shift();for(var r in a)if(a[r].indexOf(t)>-1){this.tabKey=parseInt(r);break}},onFormSubmit:function(e){var a=this;this.isLoading=!0,this.isBtnLoading=!0,o["e"]({goodsId:this.goodsId,form:e}).then((function(e){a.$message.success(e.message,1.5),setTimeout((function(){a.$router.push("./index")}),1500)})).catch((function(){a.isBtnLoading=!1})).finally((function(){a.isLoading=!1}))}}},c=u,m=(t("52e0"),t("2877")),p=Object(m["a"])(c,r,i,!1,null,"6aff9746",null);a["default"]=p.exports},cfdf:function(e,a,t){},dd3e:function(e,a,t){"use strict";t("cfdf")},e1fe:function(e,a,t){"use strict";var r=t("5530"),i=(t("d3b7"),t("3ca3"),t("ddb0"),t("d81d"),t("4de4"),t("159b"),t("2ef0")),o=t.n(i),s=t("8243"),l=t("d084"),n=t("2e1c"),d=t("19d3"),u=t("967a");a["a"]={goodsId:null,formData:{goods:{},categoryList:[],deliveryList:[],serviceList:[],defaultServiceIds:[],userGradeList:[],defaultUserGradeValue:{},dealer:{levelList:[{name:"一级佣金",value:"first_money"},{name:"二级佣金",value:"second_money"},{name:"三级佣金",value:"third_money"}]}},getFromData:function(){var e=this,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return this.goodsId=a,new Promise((function(t,r){Promise.all([e.getGoodsDetail(a),e.getCategoryList(),e.getDeliveryList(),e.getServiceList(),e.getUserGradeList()]).then((function(){e.setDefaultData(),t({formData:e.formData})}))}))},getGoodsDetail:function(){var e=this,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return!!a&&new Promise((function(t,r){l["d"]({goodsId:a}).then((function(a){e.formData.goods=a.data.goodsInfo,t()}))}))},getFieldsValue:function(){var e=this.formData.goods;e.categorys=this.formatCategoryIds(e.categoryIds);var a=o.a.pick(e,["goods_type","goods_name","categorys","goods_no","sort","status","spec_type","deduct_stock_type","is_restrict","content","selling_point","serviceIds","sales_initial","is_points_gift","is_points_discount","is_enable_grade","is_alone_grade","is_ind_dealer","dealer_money_type","first_money","second_money","third_money"]);return Object(r["a"])({},a)},getFieldsValue2:function(){var e=this.formData.goods,a={};if(10==e.goods_type&&(a["delivery_id"]=e["delivery_id"],a["is_ind_delivery_type"]=e["is_ind_delivery_type"],a["delivery_type"]=e["delivery_type"]),10==e.spec_type){var t=o.a.pick(e.skuList[0],["goods_price","line_price","stock_num","goods_weight"]);a=Object(r["a"])(Object(r["a"])({},a),t)}return 1==e.is_restrict&&(a["restrict_total"]=e["restrict_total"],a["restrict_single"]=e["restrict_single"]),a},formatCategoryIds:function(e){return e.map((function(e){return{value:e}}))},getCategoryList:function(){var e=this;return new Promise((function(a,t){s["a"].getCategoryTreeSelect().then((function(t){e.formData.categoryList=t,a()}))}))},getDeliveryList:function(){var e=this;return new Promise((function(a,t){u["b"]().then((function(t){e.formData.deliveryList=t.data.list,a()}))}))},getServiceList:function(){var e=this;return new Promise((function(a,t){d["b"]().then((function(t){e.formData.serviceList=t.data.list,a()}))}))},getUserGradeList:function(){var e=this;return new Promise((function(a,t){n["b"]({status:1}).then((function(t){e.formData.userGradeList=t.data.list,a()}))}))},setDefaultData:function(){this.setDefaultServiceIds(),this.setDefaultUserGradeValue()},setDefaultServiceIds:function(){var e=this.formData.serviceList;if(!this.goodsId){var a=e.filter((function(e){return e.is_default}));this.formData.defaultServiceIds=a.map((function(e){return e.service_id}))}},setDefaultUserGradeValue:function(){var e=this.formData.userGradeList,a=this.goodsId&&this.formData.goods.alone_grade_equity?this.formData.goods.alone_grade_equity:{},t={};e.forEach((function(e){t[e.grade_id]=a[e.grade_id]||e.equity.discount})),this.formData.defaultUserGradeValue=Object(r["a"])({},t)}}},e84a:function(e,a,t){},ee5f:function(e,a,t){"use strict";t.r(a);t("ac1f"),t("841c");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.pageTitle))]),a("div",{staticClass:"table-operator"},[a("a-row",[a("a-col",{attrs:{span:6}},[a("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("a-col",{attrs:{span:8,offset:10}},[a("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px",float:"right"},attrs:{placeholder:"请输入服务名称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(a){e.$set(e.queryParam,"search",a)},expression:"queryParam.search"}})],1)],1)],1),a("s-table",{ref:"table",attrs:{rowKey:"service_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15,scroll:{x:1300}},scopedSlots:e._u([{key:"summary",fn:function(t){return a("span",{},[a("p",{staticClass:"summary oneline-hide"},[e._v(e._s(t))])])}},{key:"is_default",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:t?"green":""}},[e._v(e._s(t?"是":"否"))])],1)}},{key:"status",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:t?"green":""}},[e._v(e._s(t?"显示":"隐藏"))])],1)}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(a){return e.handleEdit(r)}}},[e._v("编辑")]),a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),a("AddForm",{ref:"AddForm",on:{handleSubmit:e.handleRefresh}}),a("EditForm",{ref:"EditForm",on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=t("5530"),s=(t("d3b7"),t("19d3")),l=t("2af9"),n=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"服务名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入服务与承诺的名称"}})],1),a("a-form-item",{attrs:{label:"概述",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["summary"],expression:"['summary']"}],attrs:{placeholder:"请输入概述内容 (300个字符以内)",autoSize:{minRows:4}}})],1),a("a-form-item",{attrs:{label:"是否默认",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"新增商品时是否默认勾选"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_default",{initialValue:1,rules:[{required:!0}]}],expression:"['is_default', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("是")]),a("a-radio",{attrs:{value:0}},[e._v("否")])],1)],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("显示")]),a("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},d=[],u={data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{add:function(){this.title="新增记录",this.visible=!0},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,s["a"]({form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(e){a.confirmLoading=!1}))}}},c=u,m=t("2877"),p=Object(m["a"])(c,n,d,!1,null,null,null),v=p.exports,f=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"服务名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入服务与承诺的名称"}})],1),a("a-form-item",{attrs:{label:"概述",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["summary"],expression:"['summary']"}],attrs:{placeholder:"请输入概述内容 (300个字符以内)",autoSize:{minRows:4}}})],1),a("a-form-item",{attrs:{label:"是否默认",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"新增商品时是否默认勾选"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_default",{initialValue:1,rules:[{required:!0}]}],expression:"['is_default', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("是")]),a("a-radio",{attrs:{value:0}},[e._v("否")])],1)],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("显示")]),a("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},g=[],h=t("88bc"),_=t.n(h),b={data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{edit:function(e){this.title="编辑记录",this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this,a=this.form.setFieldsValue;this.$nextTick((function(){a(_()(e.record,["name","summary","is_default","status","sort"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,s["d"]({serviceId:this.record["service_id"],form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(e){a.confirmLoading=!1}))}}},C=b,w=Object(m["a"])(C,f,g,!1,null,null,null),y=w.exports,x={name:"Index",components:{STable:l["d"],AddForm:v,EditForm:y},data:function(){var e=this;return{queryParam:{},isLoading:!1,columns:[{title:"服务ID",dataIndex:"service_id"},{title:"服务名称",dataIndex:"name",width:"200px"},{title:"概述",dataIndex:"summary",width:"300px",scopedSlots:{customRender:"summary"}},{title:"是否默认",dataIndex:"is_default",scopedSlots:{customRender:"is_default"}},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"更新时间",width:"180px",dataIndex:"update_time"},{title:"操作",width:"150px",fixed:"right",scopedSlots:{customRender:"action"}}],loadData:function(a){return s["e"](Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return s["c"]({serviceId:e["service_id"]}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},S=x,k=(t("b48d"),Object(m["a"])(S,r,i,!1,null,"008d4a4b",null));a["default"]=k.exports},f11f:function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("a-form",{attrs:{form:e.form,selfUpdate:!0},on:{submit:e.handleSubmit}},[a("a-tabs",{attrs:{activeKey:e.tabKey,tabBarStyle:{marginBottom:"30px"}},on:{change:e.handleTabs}},[a("a-tab-pane",{key:0,attrs:{tab:"基本信息"}}),a("a-tab-pane",{key:1,attrs:{tab:"规格/库存"}}),a("a-tab-pane",{key:2,attrs:{tab:"商品详情"}}),a("a-tab-pane",{key:3,attrs:{tab:"更多设置"}})],1),a("div",{staticClass:"tabs-content"},[a("div",{directives:[{name:"show",rawName:"v-show",value:0==e.tabKey,expression:"tabKey == 0"}],staticClass:"tab-pane"},[a("a-form-item",{attrs:{label:"商品类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("GoodsType",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_type",{initialValue:10,rules:[{required:!0}]}],expression:"['goods_type', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate(!0)}}})],1),a("a-form-item",{attrs:{label:"商品名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['goods_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符'  }] }]"}],attrs:{placeholder:"请输入商品名称"}})],1),a("a-form-item",{attrs:{label:"商品分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["categorys",{rules:[{required:!0,message:"请至少选择1个商品分类"}]}],expression:"['categorys', { rules: [{ required: true, message: '请至少选择1个商品分类'  }] }]"}],attrs:{placeholder:"请选择商品分类",dropdownStyle:{maxHeight:"500px",overflow:"auto"},treeData:e.formData.categoryList,treeCheckable:"",treeCheckStrictly:"",allowClear:""}}),a("div",{staticClass:"form-item-help"},[a("router-link",{attrs:{target:"_blank",to:{path:"/goods/category/index"}}},[e._v("去新增")]),a("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadCategoryList}},[e._v("刷新")])],1)],1),a("a-form-item",{attrs:{label:"商品图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸：750*750像素, 最多上传10张, 可拖拽图片调整顺序, 第1张将作为商品首图"}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["imagesIds",{rules:[{required:!0,message:"请至少上传1张商品图片"}]}],expression:"['imagesIds', { rules: [{ required: true, message: '请至少上传1张商品图片'  }] }]"}],attrs:{multiple:"",maxNum:10}})],1),a("a-form-item",{attrs:{label:"商品编码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_no"],expression:"['goods_no']"}],attrs:{placeholder:"请输入商品编码"}})],1),10==e.form.getFieldValue("goods_type")?a("a-form-item",{attrs:{label:"配送方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_ind_delivery_type",{initialValue:0,rules:[{required:!0}]}],expression:"['is_ind_delivery_type', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate()}}},[a("a-radio",{attrs:{value:0}},[e._v("系统默认")]),a("a-radio",{attrs:{value:1}},[e._v("单独配置")])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_ind_delivery_type"),expression:"form.getFieldValue('is_ind_delivery_type')"}]},[a("a-form-item",{attrs:{extra:"需在 [设置 - 配送方式] 中，开启支持的配送方式才可生效"}},[a("a-checkbox-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["delivery_type",{initialValue:[10,20],rules:[{required:!0,message:"配送方式至少选择一个"}]}],expression:"['delivery_type', { initialValue: [10, 20], rules: [{ required: true, message: '配送方式至少选择一个' }] }]"}],attrs:{options:[{label:"快递配送",value:10},{label:"上门自提",value:20}]}})],1)],1)],1):e._e(),10==e.form.getFieldValue("goods_type")?a("a-form-item",{attrs:{label:"运费模板",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["delivery_id",{rules:[{required:!0,message:"请选择运费模板"}]}],expression:"['delivery_id', { rules: [{ required: true, message: '请选择运费模板'  }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请选择运费模板"}},e._l(e.formData.deliveryList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.delivery_id}},[e._v(e._s(t.name))])})),1),a("div",{staticClass:"form-item-help"},[a("router-link",{attrs:{target:"_blank",to:{path:"/setting/delivery/template/create"}}},[e._v("新增模板")]),a("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadDeliveryList}},[e._v("刷新")])],1)],1):e._e(),a("a-form-item",{attrs:{label:"商品状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:10,rules:[{required:!0}]}],expression:"['status', { initialValue: 10, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:10}},[e._v("上架")]),a("a-radio",{attrs:{value:20}},[e._v("下架")])],1)],1),a("a-form-item",{attrs:{label:"商品排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true }] }]"}],attrs:{min:0}})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:1==e.tabKey,expression:"tabKey == 1"}],staticClass:"tab-pane"},[a("a-form-item",{attrs:{label:"规格类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["spec_type",{initialValue:10,rules:[{required:!0}]}],expression:"['spec_type', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate()}}},[a("a-radio",{attrs:{value:10}},[e._v("单规格")]),a("a-radio",{attrs:{value:20}},[e._v("多规格")])],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:20==e.form.getFieldValue("spec_type"),expression:"form.getFieldValue('spec_type') == 20"}]},[a("MultiSpec",{ref:"MultiSpec"})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:10==e.form.getFieldValue("spec_type"),expression:"form.getFieldValue('spec_type') == 10"}]},[a("a-form-item",{attrs:{label:"商品价格",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际购买金额，最低0.01"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_price",{initialValue:1,rules:[{required:!0,message:"请输入商品价格"}]}],expression:"['goods_price', { initialValue: 1, rules: [{ required: true, message: '请输入商品价格' }] }]"}],attrs:{min:.01,precision:2}}),a("span",{staticClass:"ml-10"},[e._v("元")])],1),a("a-form-item",{attrs:{label:"划线价",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"划线价仅用于商品页展示"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["line_price"],expression:"['line_price']"}],attrs:{min:0,precision:2}}),a("span",{staticClass:"ml-10"},[e._v("元")])],1),a("a-form-item",{attrs:{label:"当前库存数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际库存数量，为0时用户无法下单"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["stock_num",{initialValue:100,rules:[{required:!0,message:"请输入库存数量"}]}],expression:"['stock_num', { initialValue: 100, rules: [{ required: true, message: '请输入库存数量' }] }]"}],attrs:{min:0,precision:0}}),a("span",{staticClass:"ml-10"},[e._v("件")])],1),a("a-form-item",{attrs:{label:"商品重量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际重量，用于计算运费"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_weight",{initialValue:0,rules:[{required:!0,message:"请输入库存数量"}]}],expression:"['goods_weight', { initialValue: 0, rules: [{ required: true, message: '请输入库存数量' }] }]"}],attrs:{min:0}}),a("span",{staticClass:"ml-10"},[e._v("千克 (Kg)")])],1)],1),a("a-form-item",{attrs:{label:"库存计算方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["deduct_stock_type",{initialValue:10,rules:[{required:!0}]}],expression:"['deduct_stock_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:10}},[e._v("下单减库存")]),a("a-radio",{attrs:{value:20}},[e._v("付款减库存")])],1)],1),a("a-form-item",{attrs:{label:"商品限购",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用于限制每人购买该商品的数量"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_restrict",{initialValue:0,rules:[{required:!0}]}],expression:"['is_restrict', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate()}}},[a("a-radio",{attrs:{value:0}},[e._v("关闭")]),a("a-radio",{attrs:{value:1}},[e._v("开启")])],1),e.form.getFieldValue("is_restrict")?a("div",{staticClass:"mt-10"},[a("a-form-item",[a("span",{staticClass:"mr-10"},[e._v("总限购")]),a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_total",{rules:[{required:!0,message:"请输入总限购数量"}]}],expression:"['restrict_total', { rules: [{ required: true, message: '请输入总限购数量' }] }]"}],attrs:{min:1,precision:0}}),a("span",{staticClass:"ml-10"},[e._v("件/人")])],1),a("a-form-item",[a("span",{staticClass:"mr-10"},[e._v("每单限购")]),a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_single",{rules:[{required:!0,message:"请输入每单限购数量"}]}],expression:"['restrict_single', { rules: [{ required: true, message: '请输入每单限购数量' }] }]"}],attrs:{min:1,precision:0}}),a("span",{staticClass:"ml-10"},[e._v("件/人")])],1)],1):e._e()],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:2==e.tabKey,expression:"tabKey == 2"}],staticClass:"tab-pane"},[a("a-form-item",{attrs:{label:"商品详情",labelCol:e.labelCol,wrapperCol:{span:16}}},[a("Ueditor",{directives:[{name:"decorator",rawName:"v-decorator",value:["content",{rules:[{required:!0,message:"商品详情不能为空"}]}],expression:"['content', { rules: [{ required: true, message: '商品详情不能为空'  }] }]"}]})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:3==e.tabKey,expression:"tabKey == 3"}],staticClass:"tab-pane"},[a("a-form-item",{attrs:{label:"主图视频",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议视频宽高比19:9，建议时长8-45秒"}},[a("SelectVideo",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_id"],expression:"['video_id']"}],attrs:{multiple:!1}})],1),a("a-form-item",{attrs:{label:"视频封面",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸：750像素*750像素"}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_cover_id"],expression:"['video_cover_id']"}],attrs:{multiple:!1}})],1),a("a-form-item",{attrs:{label:"商品卖点",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"一句话简述，例如：此款商品美观大方 性价比较高 不容错过"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["selling_point"],expression:"['selling_point']"}],attrs:{placeholder:"请输入商品卖点"}})],1),a("a-form-item",{attrs:{label:"服务与承诺",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[e.formData.serviceList?a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["serviceIds",{initialValue:e.formData.defaultServiceIds}],expression:"['serviceIds', { initialValue: formData.defaultServiceIds }]"}],attrs:{mode:"multiple",placeholder:"请选择服务与承诺"}},e._l(e.formData.serviceList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.service_id}},[e._v(e._s(t.name))])})),1):e._e(),a("div",{staticClass:"form-item-help"},[a("router-link",{attrs:{target:"_blank",to:{path:"/goods/service/index"}}},[e._v("去新增")]),a("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadServiceList}},[e._v("刷新")])],1)],1),a("a-form-item",{attrs:{label:"初始销量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端展示的销量 = 初始销量 + 实际销量"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sales_initial",{initialValue:0}],expression:"['sales_initial', { initialValue: 0}]"}]})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("market-points"),expression:"$module('market-points')"}]},[a("a-divider",{attrs:{orientation:"left"}},[e._v("积分设置")]),a("a-form-item",{attrs:{label:"积分赠送",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后用户购买此商品将获得积分"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_points_gift",{initialValue:1,rules:[{required:!0}]}],expression:"['is_points_gift', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("开启")]),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),a("a-form-item",{attrs:{label:"积分抵扣",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后用户购买此商品可以使用积分进行抵扣"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_points_discount",{initialValue:1,rules:[{required:!0}]}],expression:"['is_points_discount', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("开启")]),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("user-grade"),expression:"$module('user-grade')"}]},[a("a-divider",{attrs:{orientation:"left"}},[e._v("会员折扣设置")]),a("a-form-item",{attrs:{label:"会员折扣",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后会员折扣，会员购买此商品可以享受会员等级折扣价"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_enable_grade",{initialValue:1,rules:[{required:!0}]}],expression:"['is_enable_grade', { initialValue: 1, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate(!0)}}},[a("a-radio",{attrs:{value:1}},[e._v("开启")]),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),e.form.getFieldValue("is_enable_grade")?a("a-form-item",{attrs:{label:"会员折扣设置",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_alone_grade",{initialValue:0,rules:[{required:!0}]}],expression:"['is_alone_grade', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate(!0)}}},[a("a-radio",{attrs:{value:0}},[e._v("默认等级折扣")]),a("a-radio",{attrs:{value:1}},[e._v("单独设置折扣")])],1),e.form.getFieldValue("is_alone_grade")?a("div",e._l(e.formData.userGradeList,(function(t){return a("a-form-item",{key:t.grade_id},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["alone_grade_equity[grade_id:".concat(t.grade_id,"]"),{initialValue:e.formData.defaultUserGradeValue[t.grade_id],rules:[{required:!0,message:"折扣率不能为空"}]}],expression:"[`alone_grade_equity[grade_id:${item.grade_id}]`, {\n                    initialValue: formData.defaultUserGradeValue[item.grade_id], rules: [{ required: true, message: '折扣率不能为空' }]\n                  }]"}],attrs:{addonBefore:t.name,addonAfter:"折",inputProps:{min:0,max:9.9}}})],1)})),1):e._e(),a("div",{staticClass:"form-item-help"},[e.form.getFieldValue("is_alone_grade")?a("p",{staticClass:"extra"},[e._v("单独折扣：折扣率范围0.0-9.9，例如: 9.8代表98折，0代表不折扣")]):a("p",{staticClass:"extra"},[e._v("默认折扣：默认为用户所属会员等级的折扣率")])])],1):e._e()],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("apps-dealer"),expression:"$module('apps-dealer')"}]},[a("a-divider",{attrs:{orientation:"left"}},[e._v("分销设置")]),a("a-form-item",{attrs:{label:"分销佣金",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_ind_dealer",{initialValue:0,rules:[{required:!0}]}],expression:"['is_ind_dealer', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate(!0)}}},[a("a-radio",{attrs:{value:0}},[e._v("系统默认")]),a("a-radio",{attrs:{value:1}},[e._v("单独设置")])],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("若使用分销功能必须在 [分销中心 - 分销设置] 中开启")])])],1),e.form.getFieldValue("is_ind_dealer")?a("a-form-item",{attrs:{label:"分销佣金类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["dealer_money_type",{initialValue:10,rules:[{required:!0}]}],expression:"['dealer_money_type', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:function(a){return e.onForceUpdate(!0)}}},[a("a-radio",{attrs:{value:10}},[e._v("百分比")]),a("a-radio",{attrs:{value:20}},[e._v("固定金额")])],1),e._l(e.formData.dealer.levelList,(function(t,r){return a("a-form-item",{key:r},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:[t.value,{rules:[{required:!0,message:"佣金不能为空"}]}],expression:"[item.value, { rules: [{ required: true, message: '佣金不能为空' }] }]"}],attrs:{addonBefore:t.name,addonAfter:10==e.form.getFieldValue("dealer_money_type")?"%":"元",inputProps:{min:0,precision:2}}})],1)}))],2):e._e()],1)],1)]),a("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[a("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1)],1)],1)},i=[],o=(t("d81d"),t("b64b"),t("d3b7"),t("d084")),s=t("2af9"),l=t("e1fe"),n=t("b78d"),d={components:{GoodsType:n["a"],SelectImage:s["h"],SelectVideo:s["m"],Ueditor:s["n"],InputNumberGroup:s["c"],MultiSpec:n["b"]},data:function(){return{tabKey:0,labelCol:{span:3},wrapperCol:{span:10},isLoading:!1,isBtnLoading:!1,form:this.$form.createForm(this),formData:l["a"].formData}},created:function(){var e=this;this.isLoading=!0,l["a"].getFromData().then((function(){e.isLoading=!1}))},methods:{onForceUpdate:function(){var e=this,a=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$forceUpdate(),a&&setTimeout((function(){e.$forceUpdate()}),10)},handleTabs:function(e){this.tabKey=e},onReloadCategoryList:function(){var e=this;this.isLoading=!0,l["a"].getCategoryList().then((function(){e.isLoading=!1}))},onReloadServiceList:function(){var e=this;this.isLoading=!0,l["a"].getServiceList().then((function(){e.isLoading=!1}))},onReloadDeliveryList:function(){var e=this;this.isLoading=!0,l["a"].getDeliveryList().then((function(){e.isLoading=!1}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){if(e)return a.onTargetTabError(e),!1;if(20===t.spec_type){var r=a.$refs.MultiSpec;if(!r.verifyForm())return a.tabKey=1,!1;t.specData=r.getFromSpecData()}return t.categoryIds=t.categorys.map((function(e){return e.value})),delete t.categorys,a.onFormSubmit(t),!0}))},onTargetTabError:function(e){var a=[["goods_type","goods_name","categorys","imagesIds","delivery_id","is_ind_delivery_type","delivery_type"],["spec_type","goods_price","is_restrict","restrict_total","restrict_single"],["content"],["alone_grade_equity","first_money","second_money","third_money"]],t=Object.keys(e).shift();for(var r in a)if(a[r].indexOf(t)>-1){this.tabKey=parseInt(r);break}},onFormSubmit:function(e){var a=this;this.isLoading=!0,this.isBtnLoading=!0,o["a"]({form:e}).then((function(e){a.$message.success(e.message,1.5),setTimeout((function(){a.$router.push("./index")}),1500)})).catch((function(){a.isBtnLoading=!1})).finally((function(){a.isLoading=!1}))}}},u=d,c=(t("f233"),t("2877")),m=Object(c["a"])(u,r,i,!1,null,"7a8abda6",null);a["default"]=m.exports},f233:function(e,a,t){"use strict";t("22a0")},fe55:function(e,a,t){}}]);