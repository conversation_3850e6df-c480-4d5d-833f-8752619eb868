<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\dealer\apply;

use app\common\enum\EnumBasics;

/**
 * 枚举类：分销商申请方式
 * Class ApplyType
 * @package app\common\enum\dealer\apply
 */
class ApplyType extends EnumBasics
{
    // 需后台审核
    const AUDIT = 10;

    // 无需审核
    const PASS = 20;

    /**
     * 获取枚举类型值
     * @return array
     */
    public static function data(): array
    {
        return [
            self::AUDIT => [
                'name' => '需后台审核',
                'value' => self::AUDIT,
            ],
            self::PASS => [
                'name' => '无需审核',
                'value' => self::PASS,
            ]
        ];
    }
}