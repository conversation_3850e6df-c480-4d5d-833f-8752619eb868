(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["store"],{"0c84":function(e,a,t){"use strict";t("de94")},1708:function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[a("a-form-item",{attrs:{label:"门店名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["shop_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['shop_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入门店名称"}})],1),a("a-form-item",{attrs:{label:"门店Logo",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸: 300*300",required:""}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["logo_image_id",{rules:[{required:!0,message:"请上传门店logo"}]}],expression:"['logo_image_id', { rules: [{ required: true, message: '请上传门店logo' }] }]"}],attrs:{defaultList:e.record.logoImage?[e.record.logoImage]:[]}})],1),a("a-form-item",{attrs:{label:"联系人",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["linkman",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['linkman', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入联系人"}})],1),a("a-form-item",{attrs:{label:"联系电话",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["phone",{rules:[{required:!0}]}],expression:"['phone', { rules: [{ required: true }] }]"}],attrs:{placeholder:"请输入联系电话"}})],1),a("a-form-item",{attrs:{label:"营业时间",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"例如：8:30-17:30"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["shop_hours",{rules:[{required:!0}]}],expression:"['shop_hours', { rules: [{ required: true }] }]"}],attrs:{placeholder:"请输入营业时间"}})],1),a("a-form-item",{attrs:{label:"门店区域",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectRegion",{directives:[{name:"decorator",rawName:"v-decorator",value:["cascader",{rules:[{required:!0,message:"请选择省市区"}]}],expression:"['cascader', { rules: [{ required: true, message: '请选择省市区' }] }]"}],attrs:{placeholder:"请选择省市区"}})],1),a("a-form-item",{attrs:{label:"详细地址",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["address",{rules:[{required:!0}]}],expression:"['address', { rules: [{ required: true }] }]"}],attrs:{placeholder:"请输入详细地址"}})],1),a("a-form-item",{attrs:{label:"门店坐标",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["coordinate",{rules:[{required:!0}]}],expression:"['coordinate', { rules: [{ required: true }] }]"}],attrs:{placeholder:"请选择门店坐标"}}),a("Getpoint",{on:{setCoordinate:e.setCoordinate}})],1),a("a-form-item",{attrs:{label:"门店简介",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["summary"],expression:"['summary']"}],attrs:{autoSize:{minRows:4}}})],1),a("a-form-item",{attrs:{label:"自提核销",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_check",{initialValue:1,rules:[{required:!0}]}],expression:"['is_check', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("支持")]),a("a-radio",{attrs:{value:0}},[e._v("不支持")])],1)],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("启用")]),a("a-radio",{attrs:{value:0}},[e._v("禁用")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1),a("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[a("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1)],1)],1)},i=[],o=(t("d3b7"),t("88bc")),s=t.n(o),l=t("3858"),n=t("ca00"),d=t("2af9"),c={components:{SelectImage:d["h"],SelectRegion:d["j"],Getpoint:d["b"]},data:function(){return{isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},form:this.$form.createForm(this),shopId:null,record:{}}},created:function(){this.shopId=this.$route.query.shopId,this.getDetail()},methods:{getDetail:function(){var e=this,a=this.shopId;this.isLoading=!0,l["d"]({shopId:a}).then((function(a){e.record=a.data.detail,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,a=this.form,t=this.$nextTick;!Object(n["g"])(a.getFieldsValue())&&t((function(){a.setFieldsValue(s()(e,["shop_name","logo_image_id","linkman","phone","shop_hours","address","summary","cascader","coordinate","sort","is_check","status"]))}))},setCoordinate:function(e){var a=this.form,t=this.$nextTick;t((function(){a.setFieldsValue({coordinate:e})}))},handleSubmit:function(e){e.preventDefault();var a=this.form.validateFields,t=this.onFormSubmit;a((function(e,a){!e&&t(a)}))},onFormSubmit:function(e){var a=this;this.isLoading=!0,this.isBtnLoading=!0,l["e"]({shopId:this.shopId,form:e}).then((function(e){a.$message.success(e.message,1.5),setTimeout((function(){a.$router.push("./index")}),1500)})).catch((function(){a.isBtnLoading=!1})).finally((function(){return a.isLoading=!1}))}}},u=c,m=t("2877"),p=Object(m["a"])(u,r,i,!1,null,null,null);a["default"]=p.exports},"1d36":function(e,a,t){"use strict";t.r(a);t("ac1f"),t("841c");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-row",[a("a-col",{staticClass:"flex flex-x-end"},[a("a-select",{staticStyle:{width:"220px","margin-right":"20px"},attrs:{placeholder:"所属门店"},model:{value:e.queryParam.shop_id,callback:function(a){e.$set(e.queryParam,"shop_id",a)},expression:"queryParam.shop_id"}},[a("a-select-option",{attrs:{value:0}},[e._v("全部")]),e._l(e.shopList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.shop_id}},[e._v(e._s(t.shop_name))])}))],2),a("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px"},attrs:{placeholder:"请输入姓名/联系电话"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(a){e.$set(e.queryParam,"search",a)},expression:"queryParam.search"}})],1)],1)],1),a("s-table",{ref:"table",attrs:{rowKey:"id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"shop",fn:function(t){return a("span",{},[e._v(e._s(t.shop_name))])}},{key:"clerk",fn:function(t){return a("span",{},[e._v(e._s(t.real_name))])}},{key:"order",fn:function(t){return a("span",{},[e._v(e._s(t.order_no))])}}])})],1)},i=[],o=t("5530"),s=t("b775"),l={list:"/shop.order/list"};function n(e){return Object(s["b"])({url:l.list,method:"get",params:e})}var d=t("3858"),c=t("2af9"),u={name:"Index",components:{STable:c["d"]},data:function(){var e=this;return{queryParam:{},isLoading:!1,columns:[{title:"记录ID",dataIndex:"id"},{title:"核销门店",dataIndex:"shop",scopedSlots:{customRender:"shop"}},{title:"核销员",dataIndex:"clerk",scopedSlots:{customRender:"clerk"}},{title:"订单号",dataIndex:"order",scopedSlots:{customRender:"order"}},{title:"核销时间",dataIndex:"create_time"}],loadData:function(a){return n(Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))},shopList:[]}},created:function(){this.getShopList()},methods:{getShopList:function(){var e=this;d["b"]().then((function(a){e.shopList=a.data.list}))},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},m=u,p=t("2877"),f=Object(p["a"])(m,r,i,!1,null,null,null);a["default"]=f.exports},"35c4":function(e,a,t){"use strict";t.d(a,"a",(function(){return i}));var r=t("5c06"),i=new r["a"]([{key:"DELIVERY",name:"配送设置",value:"delivery"},{key:"TRADE",name:"交易设置",value:"trade"},{key:"STORAGE",name:"上传设置",value:"storage"},{key:"PRINTER",name:"小票打印",value:"printer"},{key:"FULL_FREE",name:"满额包邮设置",value:"full_free"},{key:"RECHARGE",name:"充值设置",value:"recharge"},{key:"POINTS",name:"积分设置",value:"points"},{key:"SUBMSG",name:"订阅消息设置",value:"submsg"},{key:"APP_THEME",name:"店铺页面风格",value:"app_theme"},{key:"PAGE_CATEGORY_TEMPLATE",name:"分类页模板",value:"page_category_template"},{key:"RECOMMENDED",name:"商品推荐设置",value:"recommended"},{key:"CUSTOMER",name:"商城客服设置",value:"customer"}])},"4a3c":function(e,a,t){"use strict";t.r(a);t("ac1f"),t("841c");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-row",[a("a-col",{attrs:{span:5}},[a("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("a-col",{staticClass:"flex flex-x-end",attrs:{span:11,offset:8}},[a("a-select",{staticStyle:{width:"220px","margin-right":"20px"},attrs:{placeholder:"所属门店"},model:{value:e.queryParam.shop_id,callback:function(a){e.$set(e.queryParam,"shop_id",a)},expression:"queryParam.shop_id"}},[a("a-select-option",{attrs:{value:0}},[e._v("全部")]),e._l(e.shopList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.shop_id}},[e._v(e._s(t.shop_name))])}))],2),a("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px"},attrs:{placeholder:"请输入店员姓名/手机号"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(a){e.$set(e.queryParam,"search",a)},expression:"queryParam.search"}})],1)],1)],1),a("s-table",{ref:"table",attrs:{rowKey:"clerk_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"user",fn:function(e){return a("span",{},[a("UserItem",{attrs:{user:e}})],1)}},{key:"shop",fn:function(t){return a("span",{},[a("a-tag",[e._v(e._s(t.shop_name))])],1)}},{key:"status",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:t?"green":""}},[e._v(e._s(t?"启用":"禁用"))])],1)}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(a){return e.handleEdit(r)}}},[e._v("编辑")]),a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),a("AddForm",{ref:"AddForm",attrs:{shopList:e.shopList},on:{handleSubmit:e.handleRefresh}}),a("EditForm",{ref:"EditForm",attrs:{shopList:e.shopList},on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=t("5530"),s=(t("d3b7"),t("6dd2")),l=t("3858"),n=t("ab09"),d=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"所属门店",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"请选择店员所属的门店，用于核销订单"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["shop_id",{rules:[{required:!0,message:"请选择所属门店"}]}],expression:"['shop_id', { rules: [{ required: true, message: '请选择所属门店'}] }]"}]},e._l(e.shopList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.shop_id}},[e._v(e._s(t.shop_name))])})),1)],1),a("a-form-item",{attrs:{label:"会员ID",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"请输入店员的会员ID, 确认后不可更改"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["user_id",{rules:[{required:!0,message:"请输入店员的会员ID"}]}],expression:"['user_id', { rules: [{ required: true, message: '请输入店员的会员ID' }] }]"}],attrs:{min:1}})],1),a("a-form-item",{attrs:{label:"店员姓名",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["real_name",{rules:[{required:!0,message:"请输入店员姓名"}]}],expression:"['real_name', { rules: [{ required: true, message: '请输入店员姓名' }] }]"}]})],1),a("a-form-item",{attrs:{label:"手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["mobile",{rules:[{required:!0,message:"请输入手机号"}]}],expression:"['mobile', { rules: [{ required: true, message: '请输入手机号' }] }]"}]})],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("启用")]),a("a-radio",{attrs:{value:0}},[e._v("禁用")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},c=[],u={components:{},props:{shopList:{type:Array,required:!0}},data:function(){return{title:"新增店员",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{add:function(){this.visible=!0},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,s["a"]({form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(){return a.confirmLoading=!1}))}}},m=u,p=t("2877"),f=Object(p["a"])(m,d,c,!1,null,null,null),h=f.exports,v=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"所属门店",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"请选择店员所属的门店，用于核销订单"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["shop_id",{rules:[{required:!0,message:"请选择所属门店"}]}],expression:"['shop_id', { rules: [{ required: true, message: '请选择所属门店'}] }]"}]},e._l(e.shopList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.shop_id}},[e._v(e._s(t.shop_name))])})),1)],1),a("a-form-item",{attrs:{label:"店员姓名",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["real_name",{rules:[{required:!0,message:"请输入店员姓名"}]}],expression:"['real_name', { rules: [{ required: true, message: '请输入店员姓名' }] }]"}]})],1),a("a-form-item",{attrs:{label:"手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["mobile",{rules:[{required:!0,message:"请输入手机号"}]}],expression:"['mobile', { rules: [{ required: true, message: '请输入手机号' }] }]"}]})],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("启用")]),a("a-radio",{attrs:{value:0}},[e._v("禁用")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},b=[],g=t("88bc"),C=t.n(g),_={components:{},props:{shopList:{type:Array,required:!0}},data:function(){return{title:"编辑店员",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{edit:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this.record,a=this.form.setFieldsValue;this.$nextTick((function(){a(C()(e,["shop_id","real_name","mobile","status"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,s["d"]({clerkId:this.record.clerk_id,form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(){return a.confirmLoading=!1}))}}},w=_,x=Object(p["a"])(w,v,b,!1,null,null,null),y=x.exports,q={name:"Index",components:{STable:n["b"],UserItem:n["c"],AddForm:h,EditForm:y},data:function(){var e=this;return{queryParam:{},isLoading:!1,columns:[{title:"店员ID",dataIndex:"clerk_id"},{title:"会员信息",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"所属门店",dataIndex:"shop",scopedSlots:{customRender:"shop"}},{title:"店员姓名",dataIndex:"real_name"},{title:"手机号",dataIndex:"mobile"},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(a){return s["e"](Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))},shopList:[]}},created:function(){this.getShopList()},methods:{getShopList:function(){var e=this;l["b"]().then((function(a){e.shopList=a.data.list}))},handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return s["c"]({clerkId:e.clerk_id}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},k=q,S=Object(p["a"])(k,r,i,!1,null,null,null);a["default"]=S.exports},"4d55":function(e,a,t){"use strict";t.d(a,"e",(function(){return o})),t.d(a,"b",(function(){return s})),t.d(a,"a",(function(){return l})),t.d(a,"d",(function(){return n})),t.d(a,"c",(function(){return d}));var r=t("b775"),i={list:"/store.address/list",all:"/store.address/all",add:"/store.address/add",edit:"/store.address/edit",delete:"/store.address/delete"};function o(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function s(e){return Object(r["b"])({url:i.all,method:"get",params:e})}function l(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function n(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function d(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},"5d1d":function(e,a,t){},"6dd2":function(e,a,t){"use strict";t.d(a,"e",(function(){return o})),t.d(a,"b",(function(){return s})),t.d(a,"a",(function(){return l})),t.d(a,"d",(function(){return n})),t.d(a,"c",(function(){return d}));var r=t("b775"),i={list:"/shop.clerk/list",all:"/shop.clerk/all",add:"/shop.clerk/add",edit:"/shop.clerk/edit",delete:"/shop.clerk/delete"};function o(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function s(e){return Object(r["b"])({url:i.all,method:"get",params:e})}function l(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function n(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function d(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},"89a2":function(e,a,t){"use strict";t.d(a,"d",(function(){return o})),t.d(a,"a",(function(){return s})),t.d(a,"c",(function(){return l})),t.d(a,"b",(function(){return n}));var r=t("b775"),i={list:"/content.article.category/list",add:"/content.article.category/add",edit:"/content.article.category/edit",delete:"/content.article.category/delete"};function o(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function s(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function l(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function n(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},"90ac":function(e,a,t){},"976c":function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[a("a-form-item",{attrs:{label:"商城名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["store_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['store_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),a("a-form-item",{attrs:{label:"商城简介",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["describe"],expression:"['describe']"}]})],1),a("a-form-item",{attrs:{label:"商城Logo",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸: 300*300"}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["logo_image_id"],expression:"['logo_image_id']"}],attrs:{defaultList:e.record.logoImage?[e.record.logoImage]:[]}})],1),a("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[a("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},i=[],o=(t("d3b7"),t("88bc")),s=t.n(o),l=t("33ca"),n=t("2af9"),d={components:{SelectImage:n["h"]},data:function(){return{labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),record:{}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,l["a"]().then((function(a){e.record=a.data.storeInfo,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,a=this.form.setFieldsValue;this.$nextTick((function(){a(s()(e,["store_name","describe","logo_image_id"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},onFormSubmit:function(e){var a=this;this.isLoading=!0,l["b"]({form:e}).then((function(e){return a.$message.success(e.message,1.5)})).finally((function(){return a.isLoading=!1}))}}},c=d,u=(t("9a30"),t("2877")),m=Object(u["a"])(c,r,i,!1,null,"7c474c8b",null);a["default"]=m.exports},"9a30":function(e,a,t){"use strict";t("5d1d")},a61f:function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v("主题风格")]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("div",{staticClass:"container"},[a("div",{staticClass:"form-box"},[a("a-form-model",{ref:"myForm",staticClass:"my-form",attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-form-model-item",{attrs:{label:"主题色系"}},[a("a-radio-group",{model:{value:e.record.mode,callback:function(a){e.$set(e.record,"mode",a)},expression:"record.mode"}},[a("a-radio",{attrs:{value:10}},[e._v("系统推荐")]),a("a-radio",{attrs:{value:20}},[e._v("自定义")])],1)],1),10==e.record.mode?a("a-form-model-item",{attrs:{label:"选择色系"}},[a("div",{staticClass:"color-radio-group"},e._l(e.themeTemplate,(function(t,r){return a("div",{key:r,staticClass:"color-radio-item",style:{backgroundColor:t.mainBg},on:{click:function(a){return e.handleColorRadio(r,t)}}},[e.record.themeTemplateIdx===r?a("a-icon",{attrs:{type:"check"}}):e._e()],1)})),0)]):e._e(),20==e.record.mode?a("div",[a("a-form-model-item",{attrs:{label:"按钮颜色渐变"}},[a("a-radio-group",{on:{change:function(a){return e.onGradualChangeData()}},model:{value:e.record.data.gradualChange,callback:function(a){e.$set(e.record.data,"gradualChange",a)},expression:"record.data.gradualChange"}},[a("a-radio",{attrs:{value:1}},[e._v("开启")]),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),a("a-form-model-item",{attrs:{label:"主背景"}},[a("div",{staticClass:"color-picker"},[a("colorPicker",{attrs:{defaultColor:"#fff"},model:{value:e.record.data.mainBg,callback:function(a){e.$set(e.record.data,"mainBg",a)},expression:"record.data.mainBg"}}),e.record.data.gradualChange?a("colorPicker",{attrs:{defaultColor:"#fff"},model:{value:e.record.data.mainBg2,callback:function(a){e.$set(e.record.data,"mainBg2",a)},expression:"record.data.mainBg2"}}):e._e()],1)]),a("a-form-model-item",{attrs:{label:"主文字"}},[a("div",{staticClass:"color-picker"},[a("colorPicker",{attrs:{defaultColor:"#fff"},model:{value:e.record.data.mainText,callback:function(a){e.$set(e.record.data,"mainText",a)},expression:"record.data.mainText"}})],1)]),a("a-form-model-item",{attrs:{label:"副背景"}},[a("div",{staticClass:"color-picker"},[a("colorPicker",{attrs:{defaultColor:"#fff"},model:{value:e.record.data.viceBg,callback:function(a){e.$set(e.record.data,"viceBg",a)},expression:"record.data.viceBg"}}),e.record.data.gradualChange?a("colorPicker",{attrs:{defaultColor:"#fff"},model:{value:e.record.data.viceBg2,callback:function(a){e.$set(e.record.data,"viceBg2",a)},expression:"record.data.viceBg2"}}):e._e()],1)]),a("a-form-model-item",{attrs:{label:"副文字"}},[a("div",{staticClass:"color-picker"},[a("colorPicker",{attrs:{defaultColor:"#fff"},model:{value:e.record.data.viceText,callback:function(a){e.$set(e.record.data,"viceText",a)},expression:"record.data.viceText"}})],1)])],1):e._e(),a("a-form-model-item",{attrs:{wrapperCol:{offset:e.labelCol.span}}},[a("a-button",{attrs:{type:"primary",loading:e.confirmLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1),a("div",{staticClass:"preview",style:e.appThemeStyle},[a("div",{staticClass:"example-item"},[a("div",{staticClass:"example-item-scale"},[a("img",{staticClass:"bg-image",attrs:{src:"static/img/theme/10.png"}}),a("div",{staticClass:"content"},[a("span",{staticClass:"goods-price"},[a("span",{staticClass:"unit"},[e._v("￥")]),a("span",{staticClass:"value"},[e._v("4969.00")])]),a("div",{staticClass:"btn-wrapper"},[a("div",{staticClass:"btn-item btn-item-deputy"},[a("span",[e._v("加入购物车")])]),a("div",{staticClass:"btn-item btn-item-main"},[a("span",[e._v("立即购买")])])])])])]),a("div",{staticClass:"example-item"},[a("div",{staticClass:"example-item-scale"},[a("img",{staticClass:"bg-image",attrs:{src:"static/img/theme/20.png"}}),a("div",{staticClass:"content"},[a("span",{staticClass:"goods-price"},[a("span",{staticClass:"unit"},[e._v("￥")]),a("span",{staticClass:"value"},[e._v("4969.00")])]),a("div",{staticClass:"item-content"},[a("span",[e._v("墨影灰")])]),a("div",{staticClass:"btn-wrapper"},[a("div",{staticClass:"btn-item btn-item-main"},[a("span",[e._v("立即购买")])])])])])]),a("div",{staticClass:"example-item"},[a("div",{staticClass:"example-item-scale"},[a("img",{staticClass:"bg-image",attrs:{src:"static/img/theme/30.png"}}),a("div",{staticClass:"content"},[a("div",{staticClass:"swiper-tab"},[a("div",{staticClass:"swiper-tab-item"},[e._v("快递配送")])]),a("div",{staticClass:"flow-cont1"},[e._v("￥4969.00")]),a("div",{staticClass:"flow-cont2"},[e._v("￥4969.00")]),a("div",{staticClass:"flow-cont3"},[e._v("+￥0.00")]),a("div",{staticClass:"flow-cont4"},[e._v("￥4969.00")]),a("div",{staticClass:"flow-btn"},[e._v("提交订单")])])])])])])])],1)},i=[],o=(t("ac1f"),t("5319"),t("d3b7"),t("ddb0"),t("2b0e")),s=t("a9f5"),l=t.n(s),n=t("2ef0"),d=t("f585"),c=t("35c4"),u=(t("b0c0"),function(){var e=this,a=e._self._c;return a("a-spin",{attrs:{spinning:e.isLoading}},[a("a-select",{on:{change:e.onChange},model:{value:e.selectedId,callback:function(a){e.selectedId=a},expression:"selectedId"}},[a("a-select-option",{attrs:{value:-1}},[e._v("全部")]),e._l(e.categoryList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.category_id}},[e._v(e._s(t.name))])}))],2)],1)}),m=[],p=t("4d91"),f=t("89a2"),h={name:"SArticleCate",components:{},model:{prop:"value",event:"change"},props:{value:p["a"].integer.def(-1)},data:function(){return{isLoading:!1,categoryList:[],selectedId:-1}},watch:{value:{immediate:!0,handler:function(e){this.selectedId=e}}},created:function(){this.getCategoryList()},methods:{getCategoryList:function(){var e=this;this.isLoading=!0,f["d"]().then((function(a){e.categoryList=a.data.list})).finally((function(){return e.isLoading=!1}))},onChange:function(e){this.$emit("change",e)}}},v=h,b=(t("0c84"),t("2877")),g=Object(b["a"])(v,u,m,!1,null,"ca8435fc",null),C=g.exports;o["a"].use(l.a);var _={mode:10,themeTemplateIdx:-1,data:{gradualChange:0,mainBg:"#fa2209",mainBg2:"#ff6335",mainText:"#ffffff",viceBg:"#ffb100",viceBg2:"#ffb900",viceText:"#ffffff"}},w=[{mainBg:"#fa2209",mainBg2:"#ff6335",mainText:"#ffffff",viceBg:"#ffb100",viceBg2:"#ffb900",viceText:"#ffffff",gradualChange:1},{mainBg:"#ff547b",mainBg2:"#ff547b",mainText:"#ffffff",viceBg:"#FFE6E8",viceBg2:"#FFE6E8",viceText:"#ff547b",gradualChange:0},{mainBg:"#63be72",mainBg2:"#63be72",mainText:"#ffffff",viceBg:"#E1F4E3",viceBg2:"#E1F4E3",viceText:"#50be58",gradualChange:0},{mainBg:"#c3a769",mainBg2:"#c3a769",mainText:"#ffffff",viceBg:"#F3EEE1",viceBg2:"#F3EEE1",viceText:"#C3A769",gradualChange:0},{mainBg:"#2f2f34",mainBg2:"#2f2f34",mainText:"#ffffff",viceBg:"#EBECF2",viceBg2:"#EBECF2",viceText:"#2F2F34",gradualChange:0},{mainBg:"#884cff",mainBg2:"#884cff",mainText:"#ffffff",viceBg:"#EFE6FF",viceBg2:"#EFE6FF",viceText:"#884cff",gradualChange:0},{mainBg:"#65c4aa",mainBg2:"#65c4aa",mainText:"#ffffff",viceBg:"#D9F6EF",viceBg2:"#D9F6EF",viceText:"#65c4aa",gradualChange:0},{mainBg:"#FCC600",mainBg2:"#FCC600",mainText:"#ffffff",viceBg:"#1D262E",viceBg2:"#1D262E",viceText:"#ffffff",gradualChange:0},{mainBg:"#4a90e2",mainBg2:"#4a90e2",mainText:"#ffffff",viceBg:"#D6E9FC",viceBg2:"#D6E9FC",viceText:"#0080FF",gradualChange:0}],x=function(e){return e.replace(/([A-Z])/g,"-$1").toLowerCase()},y={components:{ColorRadio:C},data:function(){return{key:c["a"].APP_THEME.value,labelCol:{span:2},wrapperCol:{span:22},isLoading:!1,confirmLoading:!1,themeTemplate:w,record:Object(n["cloneDeep"])(_)}},computed:{appThemeStyle:function(){var e=this.record.data,a={};for(var t in e){var r=x(t);a["--".concat(r)]=e[t]}return a}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,d["a"](this.key).then((function(a){return e.record=a.data.values})).finally((function(){return e.isLoading=!1}))},handleColorRadio:function(e,a){this.record.themeTemplateIdx=e,this.record.data=Object(n["cloneDeep"])(a)},onGradualChangeData:function(){this.record.data.gradualChange||(this.record.data.mainBg2=this.record.data.mainBg,this.record.data.viceBg2=this.record.data.viceBg)},handleSubmit:function(e){var a=this;this.confirmLoading=!0,d["b"](this.key,{form:this.record}).then((function(e){return a.$message.success(e.message,1.5)})).finally((function(e){return a.confirmLoading=!1}))}}},q=y,k=(t("ba35"),Object(b["a"])(q,r,i,!1,null,"5a8a70b4",null));a["default"]=k.exports},ba35:function(e,a,t){"use strict";t("90ac")},c0e3:function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[a("a-form-item",{attrs:{label:"门店名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["shop_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['shop_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入门店名称"}})],1),a("a-form-item",{attrs:{label:"门店Logo",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸: 300*300",required:""}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["logo_image_id",{rules:[{required:!0,message:"请上传门店logo"}]}],expression:"['logo_image_id', { rules: [{ required: true, message: '请上传门店logo' }] }]"}]})],1),a("a-form-item",{attrs:{label:"联系人",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["linkman",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['linkman', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入联系人"}})],1),a("a-form-item",{attrs:{label:"联系电话",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["phone",{rules:[{required:!0}]}],expression:"['phone', { rules: [{ required: true }] }]"}],attrs:{placeholder:"请输入联系电话"}})],1),a("a-form-item",{attrs:{label:"营业时间",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"例如：8:30-17:30"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["shop_hours",{rules:[{required:!0}]}],expression:"['shop_hours', { rules: [{ required: true }] }]"}],attrs:{placeholder:"请输入营业时间"}})],1),a("a-form-item",{attrs:{label:"门店区域",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectRegion",{directives:[{name:"decorator",rawName:"v-decorator",value:["cascader",{rules:[{required:!0,message:"请选择省市区"}]}],expression:"['cascader', { rules: [{ required: true, message: '请选择省市区' }] }]"}],attrs:{placeholder:"请选择省市区"}})],1),a("a-form-item",{attrs:{label:"详细地址",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["address",{rules:[{required:!0}]}],expression:"['address', { rules: [{ required: true }] }]"}],attrs:{placeholder:"请输入详细地址"}})],1),a("a-form-item",{attrs:{label:"门店坐标",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["coordinate",{rules:[{required:!0}]}],expression:"['coordinate', { rules: [{ required: true }] }]"}],attrs:{placeholder:"请选择门店坐标"}}),a("Getpoint",{on:{setCoordinate:e.setCoordinate}})],1),a("a-form-item",{attrs:{label:"门店简介",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["summary"],expression:"['summary']"}],attrs:{autoSize:{minRows:4}}})],1),a("a-form-item",{attrs:{label:"自提核销",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_check",{initialValue:1,rules:[{required:!0}]}],expression:"['is_check', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("支持")]),a("a-radio",{attrs:{value:0}},[e._v("不支持")])],1)],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("启用")]),a("a-radio",{attrs:{value:0}},[e._v("禁用")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1),a("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[a("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1)],1)],1)},i=[],o=(t("d3b7"),t("3858")),s=t("2af9"),l={components:{SelectImage:s["h"],SelectRegion:s["j"],Getpoint:s["b"]},data:function(){return{isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},form:this.$form.createForm(this)}},created:function(){},methods:{setCoordinate:function(e){var a=this.form,t=this.$nextTick;t((function(){a.setFieldsValue({coordinate:e})}))},handleSubmit:function(e){e.preventDefault();var a=this.form.validateFields,t=this.onFormSubmit;a((function(e,a){!e&&t(a)}))},onFormSubmit:function(e){var a=this;this.isLoading=!0,this.isBtnLoading=!0,o["a"]({form:e}).then((function(e){a.$message.success(e.message,1.5),setTimeout((function(){a.$router.push("./index")}),1500)})).catch((function(){a.isBtnLoading=!1})).finally((function(){return a.isLoading=!1}))}}},n=l,d=t("2877"),c=Object(d["a"])(n,r,i,!1,null,null,null);a["default"]=c.exports},c480:function(e,a,t){"use strict";t.r(a);t("ac1f"),t("841c");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-row",[a("a-col",{attrs:{span:5}},[e.$auth("/store/shop/create")?a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]):e._e()],1),a("a-col",{staticClass:"flex flex-x-end",attrs:{span:11,offset:8}},[a("a-select",{staticStyle:{width:"220px","margin-right":"20px"},attrs:{placeholder:"是否支持自提核销"},model:{value:e.queryParam.isCheck,callback:function(a){e.$set(e.queryParam,"isCheck",a)},expression:"queryParam.isCheck"}},[a("a-select-option",{attrs:{value:-1}},[e._v("全部")]),a("a-select-option",{attrs:{value:1}},[e._v("支持")]),a("a-select-option",{attrs:{value:0}},[e._v("不支持")])],1),a("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px"},attrs:{placeholder:"请输入门店名称/联系人/电话"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(a){e.$set(e.queryParam,"search",a)},expression:"queryParam.search"}})],1)],1)],1),a("s-table",{ref:"table",attrs:{rowKey:"shop_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15,scroll:{x:1300}},scopedSlots:e._u([{key:"logo_url",fn:function(e){return a("span",{},[a("a",{attrs:{title:"点击查看原图",href:e,target:"_blank"}},[a("img",{attrs:{width:"50",height:"50",src:e,alt:"门店logo"}})])])}},{key:"shop_name",fn:function(t){return a("span",{},[a("p",{staticClass:"oneline-hide"},[e._v(e._s(t))])])}},{key:"full_address",fn:function(t){return a("span",{},[a("p",{staticClass:"twoline-hide",staticStyle:{width:"240px"}},[e._v(e._s(t))])])}},{key:"is_check",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:t?"green":""}},[e._v(e._s(t?"支持":"不支持"))])],1)}},{key:"status",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:t?"green":""}},[e._v(e._s(t?"启用":"禁用"))])],1)}},{key:"action",fn:function(t,r){return a("div",{staticClass:"actions"},[e.$auth("/store/shop/update")?a("a",{on:{click:function(a){return e.handleEdit(r)}}},[e._v("编辑")]):e._e(),a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete(r)}}},[e._v("删除")])])}}])})],1)},i=[],o=t("5530"),s=(t("d3b7"),t("3858")),l=t("2af9"),n={name:"Index",components:{STable:l["d"]},data:function(){var e=this;return{queryParam:{},isLoading:!1,columns:[{title:"门店ID",dataIndex:"shop_id"},{title:"门店logo",dataIndex:"logo_url",scopedSlots:{customRender:"logo_url"}},{title:"门店名称",dataIndex:"shop_name",width:"200px",scopedSlots:{customRender:"shop_name"}},{title:"营业时间",dataIndex:"shop_hours",width:"150px"},{title:"详细地址",dataIndex:"full_address",width:"260px",scopedSlots:{customRender:"full_address"}},{title:"自提核销",dataIndex:"is_check",scopedSlots:{customRender:"is_check"}},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time",width:"150px"},{title:"操作",width:"180px",fixed:"right",scopedSlots:{customRender:"action"}}],loadData:function(a){return s["f"](Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$router.push("./create")},handleEdit:function(e){this.$router.push({path:"./update",query:{shopId:e.shop_id}})},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return s["c"]({shopId:e.shop_id}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},d=n,c=t("2877"),u=Object(c["a"])(d,r,i,!1,null,null,null);a["default"]=u.exports},de94:function(e,a,t){},f30f:function(e,a,t){"use strict";t.r(a);t("ac1f"),t("841c");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-row",[a("a-col",{attrs:{span:5}},[a("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("a-col",{staticClass:"flex flex-x-end",attrs:{span:11,offset:8}},[a("a-select",{staticStyle:{width:"220px","margin-right":"20px"},attrs:{placeholder:"请选择地址类型"},model:{value:e.queryParam.type,callback:function(a){e.$set(e.queryParam,"type",a)},expression:"queryParam.type"}},[a("a-select-option",{attrs:{value:0}},[e._v("全部")]),a("a-select-option",{attrs:{value:10}},[e._v("发货地址")]),a("a-select-option",{attrs:{value:20}},[e._v("退货地址")])],1),a("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px"},attrs:{placeholder:"请输入姓名/联系电话"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(a){e.$set(e.queryParam,"search",a)},expression:"queryParam.search"}})],1)],1)],1),a("s-table",{ref:"table",attrs:{rowKey:"address_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"full_address",fn:function(t){return a("span",{},[a("p",{staticClass:"twoline-hide",staticStyle:{width:"270px"}},[e._v(e._s(t))])])}},{key:"type",fn:function(t){return a("span",{},[a("a-tag",[e._v(e._s(10==t?"发货地址":"退货地址"))])],1)}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(a){return e.handleEdit(r)}}},[e._v("编辑")]),a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),a("AddForm",{ref:"AddForm",on:{handleSubmit:e.handleRefresh}}),a("EditForm",{ref:"EditForm",on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=t("5530"),s=(t("d3b7"),t("4d55")),l=t("2af9"),n=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"地址类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["type",{initialValue:10,rules:[{required:!0}]}],expression:"['type', { initialValue: 10, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:10}},[e._v("发货地址")]),a("a-radio",{attrs:{value:20}},[e._v("退货地址")])],1)],1),a("a-form-item",{attrs:{label:"联系人姓名",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,message:"请输入联系人姓名"}]}],expression:"['name', { rules: [{ required: true, message: '请输入联系人姓名' }] }]"}]})],1),a("a-form-item",{attrs:{label:"联系电话",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["phone",{rules:[{required:!0,message:"请输入联系电话"}]}],expression:"['phone', { rules: [{ required: true, message: '请输入联系电话' }] }]"}]})],1),a("a-form-item",{attrs:{label:"选择地区",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectRegion",{directives:[{name:"decorator",rawName:"v-decorator",value:["cascader",{rules:[{required:!0,message:"请选择省市区"}]}],expression:"['cascader', { rules: [{ required: true, message: '请选择省市区' }] }]"}],attrs:{placeholder:"请选择省市区"}})],1),a("a-form-item",{attrs:{label:"详细地址",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["detail",{rules:[{required:!0,message:"请输入详细地址"}]}],expression:"['detail', { rules: [{ required: true, message: '请输入详细地址' }] }]"}]})],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},d=[],c={components:{SelectRegion:l["j"]},data:function(){return{title:"新增地址",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{add:function(){this.visible=!0},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,s["a"]({form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(){return a.confirmLoading=!1}))}}},u=c,m=t("2877"),p=Object(m["a"])(u,n,d,!1,null,null,null),f=p.exports,h=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"地址类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["type",{initialValue:10,rules:[{required:!0}]}],expression:"['type', { initialValue: 10, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:10}},[e._v("发货地址")]),a("a-radio",{attrs:{value:20}},[e._v("退货地址")])],1)],1),a("a-form-item",{attrs:{label:"联系人姓名",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,message:"请输入联系人姓名"}]}],expression:"['name', { rules: [{ required: true, message: '请输入联系人姓名' }] }]"}]})],1),a("a-form-item",{attrs:{label:"联系电话",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["phone",{rules:[{required:!0,message:"请输入联系电话"}]}],expression:"['phone', { rules: [{ required: true, message: '请输入联系电话' }] }]"}]})],1),a("a-form-item",{attrs:{label:"选择地区",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectRegion",{directives:[{name:"decorator",rawName:"v-decorator",value:["cascader",{rules:[{required:!0,message:"请选择省市区"}]}],expression:"['cascader', { rules: [{ required: true, message: '请选择省市区' }] }]"}],attrs:{placeholder:"请选择省市区"}})],1),a("a-form-item",{attrs:{label:"详细地址",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["detail",{rules:[{required:!0,message:"请输入详细地址"}]}],expression:"['detail', { rules: [{ required: true, message: '请输入详细地址' }] }]"}]})],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},v=[],b=t("88bc"),g=t.n(b),C={components:{SelectRegion:l["j"]},data:function(){return{title:"编辑地址",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{edit:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this.record,a=this.form.setFieldsValue;e.cascader=[e.province_id,e.city_id,e.region_id],this.$nextTick((function(){a(g()(e,["type","name","phone","cascader","detail","sort"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,s["d"]({addressId:this.record.address_id,form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(){return a.confirmLoading=!1}))}}},_=C,w=Object(m["a"])(_,h,v,!1,null,null,null),x=w.exports,y={name:"Index",components:{STable:l["d"],AddForm:f,EditForm:x},data:function(){var e=this;return{queryParam:{},isLoading:!1,columns:[{title:"地址ID",dataIndex:"address_id"},{title:"联系人姓名",dataIndex:"name"},{title:"联系电话",dataIndex:"phone"},{title:"详细地址",dataIndex:"full_address",scopedSlots:{customRender:"full_address"}},{title:"地址类型",dataIndex:"type",scopedSlots:{customRender:"type"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(a){return s["e"](Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return s["c"]({addressId:e.address_id}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},q=y,k=Object(m["a"])(q,r,i,!1,null,null,null);a["default"]=k.exports},f585:function(e,a,t){"use strict";t.d(a,"a",(function(){return s})),t.d(a,"b",(function(){return l}));var r=t("5530"),i=t("b775"),o={detail:"/setting/detail",update:"/setting/update"};function s(e){return Object(i["b"])({url:o.detail,method:"get",params:{key:e}})}function l(e,a){return Object(i["b"])({url:o.update,method:"post",data:Object(r["a"])({key:e},a)})}}}]);