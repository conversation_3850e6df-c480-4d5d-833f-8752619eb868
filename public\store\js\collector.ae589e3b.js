(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["collector"],{"374e":function(e,t,a){},"3d1e":function(e,t,a){"use strict";a("6f7d")},"556c":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{staticClass:"mt-30",attrs:{label:"APIKEY",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["config.99api.apiKey",{rules:[{required:!0,min:2,message:"请输入APIKEY"}]}],expression:"['config.99api.apiKey', {  rules: [{ required: true, min: 2, message: '请输入APIKEY' }]  }]"}],attrs:{placeholder:"请输入APIKEY"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("请填写99API的APIKEY，可前往")]),t("a",{attrs:{href:"https://user.99api.com/login?log=5&referee=19347",target:"_blank"}},[e._v("https://www.99api.com/")]),t("small",[e._v("注册申请")])])],1),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},n=[],i=(a("d3b7"),a("ddb0"),a("88bc")),o=a.n(i),l=a("ca00"),s=a("f585"),u={data:function(){return{labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),key:"collector",record:{}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,s["a"](this.key).then((function(t){e.record=t.data.values,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,t=this.$nextTick,a=this.form;!Object(l["f"])(a.getFieldsValue())&&t((function(){a.setFieldsValue(o()(e,["config"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,s["b"](this.key,{form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},c=u,d=(a("3d1e"),a("2877")),f=Object(d["a"])(c,r,n,!1,null,"f1ff72dc",null);t["default"]=f.exports},"59aa":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return o}));var r=a("5c06"),n=new r["a"]([{key:"PHYSICAL",name:"实物商品",value:10},{key:"VIRTUAL",name:"虚拟商品",value:20}]),i=(new r["a"]([{key:"SINGLE",name:"单规格",value:10},{key:"MULTI",name:"多规格",value:20}]),new r["a"]([{key:"NORMAL",name:"进行中",value:10},{key:"COMPLETED",name:"导入完成",value:20}])),o=new r["a"]([{key:"NORMAL",name:"进行中",value:10},{key:"COMPLETED",name:"采集完成",value:20}])},"5c28":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"container"},[t("a-card",{staticClass:"mb-20",attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-alert",{staticClass:"mb-30",attrs:{showIcon:!0,message:"功能说明",banner:""}},[t("template",{slot:"description"},[t("p",[e._v("支持采集淘宝、天猫、京东、拼多多、1688平台的商品，需先进行API接口配置")]),t("p",[e._v("商品采集之前需要系统开启队列服务，可在超管后台中查看是否开启")])])],2),t("a-form",{staticClass:"my-form",attrs:{form:e.myForm},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"商品链接",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["urls",{rules:[{required:!0,message:"请输入商品链接"}]}],expression:"['urls', { rules: [{ required: true, message: '请输入商品链接' }] }]"}],attrs:{"auto-size":{minRows:6,maxRows:12}}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("请填写要采集的商品链接，请用回车换行间隔，每次最多100个；")]),t("a-popover",{attrs:{title:"其他说明"}},[t("template",{slot:"content"},[t("p",{staticClass:"mb-5"},[e._v("1. 商品采集数据来源于第三方，采集完成后建议手动编辑价格、库存等敏感信息后再上架；")]),t("p",{staticClass:"mb-5"},[e._v("2. 京东商品无法获取SKU的信息，默认写入主商品价格，所以务必手动编辑后再上架；")]),t("p",{staticClass:"mb-5"},[e._v("3. 商品采集无法获取到商品重量，默认为1kg，请手动进行编辑，以免影响运费；")])]),t("a",{attrs:{href:"javascript:;"}},[e._v("其他说明")])],2)],1)],1),t("a-form-item",{attrs:{label:"商品主图",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["imageStorage",{initialValue:20,rules:[{required:!0}]}],expression:"['imageStorage', { initialValue: 20, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("下载到本地")]),t("a-radio",{attrs:{value:20}},[e._v("使用源图片url")])],1)],1),t("a-form-item",{attrs:{label:"商品类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_type",{initialValue:10,rules:[{required:!0}]}],expression:"['goods_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("实物商品")]),t("a-radio",{attrs:{value:20}},[e._v("虚拟商品")])],1)],1),t("a-form-item",{attrs:{label:"商品分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["categorys",{rules:[{required:!0,message:"请至少选择1个商品分类"}]}],expression:"['categorys', { rules: [{ required: true, message: '请至少选择1个商品分类'  }] }]"}],attrs:{placeholder:"请选择商品分类",dropdownStyle:{maxHeight:"500px",overflow:"auto"},treeData:e.categoryList,treeCheckable:"",treeCheckStrictly:"",allowClear:""}})],1),10==e.myForm.getFieldValue("goods_type")?t("a-form-item",{attrs:{label:"运费模板",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["delivery_id",{rules:[{required:!0,message:"请选择运费模板"}]}],expression:"['delivery_id', { rules: [{ required: true, message: '请选择运费模板'  }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请选择运费模板"}},e._l(e.deliveryList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.delivery_id}},[e._v(e._s(a.name))])})),1)],1):e._e(),t("a-form-item",{attrs:{label:"商品状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_status",{initialValue:20,rules:[{required:!0}]}],expression:"['goods_status', { initialValue: 20, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("上架")]),t("a-radio",{attrs:{value:20}},[e._v("下架")])],1)],1),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1),t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[t("span",[e._v("商品采集记录")]),t("div",{staticClass:"right-action"},[t("a",{attrs:{href:"javascript:;"},on:{click:function(t){return e.handleRefreshCollectorList()}}},[e._v("刷新")])])]),t("CollectorList",{ref:"CollectorList"})],1)],1)},n=[],i=(a("d81d"),a("d3b7"),a("b775")),o={list:"/goods.collector/list",batch:"/goods.collector/batch",delete:"/goods.collector/delete"};function l(e){return Object(i["b"])({url:o.list,method:"get",params:e})}function s(e){return Object(i["b"])({url:o.batch,method:"post",data:e})}function u(e){return Object(i["b"])({url:o.delete,method:"post",data:e})}var c,d=a("967a"),f=a("8243"),m=function(){var e=this,t=e._self._c;return t("div",{staticClass:"container"},[t("s-table",{ref:"table",attrs:{rowKey:"id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:10},scopedSlots:e._u([{key:"start_time",fn:function(a){return[t("span",[e._v(e._s(a||"--"))])]}},{key:"end_time",fn:function(a){return[t("span",[e._v(e._s(a||"--"))])]}},{key:"status",fn:function(a){return t("span",{},[t("a-tag",{attrs:{color:e.CollectorStatusColorEnum[a]}},[e._v(e._s(e.CollectorStatusEnum[a].name))])],1)}},{key:"action",fn:function(a,r){return t("span",{staticClass:"actions"},[t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")]),r.fail_count>0?t("a",{directives:[{name:"action",rawName:"v-action:failLog",arg:"failLog"}],on:{click:function(t){return e.handleFailLog(r)}}},[e._v("失败日志")]):e._e()])}}])}),t("a-modal",{attrs:{title:"失败日志"},on:{ok:function(t){e.visibleFailLog=!1}},model:{value:e.visibleFailLog,callback:function(t){e.visibleFailLog=t},expression:"visibleFailLog"}},[t("div",{staticClass:"modal-content"},e._l(e.failLogContent,(function(a,r){return t("p",{key:r,staticClass:"log-item"},[t("span",{staticClass:"mr-5"},[e._v("链接 ["+e._s(a.url)+"]")]),t("span",[e._v(e._s(a.message))])])})),0)])],1)},p=[],v=a("ade3"),b=a("2af9"),g=a("59aa"),h=(c={},Object(v["a"])(c,g["a"].NORMAL.value,""),Object(v["a"])(c,g["a"].COMPLETED.value,"green"),c),y={name:"CollectorList",components:{STable:b["d"]},data:function(){return{isLoading:!1,CollectorStatusEnum:g["a"],CollectorStatusColorEnum:h,columns:[{title:"记录ID",dataIndex:"id"},{title:"采集总数量",dataIndex:"total_count"},{title:"成功数量",dataIndex:"success_count"},{title:"失败数量",dataIndex:"fail_count"},{title:"开始时间",dataIndex:"start_time",scopedSlots:{customRender:"start_time"}},{title:"结束时间",dataIndex:"end_time",scopedSlots:{customRender:"end_time"}},{title:"采集状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(e){return l(e).then((function(e){return e.data.list}))},visibleFailLog:!1,failLogContent:[]}},created:function(){},methods:{handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:e["status"]==g["a"].NORMAL.value?"当前任务进行中，如果删除会导致未完成的任务终止":"删除后不可恢复",onOk:function(){return u({id:e["id"]}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleFailLog:function(e){this.visibleFailLog=!0,this.failLogContent=e.fail_log},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)}}},C=y,_=a("2877"),w=Object(_["a"])(C,m,p,!1,null,"794f2a07",null),L=w.exports,j={name:"Collector",components:{CollectorList:L},data:function(){return{myForm:this.$form.createForm(this),isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:12},categoryList:[],deliveryList:[]}},created:function(){this.getCategoryList(),this.getDeliveryList()},methods:{getCategoryList:function(){var e=this;f["a"].getCategoryTreeSelect().then((function(t){return e.categoryList=t}))},getDeliveryList:function(){var e=this;d["b"]().then((function(t){return e.deliveryList=t.data.list}))},handleSubmit:function(e){var t=this;e.preventDefault(),this.myForm.validateFields((function(e,a){e||(a.categoryIds=a.categorys.map((function(e){return e.value})),delete a.categorys,t.onFormSubmit(a))}))},onFormSubmit:function(e){var t=this;t.isLoading=!0,t.isBtnLoading=!0,s(e).then((function(e){t.$message.success(e.message,1.5),t.handleRefreshCollectorList(),t.myForm.resetFields()})).finally((function(e){t.isLoading=!1,t.isBtnLoading=!1}))},handleRefreshCollectorList:function(){this.$refs.CollectorList.handleRefresh()}}},O=j,k=(a("5c5c"),Object(_["a"])(O,r,n,!1,null,"12b00230",null));t["default"]=k.exports},"5c5c":function(e,t,a){"use strict";a("374e")},"6f7d":function(e,t,a){},"88bc":function(e,t,a){(function(t){var a=1/0,r=9007199254740991,n="[object Arguments]",i="[object Function]",o="[object GeneratorFunction]",l="[object Symbol]",s="object"==typeof t&&t&&t.Object===Object&&t,u="object"==typeof self&&self&&self.Object===Object&&self,c=s||u||Function("return this")();function d(e,t,a){switch(a.length){case 0:return e.call(t);case 1:return e.call(t,a[0]);case 2:return e.call(t,a[0],a[1]);case 3:return e.call(t,a[0],a[1],a[2])}return e.apply(t,a)}function f(e,t){var a=-1,r=e?e.length:0,n=Array(r);while(++a<r)n[a]=t(e[a],a,e);return n}function m(e,t){var a=-1,r=t.length,n=e.length;while(++a<r)e[n+a]=t[a];return e}var p=Object.prototype,v=p.hasOwnProperty,b=p.toString,g=c.Symbol,h=p.propertyIsEnumerable,y=g?g.isConcatSpreadable:void 0,C=Math.max;function _(e,t,a,r,n){var i=-1,o=e.length;a||(a=O),n||(n=[]);while(++i<o){var l=e[i];t>0&&a(l)?t>1?_(l,t-1,a,r,n):m(n,l):r||(n[n.length]=l)}return n}function w(e,t){return e=Object(e),L(e,t,(function(t,a){return a in e}))}function L(e,t,a){var r=-1,n=t.length,i={};while(++r<n){var o=t[r],l=e[o];a(l,o)&&(i[o]=l)}return i}function j(e,t){return t=C(void 0===t?e.length-1:t,0),function(){var a=arguments,r=-1,n=C(a.length-t,0),i=Array(n);while(++r<n)i[r]=a[t+r];r=-1;var o=Array(t+1);while(++r<t)o[r]=a[r];return o[t]=i,d(e,this,o)}}function O(e){return F(e)||S(e)||!!(y&&e&&e[y])}function k(e){if("string"==typeof e||q(e))return e;var t=e+"";return"0"==t&&1/e==-a?"-0":t}function S(e){return I(e)&&v.call(e,"callee")&&(!h.call(e,"callee")||b.call(e)==n)}var F=Array.isArray;function x(e){return null!=e&&E(e.length)&&!A(e)}function I(e){return D(e)&&x(e)}function A(e){var t=R(e)?b.call(e):"";return t==i||t==o}function E(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=r}function R(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function D(e){return!!e&&"object"==typeof e}function q(e){return"symbol"==typeof e||D(e)&&b.call(e)==l}var N=j((function(e,t){return null==e?{}:w(e,f(_(t,1),k))}));e.exports=N}).call(this,a("c8ba"))},"967a":function(e,t,a){"use strict";a.d(t,"f",(function(){return i})),a.d(t,"b",(function(){return o})),a.d(t,"d",(function(){return l})),a.d(t,"a",(function(){return s})),a.d(t,"e",(function(){return u})),a.d(t,"c",(function(){return c}));var r=a("b775"),n={list:"/setting.delivery/list",all:"/setting.delivery/all",detail:"/setting.delivery/detail",add:"/setting.delivery/add",edit:"/setting.delivery/edit",delete:"/setting.delivery/delete"};function i(e){return Object(r["b"])({url:n.list,method:"get",params:e})}function o(e){return Object(r["b"])({url:n.all,method:"get",params:e})}function l(e){return Object(r["b"])({url:n.detail,method:"get",params:e})}function s(e){return Object(r["b"])({url:n.add,method:"post",data:e})}function u(e){return Object(r["b"])({url:n.edit,method:"post",data:e})}function c(e){return Object(r["b"])({url:n.delete,method:"post",data:e})}},f585:function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return l}));var r=a("5530"),n=a("b775"),i={detail:"/setting/detail",update:"/setting/update"};function o(e){return Object(n["b"])({url:i.detail,method:"get",params:{key:e}})}function l(e,t){return Object(n["b"])({url:i.update,method:"post",data:Object(r["a"])({key:e},t)})}}}]);