<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\api\controller;

use app\api\service\passport\Login as LoginService;
use cores\exception\BaseException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\response\Json;

/**
 * 用户认证模块
 * Class Passport
 * @package app\api\controller
 */
class Passport extends Controller
{
    /**
     * 登录接口 (需提交手机号、短信验证码、第三方用户信息)
     * @return Json
     * @throws BaseException
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function login(): Json
    {
        // 执行登录
        $LoginService = new LoginService;
        if (! $LoginService->login($this->postForm())) {
            return $this->renderError($LoginService->getError());
        }
        // 用户信息
        $userInfo = $LoginService->getUserInfo();
        return $this->renderSuccess([
            'userId' => (int) $userInfo['user_id'],
            'token'  => $LoginService->getToken((int) $userInfo['user_id']),
        ], '登录成功');
    }

    /**
     * 微信小程序快捷登录 (需提交wx.login接口返回的code、微信用户公开信息)
     * 业务流程：判断openid是否存在 -> 存在:  更新用户登录信息 -> 返回userId和token
     *                          -> 不存在: 返回false, 跳转到注册页面
     * @return Json
     * @throws BaseException
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function loginMpWx(): Json
    {
        // 微信小程序一键登录
        $LoginService = new LoginService;
        if (! $LoginService->loginMpWx($this->postForm())) {
            return $this->renderError($LoginService->getError());
        }
        // 获取登录成功后的用户信息
        $userInfo = $LoginService->getUserInfo();
        return $this->renderSuccess([
            'userId' => (int) $userInfo['user_id'],
            'token'  => $LoginService->getToken((int) $userInfo['user_id']),
        ], '登录成功');
    }

    /**
     * 微信公众号快捷登录 (需提交wx.login接口返回的code、微信用户公开信息)
     * 业务流程：判断openid是否存在 -> 存在:  更新用户登录信息 -> 返回userId和token
     *                          -> 不存在: 返回false, 跳转到注册页面
     * @return Json
     * @throws BaseException
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function loginWxOfficial(): Json
    {
        // 微信小程序一键登录
        $LoginService = new LoginService;
        if (! $LoginService->loginWxOfficial($this->postForm())) {
            return $this->renderError($LoginService->getError());
        }
        // 获取登录成功后的用户信息
        $userInfo = $LoginService->getUserInfo();
        return $this->renderSuccess([
            'userId' => (int) $userInfo['user_id'],
            'token'  => $LoginService->getToken((int) $userInfo['user_id']),
        ], '微信授权登录成功');
    }

    /**
     * 支付宝小程序快捷登录 (需提交my.getAuthCode接口返回的authCode、支付宝用户公开信息)
     * 业务流程：判断openid是否存在 -> 存在:  更新用户登录信息 -> 返回userId和token
     *                          -> 不存在: 返回false, 跳转到注册页面
     * @return Json
     * @throws BaseException
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function loginMpAlipay(): Json
    {
        // 支付宝小程序一键登录
        $LoginService = new LoginService;
        if (! $LoginService->loginMpAlipay($this->postForm())) {
            return $this->renderError($LoginService->getError());
        }
        // 获取登录成功后的用户信息
        $userInfo = $LoginService->getUserInfo();
        return $this->renderSuccess([
            'userId' => (int) $userInfo['user_id'],
            'token'  => $LoginService->getToken((int) $userInfo['user_id']),
        ], '登录成功');
    }

    /**
     * 快捷登录: 微信小程序授权手机号登录
     * @return Json
     * @throws BaseException
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function loginMpWxMobile(): Json
    {
        // 微信小程序一键登录
        $LoginService = new LoginService;
        if (! $LoginService->loginMpWxMobile($this->postForm())) {
            return $this->renderError($LoginService->getError());
        }
        // 获取登录成功后的用户信息
        $userInfo = $LoginService->getUserInfo();
        return $this->renderSuccess([
            'userId' => (int) $userInfo['user_id'],
            'token'  => $LoginService->getToken((int) $userInfo['user_id']),
        ], '登录成功');
    }

    /**
     * 是否需要填写昵称头像 (微信小程序端)
     * @param string $code
     * @return Json
     * @throws BaseException
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function isPersonalMpweixin(string $code): Json
    {
        $LoginService       = new LoginService;
        $isPersonalMpweixin = $LoginService->isPersonalMpweixin($code);
        return $this->renderSuccess(compact('isPersonalMpweixin'));
    }
}
