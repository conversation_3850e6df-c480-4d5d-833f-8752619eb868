<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\dealer;

use think\Paginator;
use app\common\library\helper;
use app\api\model\dealer\User as DealerUserModel;
use app\common\model\dealer\Referee as RefereeModel;

/**
 * 分销商推荐关系模型
 * Class Referee
 * @package app\api\model\dealer
 */
class Referee extends RefereeModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'store_id',
        'create_time'
    ];

    /**
     * 获取我的团队列表
     * @param int $dealerId 分销商ID
     * @param array $param
     * @return array|mixed|Paginator
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getList(int $dealerId, array $param = []): Paginator
    {
        // 获取列表记录
        $list = parent::getList($dealerId, $param);
        // 获取被推荐人的下级成员数量
        return !$list->isEmpty() ? $this->getSubTeamNum($list) : $list;
    }

    /**
     * 获取被推荐人的下级成员数量
     * @param mixed $list 我的团队列表
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getSubTeamNum($list)
    {
        // 被推荐人ID集
        $userIds = helper::getArrayColumn($list, 'user_id');
        // 获取分销商列表
        $mdoel = new DealerUserModel;
        $dealerList = $mdoel->getListByUserIds($userIds);
        // 整理到我的团队列表
        $dealerList = helper::arrayColumn2Key($dealerList, 'user_id');
        foreach ($list as &$item) {
            $item['subDealer'] = $dealerList[$item['user_id']] ?? null;
        }
        return $list;
    }

    /**
     * 创建推荐关系
     * @param int $userId 用户ID
     * @param int $refereeId 推荐人ID
     * @return bool
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function createRelation(int $userId, int $refereeId): bool
    { 
        // 分销商基本设置
        $setting = Setting::getItem('basic');
        // 是否开启分销功能
        if (!$setting['is_open']) {
            return false;
        }
        // 自分享
        if ($userId == $refereeId) {
            return false;
        }
        // # 记录一级推荐关系
        // 判断当前用户是否已存在推荐关系
        if (self::isExistReferee($userId)) {
            return false;
        }
        // 判断推荐人是否为分销商
        if (!User::isDealerUser($refereeId)) {
            return false;
        }
        // 新增关系记录
        $model = new self;
        $model->add($refereeId, $userId, 1);
        // # 记录二级推荐关系
        if ($setting['level'] >= 2) {
            // 二级分销商id
            $referee2Id = self::getRefereeUserId($refereeId, 1, true);
            // 新增关系记录
            $referee2Id > 0 && $model->add($referee2Id, $userId, 2);
        }
        // # 记录三级推荐关系
        if ($setting['level'] == 3) {
            // 三级分销商id
            $referee3Id = self::getRefereeUserId($refereeId, 2, true);
            // 新增关系记录
            $referee3Id > 0 && $model->add($referee3Id, $userId, 3);
        }
        return true;
    }

    /**
     * 新增关系记录
     * @param int $dealerId 分销商ID
     * @param int $userId 用户ID
     * @param int $level 等级
     * @return bool
     */
    private function add(int $dealerId, int $userId, int $level = 1): bool
    {
        // 新增推荐关系
        $this->insert([
            'dealer_id' => $dealerId,
            'user_id' => $userId,
            'level' => $level,
            'store_id' => self::$storeId,
            'create_time' => time(),
        ]);
        // 记录分销商成员数量
        User::setMemberInc($dealerId, $level);
        return true;
    }

    /**
     * 是否已存在推荐关系
     * @param int $userId
     * @return bool
     */
    private static function isExistReferee(int $userId): bool
    {
        return (bool)self::get(['user_id' => $userId]);
    }
}