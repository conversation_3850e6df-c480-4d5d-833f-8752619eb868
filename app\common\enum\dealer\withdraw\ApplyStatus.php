<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\common\enum\dealer\withdraw;

use app\common\enum\EnumBasics;

/**
 * 枚举类：分销商提现审核状态
 * Class ApplyStatus
 * @package app\common\enum\dealer\withdraw
 */
class ApplyStatus extends EnumBasics
{
    // 待审核
    const WAIT = 10;

    // 审核通过
    const PASS = 20;

    // 驳回
    const REJECT = 30;

    // 已打款
    const PAID = 40;
    // 已完成
    const SUCCESS = 50;
    const FAIL    = 60;

    /**
     * 获取枚举类型值
     * @return array
     */
    public static function data(): array
    {
        return [
            self::WAIT    => [
                'name'  => '待审核',
                'value' => self::WAIT,
            ],
            self::PASS    => [
                'name'  => '审核通过',
                'value' => self::PASS,
            ],
            self::REJECT  => [
                'name'  => '驳回',
                'value' => self::REJECT,
            ],
            self::PAID    => [
                'name'  => '已打款',
                'value' => self::PAID,
            ],
            self::SUCCESS => [
                'name'  => '已完成',
                'value' => self::SUCCESS,
            ],
            self::FAIL    => [
                'name'  => '未收款',
                'value' => self::FAIL,
            ],
        ];
    }
}
