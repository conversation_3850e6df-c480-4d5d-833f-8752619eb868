<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\fulldiscount;

use app\common\enum\EnumBasics;

/**
 * 枚举类：满额立减活动 - 优惠条件
 * Class ThresholdType
 * @package app\common\enum\fulldiscount
 */
class ThresholdType extends EnumBasics
{
    // 满额
    const AMOUNT = 10;

    // 满件
    const QUANTITY = 20;

    /**
     * 获取枚举类型值
     * @return array
     */
    public static function data(): array
    {
        return [
            self::AMOUNT => [
                'name' => '满额',
                'value' => self::AMOUNT,
            ],
            self::QUANTITY => [
                'name' => '满件',
                'value' => self::QUANTITY,
            ]
        ];
    }
}
