<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller;

use app\common\library\Network;
use cores\exception\BaseException;

/**
 * 腾讯地图API (用于坐标选择)
 * Class Map
 * @package app\store\controller
 */
class Map extends Controller
{
    const KEY = 'K76BZ-W3O2Q-RFL5S-GXOPR-3ARIT-6KFE5';
    const URL = 'https://apis.map.qq.com';
    const REFER = 'https://lbs.qq.com';

    /**
     * 请求腾讯地址API
     * @param string $route
     * @return bool|string
     * @throws BaseException
     */
    public function transfer(string $route)
    {
        $param = \request()->get();
        $param['key'] = self::KEY;
        return Network::curlGet(self::URL . $route, $param, ['refer' => self::REFER]);
    }
}