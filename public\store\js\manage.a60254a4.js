(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["manage"],{"1da1":function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));r("d3b7");function a(e,t,r,a,n,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(a,n)}function n(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var o=e.apply(t,r);function s(e){a(o,n,i,s,l,"next",e)}function l(e){a(o,n,i,s,l,"throw",e)}s(void 0)}))}}},"759b":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e.isLoading?e._e():t("a-table",{attrs:{rowKey:"role_id",columns:e.columns,dataSource:e.roleList,defaultExpandAllRows:!0,expandIconColumnIndex:1,pagination:!1,loading:e.isLoading},scopedSlots:e._u([{key:"action",fn:function(r,a){return t("span",{},[[t("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(t){return e.handleEdit(a)}}},[e._v("编辑")]),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(a)}}},[e._v("删除")])]],2)}}],null,!1,1478444480)}),t("AddForm",{ref:"AddForm",attrs:{roleList:e.roleList,menuList:e.menuList},on:{handleSubmit:e.handleRefresh}}),t("EditForm",{ref:"EditForm",attrs:{roleList:e.roleList,menuList:e.menuList},on:{handleSubmit:e.handleRefresh}})],1)},n=[],i=r("c7eb"),o=r("1da1"),s=(r("d3b7"),r("782b")),l=r("b775"),c={list:"/store.menu/list"};function u(e){return Object(l["b"])({url:c.list,method:"get",params:e})}var d=r("2af9"),h=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:"新增角色",width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"角色名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["role_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['role_name', {rules: [{required: true, min: 2, message: '请输入至少2个字符'}]}]"}]})],1),t("a-form-item",{attrs:{label:"上级角色",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["parent_id"],expression:"['parent_id']"}],attrs:{treeData:e.roleListTree,dropdownStyle:{maxHeight:"400px",overflow:"auto"},allowClear:""}})],1),t("a-form-item",{attrs:{label:"菜单权限",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"设置该角色有权操作的功能"}},[t("a-tree",{ref:"MenuTree",attrs:{checkable:"",checkStrictly:"",treeData:e.menuListTreeData,autoExpandParent:!1},on:{check:e.onCheckedMenu},model:{value:e.checkedKeys,callback:function(t){e.checkedKeys=t},expression:"checkedKeys"}})],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', {initialValue: 100, rules: [{required: true, message: '请输入至少1个数字'}]}]"}],attrs:{min:0}})],1)],1)],1)],1)},f=[],m=r("2909"),p=r("5530"),v=(r("99af"),r("b0c0"),r("159b"),r("2ef0")),b=r.n(v),g={props:{roleList:{type:Array,required:!0},menuList:{type:Array,required:!0}},data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),roleListTree:[],menuListTreeData:[],checkedKeys:{checked:[],halfChecked:[]}}},methods:{add:function(){this.visible=!0,this.getRoleList(),this.getMenuList()},getMenuList:function(){var e=this.menuList;this.menuListTreeData=this.formatTreeDataForMenuList(e)},onCheckedMenu:function(e,t){var r=t.checked,a=t.node,n=this.menuListTreeData,i=this.findNode(a.eventKey,n);this.onCheckChilds(r,i),this.onCheckParents(r,i)},findNode:function(e,t){for(var r=0;r<t.length;r++){var a=t[r];if(a.key===e)return a;if(a.children){var n=this.findNode(e,a.children);if(n)return n}}return!1},onCheckParents:function(e,t){var r=this,a=this.menuListTreeData,n=function(e){var t=[],i=r.findNode(e,a);if(!i)return t;if(t.push(i.key),i.children){var o=n(i.parentKey);o.length&&(t=t.concat(o))}return t},i=n(t.parentKey);e&&i.length&&(this.checkedKeys.checked=b.a.union(this.checkedKeys.checked,i))},onCheckChilds:function(e,t){var r=t.children?this.getAllMenuKeys(t.children):[];r.length&&(this.checkedKeys.checked=e?b.a.union(this.checkedKeys.checked,r):b.a.difference(this.checkedKeys.checked,r))},getAllMenuKeys:function(e){var t=this,r=[];return e.forEach((function(e){if(r.push(e.key),e.children&&e.children.length){var a=t.getAllMenuKeys(e.children);a.length&&(r=r.concat(a))}})),r},getRoleList:function(){var e=this.roleList,t=this.formatTreeForRoleList(e);t.unshift({title:"顶级角色",key:0,value:0}),this.roleListTree=t},formatTreeForRoleList:function(e){var t=this,r=[];return e.forEach((function(e){var a={title:e.role_name,key:e.role_id,value:e.role_id};e.children&&e.children.length&&(a["children"]=t.formatTreeForRoleList(e["children"])),r.push(a)})),r},formatTreeDataForMenuList:function(e){var t=this,r=[];return e.forEach((function(e){var a={title:e.name,key:e.menu_id,parentKey:e.parent_id};e.children&&e.children.length&&(a["children"]=t.formatTreeDataForMenuList(e["children"])),r.push(a)})),r},handleSubmit:function(e){var t=this;e.preventDefault();var r=this.form.validateFields;r((function(e,r){e||t.onFormSubmit(Object(p["a"])(Object(p["a"])({},r),{},{menus:t.getCheckedKeys()}))}))},getCheckedKeys:function(){var e=this.$refs.MenuTree;return[].concat(Object(m["a"])(e.getCheckedKeys()),Object(m["a"])(e.getHalfCheckedKeys()))},handleCancel:function(){this.visible=!1,this.form.resetFields();var e=this.$refs.MenuTree;e.clearExpandedKeys(),this.checkedKeys.checked=[]},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,s["a"]({form:e}).then((function(r){t.$message.success(r.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(e){t.confirmLoading=!1}))}}},y=g,w=r("2877"),C=Object(w["a"])(y,h,f,!1,null,null,null),L=C.exports,k=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:"编辑角色",width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"角色名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["role_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['role_name', {rules: [{required: true, min: 2, message: '请输入至少2个字符'}]}]"}]})],1),t("a-form-item",{attrs:{label:"上级角色",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["parent_id"],expression:"['parent_id']"}],attrs:{treeData:e.roleListTree,dropdownStyle:{maxHeight:"400px",overflow:"auto"},allowClear:""}})],1),t("a-form-item",{attrs:{label:"菜单权限",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"设置该角色有权操作的功能"}},[t("a-tree",{ref:"MenuTree",attrs:{checkable:"",checkStrictly:"",treeData:e.menuListTreeData,autoExpandParent:!1},on:{check:e.onCheckedMenu},model:{value:e.checkedKeys,callback:function(t){e.checkedKeys=t},expression:"checkedKeys"}})],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', {initialValue: 100, rules: [{required: true, message: '请输入至少1个数字'}]}]"}],attrs:{min:0}})],1)],1)],1)],1)},_=[],x=(r("b1f8"),r("caad"),r("2532"),{props:{roleList:{type:Array,required:!0},menuList:{type:Array,required:!0}},data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{},roleListTree:[],menuListTreeData:[],checkedKeys:{checked:[],halfChecked:[]}}},methods:{edit:function(e){this.visible=!0,this.record=e,this.getRoleList(),this.getMenuList(),this.setMenuChecked(),this.setFieldsValue()},setFieldsValue:function(){var e=this,t=this.$nextTick,r=this.form.setFieldsValue;t((function(){r(b.a.pick(e.record,["role_name","parent_id","sort"]))}))},getMenuList:function(){var e=this.menuList;this.menuListTreeData=this.formatTreeDataForMenuList(e)},onCheckedMenu:function(e,t){var r=t.checked,a=t.node,n=this.menuListTreeData,i=this.findNode(a.eventKey,n);this.onCheckChilds(r,i),this.onCheckParents(r,i)},findNode:function(e,t){for(var r=0;r<t.length;r++){var a=t[r];if(a.key===e)return a;if(a.children){var n=this.findNode(e,a.children);if(n)return n}}return!1},onCheckParents:function(e,t){var r=this,a=this.menuListTreeData,n=function(e){var t=[],i=r.findNode(e,a);if(!i)return t;if(t.push(i.key),i.children){var o=n(i.parentKey);o.length&&(t=t.concat(o))}return t},i=n(t.parentKey);e&&i.length&&(this.checkedKeys.checked=b.a.union(this.checkedKeys.checked,i))},onCheckChilds:function(e,t){var r=t.children?this.getAllMenuKeys(t.children):[];r.length&&(this.checkedKeys.checked=e?b.a.union(this.checkedKeys.checked,r):b.a.difference(this.checkedKeys.checked,r))},getAllMenuKeys:function(e){var t=this,r=[];return e.forEach((function(e){if(r.push(e.key),e.children&&e.children.length){var a=t.getAllMenuKeys(e.children);a.length&&(r=r.concat(a))}})),r},setMenuChecked:function(){var e=this.menuListTreeData,t=this.record,r=this.getAllMenuKeys(e);this.checkedKeys.checked=b.a.intersection(t.menuIds,r)},getRoleList:function(){var e=this.roleList,t=this.formatTreeForRoleList(e);t.unshift({title:"顶级角色",key:0,value:0}),this.roleListTree=t},formatTreeForRoleList:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=[];return e.forEach((function(e){var n={title:e.role_name,key:e.role_id,value:e.role_id};([e.role_id,e.parent_id].includes(t.record.role_id)||!0===r)&&(n.disabled=!0),e.children&&e.children.length&&(n["children"]=t.formatTreeForRoleList(e["children"],n.disabled)),a.push(n)})),a},formatTreeDataForMenuList:function(e){var t=this,r=[];return e.forEach((function(e){var a={title:e.name,key:e.menu_id,parentKey:e.parent_id};e.children&&e.children.length&&(a["children"]=t.formatTreeDataForMenuList(e["children"])),r.push(a)})),r},handleSubmit:function(e){var t=this;e.preventDefault();var r=this.form.validateFields;r((function(e,r){e||t.onFormSubmit(Object(p["a"])(Object(p["a"])({},r),{},{menus:t.getCheckedKeys()}))}))},getCheckedKeys:function(){var e=this.$refs.MenuTree;return[].concat(Object(m["a"])(e.getCheckedKeys()),Object(m["a"])(e.getHalfCheckedKeys()))},handleCancel:function(){this.visible=!1,this.form.resetFields();var e=this.$refs.MenuTree;e.clearExpandedKeys(),this.checkedKeys.checked=[]},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,s["c"]({roleId:this.record["role_id"],form:e}).then((function(r){t.$message.success(r.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(e){t.confirmLoading=!1}))}}}),F=x,T=Object(w["a"])(F,k,_,!1,null,null,null),S=T.exports,E={name:"Index",components:{STable:d["b"],AddForm:L,EditForm:S},data:function(){return{roleList:[],queryParam:{},isLoading:!0,columns:[{title:"角色ID",dataIndex:"role_id"},{title:"角色名称",dataIndex:"role_name"},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],menuList:[]}},created:function(){this.getRoleList()},methods:{getRoleList:function(){var e=this;this.isLoading=!0,s["d"]().then((function(t){e.roleList=t.data.list})).finally((function(t){e.isLoading=!1}))},getMenuList:function(){var e=this;return Object(o["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.menuList.length){t.next=4;break}return e.isLoading=!0,t.next=4,u().then((function(t){e.menuList=t.data.list})).finally((function(){e.isLoading=!1}));case 4:case"end":return t.stop()}}),t)})))()},handleAdd:function(){var e=this;return Object(o["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getMenuList();case 2:e.$refs.AddForm.add();case 3:case"end":return t.stop()}}),t)})))()},handleEdit:function(e){var t=this;return Object(o["a"])(Object(i["a"])().mark((function r(){return Object(i["a"])().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,t.getMenuList();case 2:t.$refs.EditForm.edit(e);case 3:case"end":return r.stop()}}),r)})))()},handleDelete:function(e){var t=this,r=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return s["b"]({roleId:e["role_id"]}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){r.destroy()}))}})},handleRefresh:function(){this.getRoleList()}}},K=E,D=Object(w["a"])(K,a,n,!1,null,null,null);t["default"]=D.exports},"782b":function(e,t,r){"use strict";r.d(t,"d",(function(){return i})),r.d(t,"a",(function(){return o})),r.d(t,"c",(function(){return s})),r.d(t,"b",(function(){return l}));var a=r("b775"),n={list:"/store.role/list",add:"/store.role/add",edit:"/store.role/edit",delete:"/store.role/delete"};function i(e){return Object(a["b"])({url:n.list,method:"get",params:e})}function o(e){return Object(a["b"])({url:n.add,method:"post",data:e})}function s(e){return Object(a["b"])({url:n.edit,method:"post",data:e})}function l(e){return Object(a["b"])({url:n.delete,method:"post",data:e})}},"9dce":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("content-header",{attrs:{title:"管理员设置"}}),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"姓名",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"管理员姓名"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["real_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['real_name', {rules: [{required: true, min: 2, message: '请输入至少2个字符'}]}]"}]})],1),t("a-form-item",{attrs:{label:"用户名",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"后台登录用户名"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["user_name",{rules:[{required:!0,min:4,message:"请输入至少4个字符"}]}],expression:"['user_name', {rules: [{required: true, min: 4, message: '请输入至少4个字符'}]}]"}]})],1),t("a-form-item",{attrs:{label:"用户密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"后台登录密码"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["password",{rules:[{min:6,message:"请输入至少6个字符"}]}],expression:"['password', {rules: [{min: 6, message: '请输入至少6个字符'}]}]"}],attrs:{type:"password"}})],1),t("a-form-item",{attrs:{label:"确认密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["password_confirm",{rules:[{message:"请输入确认密码"},{validator:e.compareToFirstPassword}]}],expression:"['password_confirm', {rules: [\n            {message: '请输入确认密码'},\n            {validator: compareToFirstPassword}\n          ]}]"}],attrs:{type:"password"}})],1),t("a-form-item",{attrs:{wrapperCol:{span:13,offset:6}}},[t("a-button",{attrs:{type:"primary",loading:e.isLoading,disabled:e.isLoading},on:{click:e.handleSubmit}},[e._v("提交")])],1)],1)],1)],1)},n=[],i=(r("d3b7"),r("f544")),o=r("2af9"),s=r("2ef0"),l=r.n(s),c={name:"TableList",components:{ContentHeader:o["a"],STable:o["b"]},data:function(){return{labelCol:{span:6},wrapperCol:{span:13},isLoading:!1,form:this.$form.createForm(this)}},mounted:function(){this.getInfo()},methods:{getInfo:function(){var e=this;this.isLoading=!0,i["d"]().then((function(t){var r=t.data.userInfo;e.form.setFieldsValue(l.a.pick(r,"user_name","real_name"))})).finally((function(){e.isLoading=!1}))},compareToFirstPassword:function(e,t,r){var a=this.form,n=a.getFieldValue("password");return!n||t===n||new Error("您输入的确认密码不一致")},handleSubmit:function(e){var t=this;e.preventDefault();var r=this.form.validateFields;r((function(e,r){e||(t.isLoading=!0,t.onFormSubmit(r).finally((function(){t.isLoading=!1})))}))},onFormSubmit:function(e){var t=this;return i["f"]({form:e}).then((function(e){t.$message.success(e.message),setTimeout((function(){window.location.reload()}),800)}))}}},u=c,d=r("2877"),h=Object(d["a"])(u,a,n,!1,null,null,null);t["default"]=h.exports},b484:function(e,t,r){"use strict";r.r(t);r("ac1f"),r("841c");var a=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{attrs:{span:6}},[t("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),t("a-col",{attrs:{span:8,offset:10}},[t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px",float:"right"},attrs:{placeholder:"请输入用户名/姓名"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"store_user_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"user_name",fn:function(r,a){return t("div",{},[t("span",{staticStyle:{"margin-right":"6px"}},[e._v(e._s(r))]),a.is_super?t("a-tag",{attrs:{color:"green"}},[e._v("超级管理员")]):e._e()],1)}},{key:"action",fn:function(r,a){return t("div",{staticClass:"actions"},[t("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],on:{click:function(t){return e.handleEdit(a)}}},[e._v("编辑")]),a.is_super?e._e():[t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(a)}}},[e._v("删除")])]],2)}}])}),t("AddForm",{ref:"AddForm",attrs:{roleList:e.roleList},on:{handleSubmit:e.handleRefresh}}),t("EditForm",{ref:"EditForm",attrs:{roleList:e.roleList},on:{handleSubmit:e.handleRefresh}})],1)},n=[],i=r("5530"),o=(r("d3b7"),r("f544")),s=r("782b"),l=r("2af9"),c=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"管理员姓名",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["real_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['real_name', {rules: [{required: true, min: 2, message: '请输入至少2个字符'}]}]"}]})],1),t("a-form-item",{attrs:{label:"用户名",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"后台登录用户名"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["user_name",{rules:[{required:!0,min:4,message:"请输入至少4个字符"}]}],expression:"['user_name', {rules: [{required: true, min: 4, message: '请输入至少4个字符'}]}]"}]})],1),t("a-form-item",{attrs:{label:"所属角色",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"后台管理员角色"}},[t("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["roles",{rules:[{required:!0,message:"请至少选择一个角色"}]}],expression:"['roles', {rules: [{required: true, message: '请至少选择一个角色'}]}]"}],attrs:{treeCheckable:"",treeCheckStrictly:"",treeDefaultExpandAll:"",allowClear:"",treeData:e.roleListTreeData,dropdownStyle:{maxHeight:"500px",overflow:"auto"}}})],1),t("a-form-item",{attrs:{label:"用户密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"后台登录密码"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["password",{rules:[{required:!0,min:6,message:"请输入至少6个字符"}]}],expression:"['password', {rules: [\n            {required: true, min: 6, message: '请输入至少6个字符'}\n          ]}]"}],attrs:{type:"password"}})],1),t("a-form-item",{attrs:{label:"确认密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["password_confirm",{rules:[{required:!0,message:"请输入确认密码"},{validator:e.compareToFirstPassword}]}],expression:"['password_confirm', {rules: [\n            {required: true, message: '请输入确认密码'},\n            {validator: compareToFirstPassword}\n          ]}]"}],attrs:{type:"password"}})],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', {initialValue: 100, rules: [{required: true, message: '请输入至少1个数字'}]}]"}],attrs:{min:0}})],1)],1)],1)],1)},u=[],d=(r("99af"),r("caad"),r("d81d"),r("2532"),r("159b"),{props:{roleList:{type:Array,required:!0}},data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),roleListTreeData:[]}},methods:{add:function(){this.title="新增管理员",this.visible=!0,this.getRoleList()},getRoleList:function(){var e=this.roleList,t=this.formatTreeData(e);this.roleListTreeData=t},getCheckedRoleKeys:function(){var e=this.roleList,t=this.record,r=function(e){var a=[];return e.forEach((function(e){if(t["roleIds"].includes(e["role_id"])&&a.push({label:e["role_name"],value:e["role_id"]}),e.children&&e.children.length){var n=r(e.children);n.length&&(a=a.concat(n))}})),a};return r(e)},formatTreeData:function(e){var t=this,r=[];return e.forEach((function(e){var a={title:e.role_name,key:e.role_id,value:e.role_id};e.children&&e.children.length&&(a["children"]=t.formatTreeData(e["children"])),r.push(a)})),r},handleSubmit:function(e){var t=this;e.preventDefault();var r=this.form.validateFields;r((function(e,r){r.roles&&(r.roles=r.roles.map((function(e){return e.value}))),!e&&t.onFormSubmit(r)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},compareToFirstPassword:function(e,t,r){var a=this.form;return!t||t===a.getFieldValue("password")||new Error("您输入的确认密码不一致")},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,o["a"]({form:e}).then((function(r){t.$message.success(r.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(e){t.confirmLoading=!1}))}}}),h=d,f=r("2877"),m=Object(f["a"])(h,c,u,!1,null,null,null),p=m.exports,v=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"管理员姓名",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["real_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['real_name', {rules: [{required: true, min: 2, message: '请输入至少2个字符'}]}]"}]})],1),t("a-form-item",{attrs:{label:"用户名",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"后台登录用户名"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["user_name",{rules:[{required:!0,min:4,message:"请输入至少4个字符"}]}],expression:"['user_name', {rules: [{required: true, min: 4, message: '请输入至少4个字符'}]}]"}]})],1),e.record.is_super?e._e():t("a-form-item",{attrs:{label:"所属角色",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"后台管理员角色"}},[t("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["roles",{rules:[{required:!0,message:"请至少选择一个角色"}]}],expression:"['roles', {rules: [{required: true, message: '请至少选择一个角色'}]}]"}],attrs:{treeCheckable:"",treeCheckStrictly:"",treeDefaultExpandAll:"",allowClear:"",treeData:e.roleListTreeData,dropdownStyle:{maxHeight:"500px",overflow:"auto"}}})],1),t("a-form-item",{attrs:{label:"用户密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"后台登录密码"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["password",{rules:[{min:6,message:"请输入至少6个字符"}]}],expression:"['password', {rules: [{min: 6, message: '请输入至少6个字符'}]}]"}],attrs:{type:"password"}})],1),t("a-form-item",{attrs:{label:"确认密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["password_confirm",{rules:[{message:"请输入确认密码"},{validator:e.compareToFirstPassword}]}],expression:"['password_confirm', {rules: [\n            { message: '请输入确认密码'},\n            {validator: compareToFirstPassword}\n          ]}]"}],attrs:{type:"password"}})],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', {initialValue: 100, rules: [{required: true, message: '请输入至少1个数字'}]}]"}],attrs:{min:0}})],1)],1)],1)],1)},b=[],g=(r("b1f8"),r("2ef0")),y=r.n(g),w={props:{roleList:{type:Array,required:!0}},data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),roleListTreeData:[],record:{}}},methods:{edit:function(e){this.title="编辑管理员",this.visible=!0,this.record=e,!e["is_super"]&&this.getRoleList(),this.setFieldsValue()},setFieldsValue:function(){var e=this,t=this.form.setFieldsValue,r=this.getCheckedRoleKeys;this.$nextTick((function(){var a=y.a.pick(e.record,["user_name","real_name","sort"]);a.roles=r(),t(a)}))},getRoleList:function(){var e=this.roleList,t=this.formatTreeData(e);this.roleListTreeData=t},getCheckedRoleKeys:function(){var e=this.roleList,t=this.record,r=function(e){var a=[];return e.forEach((function(e){if(t["roleIds"].includes(e["role_id"])&&a.push({label:e["role_name"],value:e["role_id"]}),e.children&&e.children.length){var n=r(e.children);n.length&&(a=a.concat(n))}})),a};return r(e)},formatTreeData:function(e){var t=this,r=[];return e.forEach((function(e){var a={title:e.role_name,key:e.role_id,value:e.role_id};e.children&&e.children.length&&(a["children"]=t.formatTreeData(e["children"])),r.push(a)})),r},handleSubmit:function(e){var t=this;e.preventDefault();var r=this.form.validateFields;r((function(e,r){r.roles&&(r.roles=r.roles.map((function(e){return e.value}))),!e&&t.onFormSubmit(r)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},compareToFirstPassword:function(e,t,r){var a=this.form;return!t||t===a.getFieldValue("password")||new Error("您输入的确认密码不一致")},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,o["c"]({userId:this.record["store_user_id"],form:e}).then((function(r){t.$message.success(r.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(e){t.confirmLoading=!1}))}}},C=w,L=Object(f["a"])(C,v,b,!1,null,null,null),k=L.exports,_={name:"Index",components:{STable:l["b"],AddForm:p,EditForm:k},data:function(){var e=this;return{roleList:[],queryParam:{},isLoading:!1,columns:[{title:"管理员ID",dataIndex:"store_user_id"},{title:"用户名",dataIndex:"user_name",scopedSlots:{customRender:"user_name"}},{title:"姓名",dataIndex:"real_name"},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(t){return o["e"](Object(i["a"])(Object(i["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){this.getRoleList()},methods:{handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e)},getRoleList:function(){var e=this;this.isLoading=!0,s["d"]().then((function(t){e.roleList=t.data.list})).finally((function(){e.isLoading=!1}))},handleDelete:function(e){var t=this,r=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return o["b"]({userId:e["store_user_id"]}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){r.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},x=_,F=Object(f["a"])(x,a,n,!1,null,null,null);t["default"]=F.exports},c7eb:function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));r("a4d3"),r("e01a"),r("b636"),r("d28b"),r("944a"),r("fb6a"),r("b0c0"),r("0c47"),r("23dc"),r("3410"),r("131a"),r("d3b7"),r("3ca3"),r("159b"),r("ddb0");var a=r("53ca");function n(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
n=function(){return t};var e,t={},r=Object.prototype,i=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},s="function"==typeof Symbol?Symbol:{},l=s.iterator||"@@iterator",c=s.asyncIterator||"@@asyncIterator",u=s.toStringTag||"@@toStringTag";function d(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,r){return e[t]=r}}function h(e,t,r,a){var n=t&&t.prototype instanceof y?t:y,i=Object.create(n.prototype),s=new O(a||[]);return o(i,"_invoke",{value:S(e,r,s)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=h;var m="suspendedStart",p="suspendedYield",v="executing",b="completed",g={};function y(){}function w(){}function C(){}var L={};d(L,l,(function(){return this}));var k=Object.getPrototypeOf,_=k&&k(k(j([])));_&&_!==r&&i.call(_,l)&&(L=_);var x=C.prototype=y.prototype=Object.create(L);function F(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function T(e,t){function r(n,o,s,l){var c=f(e[n],e,o);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==Object(a["a"])(d)&&i.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,s,l)}),(function(e){r("throw",e,s,l)})):t.resolve(d).then((function(e){u.value=e,s(u)}),(function(e){return r("throw",e,s,l)}))}l(c.arg)}var n;o(this,"_invoke",{value:function(e,a){function i(){return new t((function(t,n){r(e,a,t,n)}))}return n=n?n.then(i,i):i()}})}function S(t,r,a){var n=m;return function(i,o){if(n===v)throw Error("Generator is already running");if(n===b){if("throw"===i)throw o;return{value:e,done:!0}}for(a.method=i,a.arg=o;;){var s=a.delegate;if(s){var l=E(s,a);if(l){if(l===g)continue;return l}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===m)throw n=b,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=v;var c=f(t,r,a);if("normal"===c.type){if(n=a.done?b:p,c.arg===g)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(n=b,a.method="throw",a.arg=c.arg)}}}function E(t,r){var a=r.method,n=t.iterator[a];if(n===e)return r.delegate=null,"throw"===a&&t.iterator["return"]&&(r.method="return",r.arg=e,E(t,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),g;var i=f(n,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function K(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(K,this),this.reset(!0)}function j(t){if(t||""===t){var r=t[l];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function r(){for(;++n<t.length;)if(i.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(Object(a["a"])(t)+" is not iterable")}return w.prototype=C,o(x,"constructor",{value:C,configurable:!0}),o(C,"constructor",{value:w,configurable:!0}),w.displayName=d(C,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,C):(e.__proto__=C,d(e,u,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},F(T.prototype),d(T.prototype,c,(function(){return this})),t.AsyncIterator=T,t.async=function(e,r,a,n,i){void 0===i&&(i=Promise);var o=new T(h(e,r,a,n),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},F(x),d(x,u,"Generator"),d(x,l,(function(){return this})),d(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var a in t)r.push(a);return r.reverse(),function e(){for(;r.length;){var a=r.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=j,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(D),!t)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(a,n){return s.type="throw",s.arg=t,r.next=a,n&&(r.method="next",r.arg=e),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=i.call(o,"catchLoc"),c=i.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&i.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var n=a;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=e,o.arg=t,n?(this.method="next",this.next=n.finallyLoc,g):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),D(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var a=r.completion;if("throw"===a.type){var n=a.arg;D(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,a){return this.delegate={iterator:j(t),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=e),g}},t}}}]);