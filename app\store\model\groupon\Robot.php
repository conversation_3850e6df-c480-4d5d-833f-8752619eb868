<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\groupon;

use app\common\model\groupon\Robot as RobotModel;

/**
 * 拼团机器人模型
 * Class Robot
 * @package app\store\model\groupon
 */
class Robot extends RobotModel
{
    /**
     * 获取列表
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getList(): \think\Paginator
    {
        // 获取列表信息
        $list = $this->where(['is_delete' => 0])
            ->order(['sort', $this->getPk()])
            ->paginate(15);
        return static::preload($list, ['avatar']);
    }

    /**
     * 新增记录
     * @param array $data
     * @return bool|false
     */
    public function add(array $data): bool
    {
        $data['store_id'] = self::$storeId;
        return $this->save($data);
    }

    /**
     * 更新记录
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
        return $this->save($data) !== false;
    }

    /**
     * 删除记录
     * @return bool
     */
    public function setDelete(): bool
    {
        return $this->save(['is_delete' => 1]);
    }
}