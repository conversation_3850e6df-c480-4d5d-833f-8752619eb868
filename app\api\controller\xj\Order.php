<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\xj;

use think\response\Json;
use app\api\controller\Controller;
use app\api\model\xj\Order as OrderModel;

/**
 * 充值记录管理
 * Class Order
 * @package app\api\controller\recharge
 */
class Order extends Controller
{
    /**
     * 我的充值记录列表
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DbException
     */
    public function list(): Json
    {
        $model = new OrderModel;
        $list = $model->getList();
        return $this->renderSuccess(compact('list'));
    }
}