(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["client"],{"036f":function(e,a,t){},"144d":function(e,a,t){},2340:function(e,a,t){},"2fa9":function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[a("a-form-item",{staticClass:"mt-30",attrs:{label:"是否开启访问",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["enabled",{rules:[{required:!0}]}],expression:"['enabled', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:!0}},[e._v("开启")]),a("a-radio",{attrs:{value:!1}},[e._v("关闭")])],1),a("div",{staticClass:"form-item-help"},[a("small",[e._v("注：如关闭，用户则无法通过H5端访问商城")])])],1),a("a-form-item",{attrs:{label:"H5站点地址",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["baseUrl",{rules:[{required:!0,message:"请输入H5站点地址"}]}],expression:"['baseUrl', { rules: [{ required: true, message: '请输入H5站点地址' }] }]"}]}),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[a("span",[e._v("请填写H5端实际的访问地址，以")]),a("a-tag",{staticClass:"mlr-5"},[e._v("https://")]),e._v("开头； 斜杠 "),a("a-tag",{staticClass:"mlr-5"},[e._v("/")]),a("span",[e._v("结尾")])],1),a("p",{staticClass:"extra"},[a("span",[e._v("例如：https://www.你的域名.com/")]),a("span",{staticClass:"ml-8"},[e._v("https://shop10001.你的域名.com/")])])])],1),a("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[a("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},i=[],s=(t("d3b7"),t("88bc")),l=t.n(s),o=t("ca00"),n=t("5530"),c=t("b775"),u={detail:"/client.h5.setting/detail",update:"/client.h5.setting/update"};function d(e){return Object(c["b"])({url:u.detail,method:"get",params:{key:e}})}function p(e,a){return Object(c["b"])({url:u.update,method:"post",data:Object(n["a"])({key:e},a)})}var m={data:function(){return{labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),key:"basic",record:{}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,d(this.key).then((function(a){e.record=a.data.detail,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,a=this.$nextTick,t=this.form;!Object(o["f"])(t.getFieldsValue())&&a((function(){t.setFieldsValue(l()(e,["enabled","baseUrl"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},onFormSubmit:function(e){var a=this;this.isLoading=!0,p(this.key,{form:e}).then((function(e){return a.$message.success(e.message,1.5)})).finally((function(){return a.isLoading=!1}))}}},f=m,v=(t("9a01"),t("2877")),b=Object(v["a"])(f,r,i,!1,null,"3025a70a",null);a["default"]=b.exports},3267:function(e,a,t){"use strict";var r=t("5c06");a["a"]=new r["a"]([{key:"CAPTCHA",name:"短信验证码",value:"captcha"},{key:"ORDER_PAY",name:"新付款订单",value:"order_pay"}])},3490:function(e,a,t){"use strict";t("144d")},"3fac":function(e,a,t){"use strict";t("2340")},"53dd":function(e,a,t){},"56ee":function(e,a,t){},"67e5":function(e,a,t){"use strict";var r=function(){var e=this,a=e._self._c;return a("a-upload",{attrs:{beforeUpload:e.beforeUpload,remove:e.handleRemove,accept:e.accept,multiple:!1,fileList:e.fileList}},[a("a-button",[a("a-icon",{attrs:{type:"upload"}}),e._v("选择文件 ")],1)],1)},i=[],s=(t("b0c0"),t("a434"),t("4d91")),l={name:"InputFile",model:{prop:"value",event:"change"},props:{value:s["a"].string.def(""),accept:s["a"].string.def("")},data:function(){return{fileList:[]}},watch:{value:{immediate:!0,handler:function(e){e&&0===this.fileList.length&&(this.fileList=[{uid:"default",name:e}])}},fileList:function(e){var a=e.length>0?e[0]:null,t=a?a.name:null;"default"!=a.uid&&this.$emit("change",t,a)}},methods:{beforeUpload:function(e){return this.fileList=[e],!1},handleRemove:function(e){var a=this.fileList,t=a.indexOf(e);t>-1&&a.splice(t,1)}}},o=l,n=t("2877"),c=Object(n["a"])(o,r,i,!1,null,"37881c5a",null),u=c.exports;a["a"]=u},"79e0":function(e,a,t){"use strict";t("ffb0")},"7cdd":function(e,a,t){"use strict";t.d(a,"a",(function(){return l})),t.d(a,"b",(function(){return o})),t.d(a,"c",(function(){return n}));var r=t("5530"),i=t("b775"),s={basic:"/client.mp.alipay.setting/basic",detail:"/client.mp.alipay.setting/detail",update:"/client.mp.alipay.setting/update",updateBasic:"/client.mp.alipay.setting/updateBasic"};function l(e){return Object(i["b"])({url:s.detail,method:"get",params:{key:e}})}function o(e,a){return Object(i["b"])({url:s.update,method:"post",data:Object(r["a"])({key:e},a)})}function n(e){return Object(i["b"])({headers:{"Content-Type":"multipart/form-data"},url:s.updateBasic,method:"post",data:e})}},"88bc":function(e,a,t){(function(a){var t=1/0,r=9007199254740991,i="[object Arguments]",s="[object Function]",l="[object GeneratorFunction]",o="[object Symbol]",n="object"==typeof a&&a&&a.Object===Object&&a,c="object"==typeof self&&self&&self.Object===Object&&self,u=n||c||Function("return this")();function d(e,a,t){switch(t.length){case 0:return e.call(a);case 1:return e.call(a,t[0]);case 2:return e.call(a,t[0],t[1]);case 3:return e.call(a,t[0],t[1],t[2])}return e.apply(a,t)}function p(e,a){var t=-1,r=e?e.length:0,i=Array(r);while(++t<r)i[t]=a(e[t],t,e);return i}function m(e,a){var t=-1,r=a.length,i=e.length;while(++t<r)e[i+t]=a[t];return e}var f=Object.prototype,v=f.hasOwnProperty,b=f.toString,h=u.Symbol,C=f.propertyIsEnumerable,g=h?h.isConcatSpreadable:void 0,w=Math.max;function y(e,a,t,r,i){var s=-1,l=e.length;t||(t=k),i||(i=[]);while(++s<l){var o=e[s];a>0&&t(o)?a>1?y(o,a-1,t,r,i):m(i,o):r||(i[i.length]=o)}return i}function _(e,a){return e=Object(e),x(e,a,(function(a,t){return t in e}))}function x(e,a,t){var r=-1,i=a.length,s={};while(++r<i){var l=a[r],o=e[l];t(o,l)&&(s[l]=o)}return s}function F(e,a){return a=w(void 0===a?e.length-1:a,0),function(){var t=arguments,r=-1,i=w(t.length-a,0),s=Array(i);while(++r<i)s[r]=t[a+r];r=-1;var l=Array(a+1);while(++r<a)l[r]=t[r];return l[a]=s,d(e,this,l)}}function k(e){return I(e)||O(e)||!!(g&&e&&e[g])}function q(e){if("string"==typeof e||A(e))return e;var a=e+"";return"0"==a&&1/e==-t?"-0":a}function O(e){return L(e)&&v.call(e,"callee")&&(!C.call(e,"callee")||b.call(e)==i)}var I=Array.isArray;function S(e){return null!=e&&j(e.length)&&!M(e)}function L(e){return $(e)&&S(e)}function M(e){var a=V(e)?b.call(e):"";return a==s||a==l}function j(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=r}function V(e){var a=typeof e;return!!e&&("object"==a||"function"==a)}function $(e){return!!e&&"object"==typeof e}function A(e){return"symbol"==typeof e||$(e)&&b.call(e)==o}var P=F((function(e,a){return null==e?{}:_(e,p(y(a,1),q))}));e.exports=P}).call(this,t("c8ba"))},"89b9":function(e,a,t){"use strict";t("89de")},"89de":function(e,a,t){},"8bb2":function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("a-alert",{staticClass:"mb-15",attrs:{showIcon:!0,message:"微信公众号端链接分享说明",banner:""}},[a("template",{slot:"description"},[a("p",[e._v("1. 开启链接分享卡片之前请先配置完成公众号设置（开发者信息和IP白名单等），"),a("strong",[e._v("否则用户访问商城时会报错")])]),a("p",[e._v("2. 只有通过微信扫码、微信公众号底部菜单和对话框打开H5链接，点击右上角的 “转发给朋友” 才会是卡片形式")])])],2),a("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[a("a-form-item",{staticClass:"mt-30",attrs:{label:"链接分享卡片",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["enabled",{rules:[{required:!0}]}],expression:"['enabled', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:!0}},[e._v("开启")]),a("a-radio",{attrs:{value:!1}},[e._v("关闭")])],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[a("small",[e._v("在微信公众号端分享商品链接时，通过卡片形式展示封面图、标题和描述；")]),a("a-popover",{attrs:{title:!1}},[a("template",{slot:"content"},[a("img",{staticClass:"bg-image",staticStyle:{width:"360px"},attrs:{src:"static/img/client/wxofficial/linkShareCard.png"}})]),a("a",{attrs:{href:"javascript:;"}},[e._v("查看示例")]),a("img",{staticClass:"hiden",attrs:{src:"static/img/client/wxofficial/linkShareCard.png"}})],2)],1)])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("enabled"),expression:"form.getFieldValue('enabled')"}]},[a("a-form-item",{staticClass:"mt-30",attrs:{label:"分享标题",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["title",{rules:[{required:!0,message:"请输入分享标题"}]}],expression:"['title', { rules: [{ required: true, message: '请输入分享标题' }] }]"}]}),a("p",{staticClass:"form-item-help"},[a("small",[e._v("分享链接给微信好友时，链接卡片的标题内容")])])],1),a("a-form-item",{attrs:{label:"分享描述",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["desc",{rules:[{required:!0,message:"请输入分享描述"}]}],expression:"['desc', { rules: [{ required: true, message: '请输入分享描述' }] }]"}]}),a("p",{staticClass:"form-item-help"},[a("small",[e._v("分享链接给微信好友时，链接卡片的描述内容")])])],1),a("a-form-model-item",{attrs:{label:"分享图标",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[a("SelectImage2",{directives:[{name:"decorator",rawName:"v-decorator",value:["imgUrl",{initialValue:"",rules:[{required:!0,message:"请上传分享图标"}]}],expression:"['imgUrl', { initialValue: '', rules: [{ required: true, message: '请上传分享图标' }] }]"}],attrs:{tips:"建议尺寸：宽300 高度300"}})],1)],1),a("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[a("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},i=[],s=(t("d3b7"),t("88bc")),l=t.n(s),o=t("ca00"),n=t("fdd5"),c=t("2af9"),u={components:{SelectImage2:c["i"]},data:function(){return{labelCol:{span:4},wrapperCol:{span:14},isLoading:!1,form:this.$form.createForm(this),record:{}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,n["b"]("share").then((function(a){e.record=a.data.detail,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,a=this.$nextTick,t=this.form;!Object(o["f"])(t.getFieldsValue())&&a((function(){t.setFieldsValue(l()(e,["enabled","title","desc","imgUrl"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},onFormSubmit:function(e){var a=this;this.isLoading=!0,n["c"]("share",{form:e}).then((function(e){return a.$message.success(e.message,1.5)})).finally((function(){return a.isLoading=!1}))}}},d=u,p=(t("9b95"),t("2877")),m=Object(p["a"])(d,r,i,!1,null,"73a94fd8",null);a["default"]=m.exports},"8c72":function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[a("a-form-item",{attrs:{label:"默认登录/注册方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["registerMethod",{rules:[{required:!0}]}],expression:"['registerMethod', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:10}},[e._v("手机号 + 短信验证码")])],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[a("small",[e._v("发送短信服务需要先配置")]),a("router-link",{attrs:{target:"_blank",to:{path:"/setting/sms"}}},[e._v("短信通知设置")])],1),a("p",{staticClass:"extra"},[e._v("使用手机号注册可以实现多种客户端的账号统一，例如H5、微信小程序、APP，是目前最主流的方案")])])],1),a("a-form-item",{attrs:{label:"手动绑定手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["isManualBind",{rules:[{required:!0}]}],expression:"['isManualBind', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("显示")]),a("a-radio",{attrs:{value:0}},[e._v("不显示")])],1),a("div",{staticClass:"form-item-help"},[a("small",[e._v("用户在个人中心页可以手动操作绑定手机号（仅未绑定手机号时显示）")]),a("a-popover",{attrs:{title:!1}},[a("template",{slot:"content"},[a("img",{staticClass:"bg-image",staticStyle:{width:"300px"},attrs:{src:"static/img/client/register/isManualBind.png"}})]),a("a",{attrs:{href:"javascript:;"}},[e._v("查看示例")]),a("img",{staticClass:"hiden",attrs:{src:"static/img/client/register/isManualBind.png"}})],2)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("client-mpWeixin"),expression:"$module('client-mpWeixin')"}]},[a("a-divider",{attrs:{orientation:"left"}},[e._v("微信小程序授权登录")]),a("a-form-item",{attrs:{label:"一键授权登录/注册",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["isOauthMpweixin",{rules:[{required:!0}]}],expression:"['isOauthMpweixin', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[a("span",[e._v("开启")]),a("a-tag",{staticClass:"ml-5",attrs:{color:"green"}},[e._v("推荐")])],1),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("开启后在微信小程序端一键获取用户授权并登录和注册（请先配置微信小程序设置）")]),a("p",{directives:[{name:"show",rawName:"v-show",value:0==e.form.getFieldValue("isOauthMpweixin"),expression:"form.getFieldValue('isOauthMpweixin') == 0"}],staticClass:"extra c-red"},[e._v("关闭后微信小程序端将无法获取用户的openid，同时无法使用微信支付")])])],1),a("a-form-item",{directives:[{name:"show",rawName:"v-show",value:1==e.form.getFieldValue("isOauthMpweixin"),expression:"form.getFieldValue('isOauthMpweixin') == 1"}],attrs:{label:"填写微信头像和昵称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["isPersonalMpweixin",{rules:[{required:!0}]}],expression:"['isPersonalMpweixin', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("开启")]),a("a-radio",{attrs:{value:0}},[a("span",[e._v("关闭")]),a("a-tag",{staticClass:"ml-5",attrs:{color:"green"}},[e._v("推荐")])],1)],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("开启后在微信小程序端一键授权注册时要求用户填写微信头像和昵称，仅首次注册时弹出")])])],1),a("a-form-item",{directives:[{name:"show",rawName:"v-show",value:1==e.form.getFieldValue("isOauthMpweixin"),expression:"form.getFieldValue('isOauthMpweixin') == 1"}],attrs:{label:"注册时绑定手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["isForceBindMpweixin",{rules:[{required:!0}]}],expression:"['isForceBindMpweixin', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[a("span",[e._v("强制绑定")]),a("a-tag",{staticClass:"ml-5",attrs:{color:"green"}},[e._v("推荐")])],1),a("a-radio",{attrs:{value:0}},[e._v("不绑定")])],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("开启后在微信小程序端一键授权注册时强制绑定手机号，仅首次注册时弹出")]),a("p",{directives:[{name:"show",rawName:"v-show",value:0==e.form.getFieldValue("isForceBindMpweixin"),expression:"form.getFieldValue('isForceBindMpweixin') == 0"}],staticClass:"extra c-red"},[e._v("如果不强制绑定手机号，会造成多端情况下同一个用户注册多个账户，强烈推荐绑定手机号")])])],1),a("a-form-item",{directives:[{name:"show",rawName:"v-show",value:1==e.form.getFieldValue("isOauthMpweixin")&&1==e.form.getFieldValue("isForceBindMpweixin"),expression:"form.getFieldValue('isOauthMpweixin') == 1 && form.getFieldValue('isForceBindMpweixin') == 1"}],attrs:{label:"一键获取微信手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["isOauthMobileMpweixin",{rules:[{required:!0}]}],expression:"['isOauthMobileMpweixin', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[a("span",[e._v("开启")]),a("a-tag",{staticClass:"ml-5",attrs:{color:"green"}},[e._v("推荐")])],1),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[a("small",[e._v("开启后在微信小程序端授权获取微信用户的手机号并登录和注册（请先配置微信小程序设置）")]),a("a-popover",{attrs:{title:!1}},[a("template",{slot:"content"},[a("img",{staticClass:"bg-image",staticStyle:{width:"300px"},attrs:{src:"static/img/client/register/isOauthMobileMpweixin.png"}})]),a("a",{attrs:{href:"javascript:;"}},[e._v("查看示例")]),a("img",{staticClass:"hiden",attrs:{src:"static/img/client/register/isOauthMobileMpweixin.png"}})],2)],1),a("p",{directives:[{name:"show",rawName:"v-show",value:1==e.form.getFieldValue("isOauthMobileMpweixin"),expression:"form.getFieldValue('isOauthMobileMpweixin') == 1"}],staticClass:"extra c-red"},[a("span",[e._v("微信官方将于2023年8月26日起对该接口功能收费，每次成功调用收费0.03元；详情 ")]),a("a",{attrs:{href:"https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/getPhoneNumber.html",target:"_blank"}},[e._v("查看文档")])])])],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("client-wxofficial"),expression:"$module('client-wxofficial')"}]},[a("a-divider",{attrs:{orientation:"left"}},[e._v("微信公众号授权登录")]),a("a-form-item",{attrs:{label:"一键授权登录/注册",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["isOauthWxofficial",{rules:[{required:!0}]}],expression:"['isOauthWxofficial', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[a("span",[e._v("开启")]),a("a-tag",{staticClass:"ml-5",attrs:{color:"green"}},[e._v("推荐")])],1),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("开启后在微信公众号端一键获取用户授权并登录和注册（请先配置微信公众号设置）")]),a("p",{directives:[{name:"show",rawName:"v-show",value:0==e.form.getFieldValue("isOauthWxofficial"),expression:"form.getFieldValue('isOauthWxofficial') == 0"}],staticClass:"extra c-red"},[e._v("关闭后微信公众号端将无法获取用户的openid，同时无法使用微信支付")])])],1),a("a-form-item",{directives:[{name:"show",rawName:"v-show",value:1==e.form.getFieldValue("isOauthWxofficial"),expression:"form.getFieldValue('isOauthWxofficial') == 1"}],attrs:{label:"注册时绑定手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["isForceBindWxofficial",{rules:[{required:!0}]}],expression:"['isForceBindWxofficial', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[a("span",[e._v("强制绑定")]),a("a-tag",{staticClass:"ml-5",attrs:{color:"green"}},[e._v("推荐")])],1),a("a-radio",{attrs:{value:0}},[e._v("不绑定")])],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("开启后在微信公众号一键授权注册时强制绑定手机号，仅首次注册时弹出")]),a("p",{directives:[{name:"show",rawName:"v-show",value:0==e.form.getFieldValue("isForceBindWxofficial"),expression:"form.getFieldValue('isForceBindWxofficial') == 0"}],staticClass:"extra c-red"},[e._v("如果不强制绑定手机号，会造成多端情况下同一个用户注册多个账户，强烈推荐绑定手机号")])])],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.checkYoShopPlugin("mpAlipay"),expression:"checkYoShopPlugin('mpAlipay')"}]},[a("a-divider",{attrs:{orientation:"left"}},[e._v("支付宝小程序授权登录")]),a("a-form-item",{attrs:{label:"一键授权登录/注册",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["isOauthMpAlipay",{rules:[{required:!0}]}],expression:"['isOauthMpAlipay', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[a("span",[e._v("开启")]),a("a-tag",{staticClass:"ml-5",attrs:{color:"green"}},[e._v("推荐")])],1),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("开启后在支付宝小程序端一键获取用户授权并登录和注册（请先配置支付宝小程序设置）")]),a("p",{directives:[{name:"show",rawName:"v-show",value:0==e.form.getFieldValue("isOauthMpAlipay"),expression:"form.getFieldValue('isOauthMpAlipay') == 0"}],staticClass:"extra c-red"},[e._v("关闭后支付宝小程序端将无法获取支付宝用户的buyerId，同时无法使用支付宝支付")])])],1),a("a-form-item",{directives:[{name:"show",rawName:"v-show",value:1==e.form.getFieldValue("isOauthMpAlipay"),expression:"form.getFieldValue('isOauthMpAlipay') == 1"}],attrs:{label:"注册时绑定手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["isForceBindMpAlipay",{rules:[{required:!0}]}],expression:"['isForceBindMpAlipay', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[a("span",[e._v("强制绑定")]),a("a-tag",{staticClass:"ml-5",attrs:{color:"green"}},[e._v("推荐")])],1),a("a-radio",{attrs:{value:0}},[e._v("不绑定")])],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("开启后在支付宝小程序端一键授权注册时强制绑定手机号，仅首次注册时弹出")]),a("p",{directives:[{name:"show",rawName:"v-show",value:0==e.form.getFieldValue("isForceBindMpAlipay"),expression:"form.getFieldValue('isForceBindMpAlipay') == 0"}],staticClass:"extra c-red"},[e._v("如果不强制绑定手机号，会造成多端情况下同一个用户注册多个账户，强烈推荐绑定手机号")])])],1)],1),a("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[a("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},i=[],s=(t("d3b7"),t("ddb0"),t("88bc")),l=t.n(s),o=t("ca00"),n=t("fa04"),c=t("f585"),u=t("3267"),d={data:function(){return{SettingSmsSceneEnum:u["a"],key:"register",labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),record:{}}},beforeCreate:function(){Object(o["a"])(this,{isEmpty:o["f"],checkYoShopPlugin:n["b"]})},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,c["a"](this.key).then((function(a){e.record=a.data.values,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,a=this.$nextTick,t=this.form;!Object(o["f"])(t.getFieldsValue())&&a((function(){t.setFieldsValue(l()(e,["registerMethod","isManualBind","isOauthMpweixin","isPersonalMpweixin","isForceBindMpweixin","isOauthMobileMpweixin","isOauthWxofficial","isForceBindWxofficial","isOauthMpAlipay","isForceBindMpAlipay"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},onFormSubmit:function(e){var a=this;this.isLoading=!0,c["b"](this.key,{form:e}).then((function(e){return a.$message.success(e.message,1.5)})).finally((function(){return a.isLoading=!1}))}}},p=d,m=(t("3490"),t("2877")),f=Object(m["a"])(p,r,i,!1,null,"7234e17d",null);a["default"]=f.exports},"9a01":function(e,a,t){"use strict";t("56ee")},"9b95":function(e,a,t){"use strict";t("53dd")},a9b0:function(e,a,t){"use strict";t("036f")},b22d:function(e,a,t){"use strict";t.r(a);t("b0c0"),t("99af");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("a-form-model",{ref:"myForm",staticClass:"my-form",attrs:{model:e.record,"label-col":e.labelCol,wrapperCol:e.wrapperCol}},[a("a-form-model-item",{attrs:{label:"是否开启访问",prop:"enabled",rules:[{required:!0}]}},[a("a-radio-group",{model:{value:e.record.enabled,callback:function(a){e.$set(e.record,"enabled",a)},expression:"record.enabled"}},[a("a-radio",{attrs:{value:1}},[e._v("开启")]),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),a("div",{staticClass:"form-item-help"},[a("small",[e._v("注：如关闭，用户则无法通过支付宝端访问商城")])])],1),a("a-form-model-item",{staticClass:"mt-30",attrs:{label:"小程序 AppID",prop:"appId",rules:[{required:!0,message:"请输入小程序AppID"}]}},[a("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.appId,callback:function(a){e.$set(e.record,"appId",a)},expression:"record.appId"}}),a("div",{staticClass:"form-item-help"},[a("small",[e._v("登录支付宝小程序控制台，小程序应用，记录应用ID（16位数字）")])])],1),a("a-divider",{attrs:{orientation:"left"}},[e._v("接口加签方式")]),a("a-form-model-item",{attrs:{label:"签名算法 (signType)",prop:"signType",rules:[{required:!0,message:"请选择签名算法 (signType)"}]}},[a("a-radio-group",{model:{value:e.record.signType,callback:function(a){e.$set(e.record,"signType",a)},expression:"record.signType"}},[a("a-radio",{attrs:{value:"RSA2"}},[e._v("RSA2")]),a("a-radio",{attrs:{value:"RSA",disabled:!0}},[e._v("RSA")])],1)],1),a("a-form-model-item",{attrs:{label:"加签模式",prop:"signMode",rules:[{required:!0,message:"请选择加签模式"}]}},[a("a-radio-group",{model:{value:e.record.signMode,callback:function(a){e.$set(e.record,"signMode",a)},expression:"record.signMode"}},[a("a-radio",{attrs:{value:10}},[a("span",[e._v("公钥证书")]),a("a-tag",{staticClass:"ml-5",attrs:{color:"green"}},[e._v("推荐")])],1),a("a-radio",{attrs:{value:20}},[e._v("公钥")])],1),a("div",{staticClass:"form-item-help"},[a("small",[e._v("如需使用资金支出类的接口，则必须使用公钥证书模式")])])],1),20===e.record.signMode?a("div",{attrs:{method:e.record.method}},[a("a-form-model-item",{attrs:{label:"支付宝公钥 (alipayPublicKey)",prop:"alipayPublicKey",rules:[{required:!0,message:"请填写支付宝公钥 (alipayPublicKey)"}]}},[a("a-textarea",{attrs:{autoSize:{minRows:4,maxRows:6},autocomplete:"off"},model:{value:e.record.alipayPublicKey,callback:function(a){e.$set(e.record,"alipayPublicKey",a)},expression:"record.alipayPublicKey"}}),a("div",{staticClass:"form-item-help"},[a("small",[e._v('可在 "支付宝开放平台" - "应用信息" - "接口加签方式" - "支付宝公钥" 中复制')])])],1)],1):e._e(),10===e.record.signMode?a("div",{attrs:{method:e.record.method}},[a("a-form-model-item",{attrs:{label:"应用公钥证书",prop:"appCertPublicKey",rules:[{required:!0,message:"需要上传该文件"}]}},[a("InputFile",{attrs:{accept:".crt"},on:{change:function(a){return e.onChangeInputFile(a,arguments,"appCertPublicKey")}},model:{value:e.record.appCertPublicKey,callback:function(a){e.$set(e.record,"appCertPublicKey",a)},expression:"record.appCertPublicKey"}}),a("div",{staticClass:"form-item-help"},[a("small",[e._v('请上传 "appCertPublicKey_xxxxxxxx.crt" 文件')])])],1),a("a-form-model-item",{attrs:{label:"支付宝公钥证书",prop:"alipayCertPublicKey",rules:[{required:!0,message:"需要上传该文件"}]}},[a("InputFile",{attrs:{accept:".crt"},on:{change:function(a){return e.onChangeInputFile(a,arguments,"alipayCertPublicKey")}},model:{value:e.record.alipayCertPublicKey,callback:function(a){e.$set(e.record,"alipayCertPublicKey",a)},expression:"record.alipayCertPublicKey"}}),a("div",{staticClass:"form-item-help"},[a("small",[e._v('请上传 "alipayCertPublicKey_RSA2.crt" 文件')])])],1),a("a-form-model-item",{attrs:{label:"支付宝根证书",prop:"alipayRootCert",rules:[{required:!0,message:"需要上传该文件"}]}},[a("InputFile",{attrs:{accept:".crt"},on:{change:function(a){return e.onChangeInputFile(a,arguments,"alipayRootCert")}},model:{value:e.record.alipayRootCert,callback:function(a){e.$set(e.record,"alipayRootCert",a)},expression:"record.alipayRootCert"}}),a("div",{staticClass:"form-item-help"},[a("small",[e._v('请上传 "alipayRootCert.crt" 文件')])])],1)],1):e._e(),a("a-form-model-item",{attrs:{label:"应用私钥 (privateKey)",prop:"merchantPrivateKey",rules:[{required:!0,message:"请填写应用私钥 (privateKey)"}]}},[a("a-textarea",{attrs:{autoSize:{minRows:4,maxRows:6},autocomplete:"off"},model:{value:e.record.merchantPrivateKey,callback:function(a){e.$set(e.record,"merchantPrivateKey",a)},expression:"record.merchantPrivateKey"}}),a("div",{staticClass:"form-item-help"},[a("small",[e._v('查看 "应用私钥RSA2048-敏感数据，请妥善保管.txt" 文件，将全部内容复制到此处')])])],1),a("a-divider",{attrs:{orientation:"left"}},[e._v("授权域名设置")]),e._l(e.domainList,(function(t,r){return a("a-form-item",{key:r,staticClass:"mt-30",attrs:{label:t.name,labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[a("span",{staticClass:"f-14"},[e._v(e._s("".concat(t.protocol,"://").concat(e.domain)))]),a("a",{staticClass:"ml-15 f-12",attrs:{href:"javascript:void(0);"},on:{click:function(a){return e.handleCopyLink(e.domain)}}},[e._v("点击复制")]),a("p",{staticClass:"form-item-help"},[a("small",[e._v("登录支付宝小程序控制台，开发 - 开发设置 - 服务器域名白名单，添加"+e._s(t.protocol)+"域名")])])])})),a("a-form-model-item",{attrs:{wrapperCol:{offset:e.labelCol.span}}},[a("a-button",{attrs:{type:"primary",loading:e.isBtnLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],2)],1)],1)},i=[],s=t("5530"),l=(t("d3b7"),t("2ef0")),o=t("ca00"),n=t("67e5"),c=t("7cdd"),u=[{name:"服务器域名白名单",protocol:"https"}],d={enabled:0,appId:"",signType:"RSA2",signMode:10,alipayPublicKey:"",appCertPublicKey:"",alipayCertPublicKey:"",alipayRootCert:"",merchantPrivateKey:""},p={components:{InputFile:n["a"]},data:function(){return{isLoading:!1,isBtnLoading:!1,labelCol:{span:4},wrapperCol:{span:10},form:this.$form.createForm(this),key:"basic",domainList:u,record:Object(l["cloneDeep"])(d),uploadFiles:{appCertPublicKey:null,alipayCertPublicKey:null,alipayRootCert:null},domain:""}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,c["a"](this.key).then((function(a){e.record=Object(s["a"])(Object(s["a"])({},d),a.data.detail),e.domain=a.data.domain})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,a=this.$nextTick,t=this.form;!Object(o["f"])(t.getFieldsValue())&&a((function(){t.setFieldsValue(pick(e,["enabled","appId"]))}))},handleCopyLink:function(e){var a=this;this.$copyText(e).then((function(e){a.$message.success("复制成功",.8)}))},handleSubmit:function(e){var a=this;e.preventDefault(),this.$refs.myForm.validate((function(e){if(!e)return!1;a.onSubmitForm()}))},onChangeInputFile:function(e,a,t){var r=this.uploadFiles;null!==a[1]&&(r[t]=a[1])},onSubmitForm:function(){var e=this;this.isLoading=!0,this.isBtnLoading=!0;var a=this.buildFormData();c["c"](a).then((function(a){return e.$message.success(a.message,1.5)})).finally((function(){e.isBtnLoading=!1,e.isLoading=!1}))},buildFormData:function(){this.key;var e=this.record,a=this.uploadFiles,t=new FormData;for(var r in e)t.append(r,e[r]);for(var i in a)null!=a[i]&&t.append(i,a[i]);return t}}},m=p,f=(t("79e0"),t("2877")),v=Object(f["a"])(m,r,i,!1,null,"0284bdca",null);a["default"]=v.exports},e36a:function(e,a,t){"use strict";t.r(a);t("b0c0"),t("99af");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[a("a-form-item",{staticClass:"mt-30",attrs:{label:"是否开启访问",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["enabled",{rules:[{required:!0}]}],expression:"['enabled', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:!0}},[e._v("开启")]),a("a-radio",{attrs:{value:!1}},[e._v("关闭")])],1),a("div",{staticClass:"form-item-help"},[a("small",[e._v("注：如关闭，用户则无法通过微信小程序端访问商城")])])],1),a("a-form-item",{staticClass:"mt-30",attrs:{label:"小程序 AppID",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["app_id",{rules:[{required:!0,message:"请输入小程序AppID"}]}],expression:"['app_id', { rules: [{ required: true, message: '请输入小程序AppID' }] }]"}]}),a("p",{staticClass:"form-item-help"},[a("small",[e._v("登录微信小程序平台，开发 - 开发管理 - 开发设置，记录AppID (小程序ID)")])])],1),a("a-form-item",{attrs:{label:"小程序 AppSecret",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["app_secret",{rules:[{required:!0,message:"请输入小程序AppSecret"}]}],expression:"['app_secret', { rules: [{ required: true, message: '请输入小程序AppSecret' }] }]"}],attrs:{type:"password"}}),a("p",{staticClass:"form-item-help"},[a("small",[e._v("登录微信小程序平台，开发 - 开发管理 - 开发设置，记录AppSecret (小程序密钥)")])])],1),a("a-form-item",{attrs:{label:"发货信息管理",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["enableShipping",{rules:[{required:!0}]}],expression:"['enableShipping', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:!0}},[e._v("开启")]),a("a-radio",{attrs:{value:!1}},[e._v("关闭")])],1),a("p",{staticClass:"form-item-help"},[a("small",[e._v("登录微信小程序平台，在功能菜单中查找是否存在《发货信息管理》，如果存在则需开启")])])],1),a("a-divider",{attrs:{orientation:"left"}},[e._v("授权域名设置")]),e._l(e.domainList,(function(t,r){return a("a-form-item",{key:r,staticClass:"mt-30",attrs:{label:"".concat(t.name,"合法域名"),labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[a("span",{staticClass:"f-14"},[e._v(e._s("".concat(t.protocol,"://").concat(e.domain)))]),a("a",{staticClass:"ml-15 f-12",attrs:{href:"javascript:void(0);"},on:{click:function(a){return e.handleCopyLink("".concat(t.protocol,"://").concat(e.domain))}}},[e._v("点击复制")]),a("p",{staticClass:"form-item-help"},[a("small",[e._v("登录小程序平台，开发 - 开发管理 - 开发设置 - 服务器域名，修改"+e._s(t.protocol)+"协议业务域名")])])])})),a("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[a("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],2)],1)],1)},i=[],s=(t("d3b7"),t("88bc")),l=t.n(s),o=t("ca00"),n=t("5530"),c=t("b775"),u={detail:"/client.wxapp.setting/detail",update:"/client.wxapp.setting/update"};function d(e){return Object(c["b"])({url:u.detail,method:"get",params:{key:e}})}function p(e,a){return Object(c["b"])({url:u.update,method:"post",data:Object(n["a"])({key:e},a)})}var m=[{name:"request",protocol:"https"},{name:"socket",protocol:"wss"},{name:"uploadFile",protocol:"https"},{name:"downloadFile",protocol:"https"}],f={data:function(){return{labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),key:"basic",domainList:m,record:{},domain:""}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,d(this.key).then((function(a){e.record=a.data.detail,e.domain=a.data.domain,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,a=this.$nextTick,t=this.form;!Object(o["f"])(t.getFieldsValue())&&a((function(){t.setFieldsValue(l()(e,["enabled","app_id","app_secret","enableShipping"]))}))},handleCopyLink:function(e){var a=this;this.$copyText(e).then((function(e){a.$message.success("复制成功",.8)}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},onFormSubmit:function(e){var a=this;this.isLoading=!0,p(this.key,{form:e}).then((function(e){return a.$message.success(e.message,1.5)})).finally((function(){return a.isLoading=!1}))}}},v=f,b=(t("3fac"),t("2877")),h=Object(b["a"])(v,r,i,!1,null,"39a1a04e",null);a["default"]=h.exports},e6a1:function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("a-form-model",{ref:"myForm",staticClass:"my-form",attrs:{model:e.record,labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-form-model-item",{attrs:{label:"开启商城客服",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[a("a-radio-group",{model:{value:e.record.enabled,callback:function(a){e.$set(e.record,"enabled",a)},expression:"record.enabled"}},[a("a-radio",{attrs:{value:1}},[e._v("开启")]),a("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),a("p",{staticClass:"form-item-help"},[a("small",[e._v("开启后将在用户端商品详情页、个人中心页显示在线客服按钮")])])],1),1==e.record.enabled?a("div",[a("a-form-model-item",{attrs:{label:"在线客服方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[a("a-radio-group",{model:{value:e.record.provider,callback:function(a){e.$set(e.record,"provider",a)},expression:"record.provider"}},[a("a-radio",{attrs:{value:"myznkf"}},[e._v("蚂蚁智能客服")])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"myznkf"===e.record.provider,expression:"record.provider === 'myznkf'"}],staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[a("span",[e._v("云客服地址：")]),a("a",{attrs:{href:"https://csmng.cloud.alipay.com/ccm.htm#/home",target:"_blank"}},[e._v("https://csmng.cloud.alipay.com/ccm.htm#/home")])])])],1),"myznkf"===e.record.provider?a("div",[a("a-form-model-item",{attrs:{label:"企业编码 tntInstId",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"config.myznkf.tntInstId",rules:{required:!0,message:"请输入企业编码"}}},[a("a-input",{model:{value:e.record.config.myznkf.tntInstId,callback:function(a){e.$set(e.record.config.myznkf,"tntInstId",a)},expression:"record.config.myznkf.tntInstId"}}),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[a("span",[e._v("在")]),a("a",{attrs:{href:"https://csmng.cloud.alipay.com/ccm.htm#/home",target:"_blank"}},[e._v("云客服")]),a("span",[e._v("中进入 设置 -> 服务窗配置，点击操作栏中的 部署 ，获取 tntInstId（租户ID）和 scene（聊天窗ID）")])])])],1),a("a-form-model-item",{attrs:{label:"聊天窗编码 scene",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"config.myznkf.scene",rules:{required:!0,message:"请输入聊天窗编码"}}},[a("a-input",{model:{value:e.record.config.myznkf.scene,callback:function(a){e.$set(e.record.config.myznkf,"scene",a)},expression:"record.config.myznkf.scene"}})],1)],1):e._e()],1):e._e(),a("a-form-model-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[a("a-button",{attrs:{type:"primary",loading:e.confirmLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)],1)},i=[],s=(t("d3b7"),t("2ef0")),l=t("7cdd"),o={enabled:0,provider:"myznkf",config:{myznkf:{tntInstId:"",scene:""}}},n={data:function(){return{key:"customer",labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,confirmLoading:!1,record:Object(s["cloneDeep"])(o)}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,l["a"](this.key).then((function(a){return e.record=a.data.detail})).finally((function(){return e.isLoading=!1}))},handleSubmit:function(e){var a=this;this.$refs.myForm.validate((function(e){e&&(a.confirmLoading=!0,l["b"](a.key,{form:a.record}).then((function(e){a.$message.success(e.message,1.5)})).finally((function(e){return a.confirmLoading=!1})))}))}}},c=n,u=(t("89b9"),t("2877")),d=Object(u["a"])(c,r,i,!1,null,"4c858f5a",null);a["default"]=d.exports},f09e:function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("a-spin",{attrs:{spinning:e.isLoading}},[a("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[a("a-form-item",{staticClass:"mt-30",attrs:{label:"是否开启访问",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["enabled",{rules:[{required:!0}]}],expression:"['enabled', { rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:!0}},[e._v("开启")]),a("a-radio",{attrs:{value:!1}},[e._v("关闭")])],1),a("div",{staticClass:"form-item-help"},[a("small",[e._v("注：如关闭，用户则无法通过微信中公众号端访问商城")])])],1),a("a-form-item",{staticClass:"mt-30",attrs:{label:"公众号名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,message:"请输入公众号名称"}]}],expression:"['name', { rules: [{ required: true, message: '请输入公众号名称' }] }]"}]})],1),a("a-form-item",{attrs:{label:"原始ID",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["originalId",{rules:[{required:!0,message:"请输入公众号原始ID"}]}],expression:"['originalId', { rules: [{ required: true, message: '请输入公众号原始ID' }] }]"}]}),a("p",{staticClass:"form-item-help"},[a("small",[e._v("登录微信公众平台，设置与开发 - 公众号设置 - 注册信息，查看原始ID")])])],1),a("a-form-item",{attrs:{label:"二维码图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectImage2",{directives:[{name:"decorator",rawName:"v-decorator",value:["qrcodeImageUrl",{initialValue:"",rules:[{required:!0,message:"请上传分享图标"}]}],expression:"['qrcodeImageUrl', { initialValue: '', rules: [{ required: true, message: '请上传分享图标' }] }]"}],attrs:{tips:"建议尺寸：宽430 高度430"}}),a("p",{staticClass:"form-item-help"},[a("small",[e._v("登录微信公众平台，设置与开发 - 公众号设置 - 公开信息，下载公众号二维码（15cm）")])])],1),a("a-divider",{attrs:{orientation:"left"}},[e._v("开发者信息")]),a("a-form-item",{staticClass:"mt-30",attrs:{label:"开发者 AppID",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["appId",{rules:[{required:!0,message:"请输入开发者AppID"}]}],expression:"['appId', { rules: [{ required: true, message: '请输入开发者AppID' }] }]"}]}),a("p",{staticClass:"form-item-help"},[a("small",[e._v("登录微信公众平台，设置与开发 - 基本配置 - 公众号开发信息，记录开发者ID(AppID)")])])],1),a("a-form-item",{attrs:{label:"开发者 AppSecret",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["appSecret",{rules:[{required:!0,message:"请输入开发者AppSecret"}]}],expression:"['appSecret', { rules: [{ required: true, message: '请输入开发者AppSecret' }] }]"}],attrs:{type:"password"}}),a("p",{staticClass:"form-item-help"},[a("small",[e._v("登录微信公众平台，设置与开发 - 基本配置 - 公众号开发信息，设置开发者密码(AppSecret)")])])],1),a("a-divider",{attrs:{orientation:"left"}},[e._v("授权域名设置")]),a("a-form-item",{staticClass:"mt-30",attrs:{label:"服务器IP白名单",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[a("span",{staticClass:"f-14"},[e._v(e._s(e.serverIP))]),a("a",{staticClass:"ml-15 f-12",attrs:{href:"javascript:void(0);"},on:{click:function(a){return e.handleCopyText(e.serverIP)}}},[e._v("点击复制")]),a("p",{staticClass:"form-item-help"},[a("small",[e._v("登录微信公众平台，设置与开发 - 基本配置 - IP白名单，填写服务器IP")])])]),a("a-form-item",{attrs:{label:"业务域名",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[a("span",{staticClass:"f-14"},[e._v(e._s(e.domain))]),a("a",{staticClass:"ml-15 f-12",attrs:{href:"javascript:void(0);"},on:{click:function(a){return e.handleCopyText(e.domain)}}},[e._v("点击复制")]),a("p",{staticClass:"form-item-help"},[a("small",[e._v("登录微信公众平台，设置与开发 - 公众号设置 - 功能设置，填写业务域名")])])]),a("a-form-item",{attrs:{label:"JS接口安全域名",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[a("span",{staticClass:"f-14"},[e._v(e._s(e.domain))]),a("a",{staticClass:"ml-15 f-12",attrs:{href:"javascript:void(0);"},on:{click:function(a){return e.handleCopyText(e.domain)}}},[e._v("点击复制")]),a("p",{staticClass:"form-item-help"},[a("small",[e._v("登录微信公众平台，设置与开发 - 公众号设置 - 功能设置，填写JS接口安全域名")])])]),a("a-form-item",{attrs:{label:"网页授权域名",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[a("span",{staticClass:"f-14"},[e._v(e._s(e.domain))]),a("a",{staticClass:"ml-15 f-12",attrs:{href:"javascript:void(0);"},on:{click:function(a){return e.handleCopyText(e.domain)}}},[e._v("点击复制")]),a("p",{staticClass:"form-item-help"},[a("small",[e._v("登录微信公众平台，设置与开发 - 公众号设置 - 功能设置，填写网页授权域名")])])]),a("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[a("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},i=[],s=(t("d3b7"),t("88bc")),l=t.n(s),o=t("ca00"),n=t("fdd5"),c=t("2af9"),u={components:{SelectImage2:c["i"]},data:function(){return{labelCol:{span:4},wrapperCol:{span:12},isLoading:!1,form:this.$form.createForm(this),record:{},domain:"",serverUrl:"",serverIP:"",radioStyle:{display:"block",marginBottom:"16px"}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,n["a"]().then((function(a){e.record=a.data.setting,e.domain=a.data.domain,e.serverUrl=a.data.serverUrl,e.serverIP=a.data.serverIP,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,a=this.$nextTick,t=this.form;!Object(o["f"])(t.getFieldsValue())&&a((function(){t.setFieldsValue(l()(e,["enabled","name","originalId","qrcodeImageUrl","appId","appSecret"]))}))},handleCopyText:function(e){var a=this;this.$copyText(e).then((function(e){a.$message.success("复制成功",.8)}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},onFormSubmit:function(e){var a=this;this.isLoading=!0,n["c"]("basic",{form:e}).then((function(e){return a.$message.success(e.message,1.5)})).finally((function(){return a.isLoading=!1}))}}},d=u,p=(t("a9b0"),t("2877")),m=Object(p["a"])(d,r,i,!1,null,"75e55880",null);a["default"]=m.exports},f585:function(e,a,t){"use strict";t.d(a,"a",(function(){return l})),t.d(a,"b",(function(){return o}));var r=t("5530"),i=t("b775"),s={detail:"/setting/detail",update:"/setting/update"};function l(e){return Object(i["b"])({url:s.detail,method:"get",params:{key:e}})}function o(e,a){return Object(i["b"])({url:s.update,method:"post",data:Object(r["a"])({key:e},a)})}},fdd5:function(e,a,t){"use strict";t.d(a,"a",(function(){return l})),t.d(a,"b",(function(){return o})),t.d(a,"c",(function(){return n}));var r=t("5530"),i=t("b775"),s={basic:"/client.wxofficial.setting/basic",detail:"/client.wxofficial.setting/detail",update:"/client.wxofficial.setting/update"};function l(e){return Object(i["b"])({url:s.basic,method:"get"})}function o(e){return Object(i["b"])({url:s.detail,method:"get",params:{key:e}})}function n(e,a){return Object(i["b"])({url:s.update,method:"post",data:Object(r["a"])({key:e},a)})}},ffb0:function(e,a,t){}}]);