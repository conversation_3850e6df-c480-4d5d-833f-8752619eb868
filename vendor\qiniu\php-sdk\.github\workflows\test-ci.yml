name: PHP CI with Composer

on:
  push:
    paths-ignore:
      - '**.md'

jobs:
  build:
    strategy:
      fail-fast: false
      max-parallel: 1
      matrix:
        php-versions: ['5.4', '5.5', '5.6', '7.0']
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup php
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}

      - name: Install dependencies
        run: |
          composer self-update
          composer install --no-interaction --prefer-source --dev

      - name: Run cases
        run: |
          ./vendor/bin/phpcs --standard=PSR2 src
          ./vendor/bin/phpcs --standard=PSR2 examples
          ./vendor/bin/phpcs --standard=PSR2 tests
          ./vendor/bin/phpunit --coverage-clover=coverage.xml

        env:
          QINIU_ACCESS_KEY: ${{ secrets.QINIU_ACCESS_KEY }}
          QINIU_SECRET_KEY: ${{ secrets.QINIU_SECRET_KEY }}
          QINIU_TEST_BUCKET: ${{ secrets.QINIU_TEST_BUCKET }}
          QINIU_TEST_DOMAIN: ${{ secrets.QINIU_TEST_DOMAIN }}

      - name: After_success
        run: bash <(curl -s https://codecov.io/bash)
