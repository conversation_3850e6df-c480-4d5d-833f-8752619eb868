<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\sharp;

use cores\BaseModel;
use think\model\relation\HasMany;

/**
 * 整点秒杀-活动会场模型
 * Class Active
 * @package app\common\model\sharp
 */
class Active extends BaseModel
{
    // 定义表名
    protected $name = 'sharp_active';

    // 定义主键
    protected $pk = 'active_id';

    /**
     * 关联活动场次表
     * @return HasMany
     */
    public function activeTime(): HasMany
    {
        return $this->hasMany('ActiveTime', 'active_id')
            ->order(['active_time' => 'asc']);
    }

    /**
     * 活动会场详情
     * @param $activeId
     * @param array $with
     * @return static|array|null
     */
    public static function detail($activeId, array $with = [])
    {
        return static::get($activeId, $with);
    }
}