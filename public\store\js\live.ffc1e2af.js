(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["live"],{ea2d:function(e,t,a){"use strict";a.r(t);a("ac1f"),a("841c"),a("b0c0");var n=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-alert",{staticClass:"mb-15",attrs:{showIcon:!0,message:"微信小程序直播操作说明",banner:""}},[t("template",{slot:"description"},[t("p",[e._v(" 1. 登录 "),t("a",{attrs:{href:"https://mp.weixin.qq.com/",target:"_blank"}},[e._v("微信小程序运营平台")]),e._v("，点击左侧菜单栏 “直播”，点击 “创建直播间” 按钮。 ")]),t("p",[e._v('2. 点击本页面中的 "同步直播间" 按钮，将直播间列表导入商城系统中。')])])],2),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{attrs:{span:5}},[t("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"sync"},on:{click:function(t){return e.handleSync()}}},[e._v("同步直播间")])],1),t("a-col",{staticClass:"flex flex-x-end",attrs:{span:11,offset:8}},[t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px"},attrs:{placeholder:"请输入直播间名称/主播昵称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"time",fn:function(a){return[t("p",[e._v("开始："+e._s(a.start_time))]),t("p",[e._v("结束："+e._s(a.end_time))])]}},{key:"live_status",fn:function(a){return[t("a-tag",{attrs:{color:e.LiveStatusColorEnum[a]}},[e._v(e._s(e.LiveStatusEnum[a].name))])]}},{key:"is_top",fn:function(a,n){return[t("a-tag",{staticClass:"cur-p",attrs:{color:a?"green":""},on:{click:function(t){return e.handleSetTop(n,a?0:1)}}},[e._v(e._s(a?"是":"否"))])]}}])})],1)},s=[],r=a("5530"),o=(a("d3b7"),a("2af9")),i=a("b775"),c={list:"/live.room/list",sync:"/live.room/sync",setTop:"/live.room/setTop"};function l(e){return Object(i["b"])({url:c.list,method:"get",params:e})}function u(e){return Object(i["b"])({url:c.sync,method:"post",data:e})}function d(e,t){return Object(i["b"])({url:c.setTop,method:"post",data:{id:e,isTop:t}})}var m=a("5c06"),f=new m["a"]([{key:101,name:"直播中",value:101},{key:102,name:"未开始",value:102},{key:103,name:"已结束",value:103},{key:104,name:"禁播",value:104},{key:105,name:"暂停中",value:105},{key:106,name:"异常",value:106},{key:107,name:"已过期",value:107}]),h={101:"green",102:"green",103:"red",104:"red",105:"orange",106:"red",107:"red"},p={name:"Index",components:{STable:o["d"]},data:function(){var e=this;return{queryParam:{search:void 0},isLoading:!1,LiveStatusEnum:f,LiveStatusColorEnum:h,columns:[{title:"直播间ID",dataIndex:"id"},{title:"直播间名称",dataIndex:"room_name"},{title:"主播昵称",dataIndex:"anchor_name",scopedSlots:{customRender:"anchor_name"}},{title:"直播时间",scopedSlots:{customRender:"time"}},{title:"直播状态",dataIndex:"live_status",scopedSlots:{customRender:"live_status"}},{title:"是否置顶",dataIndex:"is_top",scopedSlots:{customRender:"is_top"}},{title:"更新时间",dataIndex:"update_time"}],loadData:function(t){return l(Object(r["a"])(Object(r["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleSync:function(){var e=this;e.isLoading=!0,u().then((function(t){e.$message.success(t.message,1.5),e.queryParam.search=void 0,e.handleRefresh(!0)})).finally((function(){return e.isLoading=!1}))},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleSetTop:function(e,t){var a=this,n=t?"":"取消",s=this.$confirm({title:"您确定要".concat(n,"置顶该直播间吗?"),onOk:function(){return d(e.id,t).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(){return s.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},v=p,_=a("2877"),y=Object(_["a"])(v,n,s,!1,null,null,null);t["default"]=y.exports}}]);