<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\bargain;

use think\response\Json;
use app\api\controller\Controller;
use app\api\model\bargain\Task as TaskModel;
use app\api\model\bargain\Setting as SettingModel;
use app\api\model\bargain\TaskHelp as TaskHelpModel;
use app\common\library\Lock;
use cores\exception\BaseException;

/**
 * 砍价任务控制器
 * Class Checkout
 * @package app\api\controller\bargain
 */
class Task extends Controller
{
    /**
     * 我的砍价列表
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DbException
     */
    public function list(): Json
    {
        $model = new TaskModel;
        $list = $model->getMyList();
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 创建砍价任务
     * @param int $activeId
     * @param string $goodsSkuId
     * @return Json
     * @throws BaseException
     */
    public function partake(int $activeId, string $goodsSkuId): Json
    {
        // 创建砍价任务
        $model = new TaskModel;
        if (!$model->partake($activeId, $goodsSkuId)) {
            return $this->renderError($model->getError() ?: '砍价任务创建失败');
        }
        return $this->renderSuccess(['taskId' => (int)$model['task_id']]);
    }

    /**
     * 获取砍价任务详情
     * @param int $taskId
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(int $taskId): Json
    {
        // 获取砍价详情记录
        $model = new TaskModel;
        $taskInfo = $model->getDetail($taskId);
        // 当前是否为发起人
        $isCreater = $model->isCreater($taskInfo);
        // 当前是否已砍价
        $isCut = $model->isCut($taskId);
        // 砍价规则设置
        $setting = SettingModel::getBasic();
        return $this->renderSuccess(compact('taskInfo', 'isCreater', 'isCut', 'setting'));
    }

    /**
     * 获取砍价任务好友助力榜
     * @param int $taskId
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function helpList(int $taskId): Json
    {
        // 好友助力榜
        $list = TaskHelpModel::getListByTaskId($taskId);
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 帮砍一刀
     * @param int $taskId
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function helpCut(int $taskId): Json
    {
        // 加阻塞锁, 防止并发
        Lock::lockUp("bargain_help_cut_{$taskId}");
        // 砍价任务详情
        $model = TaskModel::detail($taskId);
        // 砍一刀的金额
        $cutMoney = $model->getCutMoney();
        // 帮砍一刀事件
        $status = $model->helpCut();
        // 解除并发锁
        Lock::unLock("bargain_help_cut_{$taskId}");
        if ($status) {
            return $this->renderSuccess(compact('cutMoney'), '帮砍一刀成功，感谢您的帮助');
        }
        return $this->renderError($model->getError() ?: '砍价失败');
    }
}