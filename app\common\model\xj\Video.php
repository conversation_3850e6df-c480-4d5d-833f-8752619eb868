<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\common\model\xj;

use cores\BaseModel;
use think\model\relation\BelongsTo;
use think\model\relation\HasOne;

/**
 * 文章模型
 * Class Video
 * @package app\common\model
 */
class Video extends BaseModel
{
    // 定义表名
    protected $name = 'xj_video';

    // 定义主键
    protected $pk = 'id';

    // 追加字段
    protected $append = ['show_views', 'autoplay'];
    public function getautoplayAttr($value): bool
    {
        return false;
    }
    /**
     * 关联文章封面图
     * @return HasOne
     */
    public function image(): HasOne
    {$module = self::getCalledModule();
        return $this->hasOne("app\\{$module}\\model\\UploadFile", 'file_id', 'image_id')
            ->bind(['image_url' => 'preview_url']);}
    /**
     * 关联文章封面图
     * @return HasOne
     */
    public function videos(): HasOne
    {
        $module = self::getCalledModule();
        return $this->hasOne("app\\{$module}\\model\\UploadFile", 'file_id', 'video_id')
            ->bind(['video_url' => 'preview_url']);
    }
    /**
     * 关联文章分类表
     * @return BelongsTo
     */
    public function category(): BelongsTo
    {
        $module = self::getCalledModule();
        return $this->BelongsTo("app\\{$module}\\model\\xj\\Category", 'category_id');
    }

    /**
     * 文章详情：HTML实体转换回普通字符
     * @param $value
     * @return string
     */
    public function getContentAttr($value): string
    {
        return htmlspecialchars_decode($value);
    }

    /**
     * 展示的浏览次数
     * @param $value
     * @param $data
     * @return mixed
     */
    public function getShowViewsAttr($value, $data)
    {
        return $data['virtual_views'] + $data['actual_views'];
    }

    /**
     * 文章详情
     * @param int $articleId
     * @param array $with
     * @return static|array|null
     */
    public static function detail(int $articleId, array $with = [])
    {
        return self::get($articleId, $with);
    }
}
