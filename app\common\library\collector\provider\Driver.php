<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\library\collector\provider;

use app\common\library\helper;
use app\common\library\Network;
use cores\traits\ErrorTrait;
use cores\exception\BaseException;

/**
 * 物流查询驱动基类
 * Class Driver
 * @package app\common\library\collector\provider\driver
 */
abstract class Driver
{
    use ErrorTrait;

    /**
     * 驱动句柄
     * @var ?Driver
     */
    protected ?Driver $handler = null;

    /**
     * api配置参数
     * @var array
     */
    protected array $options = [];

    /**
     * 采集商品详情
     * @param string $itemId 商品ID
     * @return array
     */
    abstract function detail(string $itemId): array;

    /**
     * 设置api配置参数
     * @param array $options 配置信息
     * @return static|null
     */
    public function setOptions(array $options): ?Driver
    {
        $this->options = $options;
        return $this;
    }

    /**
     * curl请求99api
     * @param string $url API地址
     * @param string $itemId 商品ID
     * @param string $itemIdKeyName 商品ID键名
     * @return mixed|string
     * @throws BaseException
     */
    protected function curlGet(string $url, string $itemId, string $itemIdKeyName = 'itemid')
    {
        $response = Network::curlGet($url, [
            'apikey' => $this->options['apiKey'],
            $itemIdKeyName => $itemId,
        ]);
        // \file_put_contents('test1.json', $response);
        $result = helper::jsonDecode($response);
        if ($result['retcode'] != '0000') {
            throwError(is_string($result['data']) ? $result['data'] : '采集失败');
        }
        return $result;
    }
}