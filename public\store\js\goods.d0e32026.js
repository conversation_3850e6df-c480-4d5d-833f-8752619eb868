(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["goods"],{"06d2":function(e,t,a){"use strict";a("2c3c")},"07b4":function(e,t,a){},"09ff":function(e,t,a){"use strict";a("7c8f")},"0b0d":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("div",{staticClass:"upload-dragger"},[t("a-upload-dragger",{attrs:{accept:".xls, .xlsx",multiple:!1,fileList:e.fileList,showUploadList:!1,beforeUpload:e.beforeUpload,remove:e.handleRemove}},[t("p",{staticClass:"ant-upload-drag-icon"},[t("a-icon",{attrs:{type:"inbox"}})],1),e.fileList.length?t("div",[t("p",{staticClass:"ant-upload-text"},[t("span",[e._v(e._s(e.fileList[0].name))]),t("a",{staticClass:"ml-10",attrs:{href:"javascript:void(0);"},on:{click:function(t){return t.stopPropagation(),e.handleRemove(e.fileList[0])}}},[e._v("删除")])]),t("a-button",{staticClass:"mt-20",attrs:{type:"primary"},on:{click:function(t){return t.stopPropagation(),e.onFormSubmit.apply(null,arguments)}}},[e._v("立即导入")])],1):t("div",[t("p",{staticClass:"ant-upload-text"},[e._v("点击选择文件，或者将文件拖拽至此区域")]),t("p",{staticClass:"ant-upload-hint"},[e._v("仅支持 .xls, .xlsx 格式，限20M以内")])])])],1),t("div",{staticClass:"upload-rules"},[t("p",{staticClass:"title"},[e._v("导入规则")]),t("p",{staticClass:"text"},[e._v(" 1. 上传前请先按照导入模版格式填写信息； "),t("a",{attrs:{href:"static/template/batch-user-goods.xlsx",target:"_blank"}},[e._v("下载模版文件")])]),t("p",{staticClass:"text"},[e._v("2. 导入的订单以手机号做唯一判断，重复数据将无法导入；")]),t("p",{staticClass:"text"},[e._v("3. 单次最多导入200条，超出的部分会被自动删除；")])])])],1)},i=[],o=(a("a434"),a("d3b7"),a("b775")),s={list:"/user.delivery/list",detail:"/user.delivery/detail",delivery:"/user.delivery/delivery",batch:"/user.delivery/batch",eorder:"/user.delivery/eorder"};function l(e){return Object(o["b"])({url:s.batch,method:"post",data:e})}var n={data:function(){return{isLoading:!1,fileList:[],uploadSizeLimit:"20"}},created:function(){},methods:{beforeUpload:function(e){var t=e.size/1024/1024;return t>this.uploadSizeLimit?(this.$message.error("上传的文件大小不能超出".concat(this.uploadSizeLimit,"MB")),!1):(this.fileList=[e],!1)},handleRemove:function(e){var t=this.fileList,a=t.indexOf(e);a>-1&&t.splice(a,1)},onFormSubmit:function(){var e=this,t=this.fileList,a=new FormData;a.append("file",t[0]),this.isLoading=!0,l(a).then((function(t){e.fileList=[],e.$message.success(t.message,1.5),setTimeout((function(){return e.$router.back()}),1200)})).finally((function(){return e.isLoading=!1}))}}},d=n,u=(a("06d2"),a("2877")),c=Object(u["a"])(d,r,i,!1,null,"3cad7247",null);t["default"]=c.exports},1457:function(e,t,a){"use strict";a("a53d")},"16e5":function(e,t,a){},1788:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",{staticClass:"row-item-search"},[t("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[t("a-form-item",{attrs:{label:"商品名称"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goodsName"],expression:"['goodsName']"}],attrs:{placeholder:"请输入商品名称"}})],1),t("a-form-item",{attrs:{label:"商品编码"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goodsNo"],expression:"['goodsNo']"}],attrs:{placeholder:"请输入商品编码"}})],1),t("a-form-item",{attrs:{label:"商品分类"}},[t("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["categoryId",{initialValue:0}],expression:"['categoryId', { initialValue: 0 }]"}],attrs:{treeData:e.categoryListTree,dropdownStyle:{maxHeight:"500px",overflow:"auto"},allowClear:""}})],1),t("a-form-item",{staticClass:"search-btn"},[t("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1),t("div",{staticClass:"row-item-tab clearfix"},[t("div",{staticClass:"tab-list fl-l"},[t("a-radio-group",{attrs:{defaultValue:e.queryParam.listType},on:{change:e.handleTabs}},[t("a-radio-button",{attrs:{value:"all"}},[e._v("全部")]),t("a-radio-button",{attrs:{value:"on_sale"}},[e._v("出售中")]),t("a-radio-button",{attrs:{value:"off_sale"}},[e._v("已下架")]),t("a-radio-button",{attrs:{value:"sold_out"}},[e._v("已售罄")])],1)],1),e.$auth("/goods/create")?t("a-button",{staticClass:"fl-l",attrs:{type:"primary",icon:"plus"},on:{click:function(t){return e.handleCreate()}}},[e._v("创建商品")]):e._e(),e.$module("goods-import")&&e.$auth("/goods/import/batch")?t("a-button",{staticClass:"fl-l",attrs:{icon:"arrow-up"},on:{click:function(t){return e.handleImport()}}},[e._v("批量导入")]):e._e(),e.selectedRowKeys.length?t("div",{staticClass:"button-group"},[t("a-button-group",{staticClass:"ml-10"},[t("a-button",{directives:[{name:"action",rawName:"v-action:status",arg:"status"}],attrs:{icon:"arrow-up"},on:{click:function(t){return e.handleUpdateStatus(e.selectedRowKeys,!0)}}},[e._v("上架")]),t("a-button",{directives:[{name:"action",rawName:"v-action:status",arg:"status"}],attrs:{icon:"arrow-down"},on:{click:function(t){return e.handleUpdateStatus(e.selectedRowKeys,!1)}}},[e._v("下架")]),t("a-button",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],attrs:{icon:"delete"},on:{click:function(t){return e.handleDelete(e.selectedRowKeys)}}},[e._v("删除")])],1)],1):e._e()],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"goods_id",loading:e.isLoading,columns:e.columns,data:e.loadData,rowSelection:e.rowSelection,pageSize:15,scroll:{x:1450}},scopedSlots:e._u([{key:"goods_image",fn:function(e){return t("span",{},[t("a",{attrs:{title:"点击查看原图",href:e,target:"_blank"}},[t("img",{attrs:{width:"50",height:"50",src:e,alt:"商品图片"}})])])}},{key:"goods_name",fn:function(a){return t("span",{},[t("p",{staticClass:"twoline-hide",staticStyle:{width:"270px"}},[e._v(e._s(a))])])}},{key:"status",fn:function(a,r){return t("span",{},[t("a-tag",{staticClass:"cur-p",attrs:{color:10==a?"green":"red"},on:{click:function(t){return e.handleUpdateStatus([r.goods_id],10!=a)}}},[e._v(e._s(10==a?"上架":"下架"))])],1)}},{key:"action",fn:function(a,r){return t("div",{staticClass:"actions"},[e.$auth("/goods/update")?t("router-link",{attrs:{to:{path:"/goods/update",query:{goodsId:r.goods_id}}}},[e._v("编辑")]):e._e(),e.$module("goods-copy")&&e.$auth("/goods/copy")?t("router-link",{attrs:{to:{path:"/goods/copy",query:{goodsId:r.goods_id}}}},[e._v("复制")]):e._e(),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete([r.goods_id])}}},[e._v("删除")])],1)}}])})],1)},i=[],o=a("5530"),s=(a("d3b7"),a("d084")),l=a("2af9"),n=a("8243"),d=[{title:"商品ID",dataIndex:"goods_id"},{title:"商品图片",dataIndex:"goods_image",scopedSlots:{customRender:"goods_image"}},{title:"商品名称",dataIndex:"goods_name",width:"302px",scopedSlots:{customRender:"goods_name"}},{title:"商品价格",dataIndex:"goods_price_min",scopedSlots:{customRender:"goods_price_min"}},{title:"总销量",dataIndex:"sales_actual"},{title:"库存总量",dataIndex:"stock_total"},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",width:"180px",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"150px",fixed:"right",scopedSlots:{customRender:"action"}}],u={name:"Index",components:{ContentHeader:l["a"],STable:l["d"]},data:function(){var e=this;return{searchForm:this.$form.createForm(this),categoryListTree:[],queryParam:{listType:"all"},isLoading:!1,columns:d,selectedRowKeys:[],loadData:function(t){return s["f"](Object(o["a"])(Object(o["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){this.$route.query.listType&&(this.queryParam.listType=this.$route.query.listType),this.getCategoryList()},computed:{rowSelection:function(){return{selectedRowKeys:this.selectedRowKeys,onChange:this.onSelectChange}}},methods:{onSelectChange:function(e){this.selectedRowKeys=e},handleTabs:function(e){this.queryParam.listType=e.target.value,this.handleRefresh(!0)},handleSearch:function(e){var t=this;e.preventDefault(),this.searchForm.validateFields((function(e,a){e||(t.queryParam=Object(o["a"])(Object(o["a"])({},t.queryParam),a),t.handleRefresh(!0))}))},handleReset:function(){this.searchForm.resetFields()},getCategoryList:function(){var e=this;this.isLoading=!0,n["a"].getListFromScreen().then((function(t){e.categoryListTree=t})).finally((function(){return e.isLoading=!1}))},handleUpdateStatus:function(e){var t=this,a=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(!this.$auth("/goods/index.status"))return!1;this.isLoading=!0,s["h"]({goodsIds:e,state:a}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){t.isLoading=!1}))},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return s["c"]({goodsIds:e}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleCreate:function(){this.$router.push("/goods/create")},handleImport:function(){this.$router.push("/goods/import/batch")},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.selectedRowKeys=[],this.$refs.table.refresh(e)}}},c=u,m=(a("dd3e"),a("2877")),p=Object(m["a"])(c,r,i,!1,null,"5cdd2d32",null);t["default"]=p.exports},1827:function(e,t,a){"use strict";a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return s})),a.d(t,"b",(function(){return l}));var r=a("b775"),i={list:"/goods.import/list",batch:"/goods.import/batch",delete:"/goods.import/delete"};function o(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function s(e){return Object(r["b"])({url:i.batch,method:"post",data:e})}function l(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},"19d3":function(e,t,a){"use strict";a.d(t,"e",(function(){return o})),a.d(t,"b",(function(){return s})),a.d(t,"a",(function(){return l})),a.d(t,"d",(function(){return n})),a.d(t,"c",(function(){return d}));var r=a("b775"),i={list:"/goods.service/list",all:"/goods.service/all",add:"/goods.service/add",edit:"/goods.service/edit",delete:"/goods.service/delete"};function o(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function s(e){return Object(r["b"])({url:i.all,method:"get",params:e})}function l(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function n(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function d(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},2742:function(e,t,a){"use strict";a("fe55")},"2c3c":function(e,t,a){},"2d98":function(e,t,a){"use strict";a("bfce")},3686:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e.isLoading?e._e():t("a-table",{attrs:{rowKey:"category_id",columns:e.columns,dataSource:e.categoryList,defaultExpandAllRows:!0,expandIconColumnIndex:1,pagination:!1,loading:e.isLoading},scopedSlots:e._u([{key:"status",fn:function(a){return t("span",{},[t("a-tag",{attrs:{color:a?"green":""}},[e._v(e._s(a?"显示":"隐藏"))])],1)}},{key:"action",fn:function(a,r){return t("span",{},[t("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")])])}}],null,!1,1032135766)}),t("AddForm",{ref:"AddForm",attrs:{categoryList:e.categoryList},on:{handleSubmit:e.handleRefresh}}),t("EditForm",{ref:"EditForm",attrs:{categoryList:e.categoryList},on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=(a("d3b7"),a("2f71")),s=a("2af9"),l=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:"新增商品分类",width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"分类名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),t("a-form-item",{attrs:{label:"上级分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["parent_id",{initialValue:0}],expression:"['parent_id', { initialValue: 0}]"}],attrs:{treeData:e.categoryListTree,dropdownStyle:{maxHeight:"400px",overflow:"auto"},allowClear:""}})],1),t("a-form-item",{attrs:{label:"分类图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["image_id"],expression:"['image_id']"}]})],1),t("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("显示")]),t("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},n=[],d=(a("99af"),a("8243")),u={components:{SelectImage:s["h"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),categoryListTree:[]}},methods:{add:function(){this.visible=!0,this.getCategoryList()},getCategoryList:function(){var e=this.categoryList,t=d["a"].formatTreeData(e);this.categoryListTree=[{title:"顶级分类",key:0,value:0}].concat(t)},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){e||t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,o["a"]({form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(e){t.confirmLoading=!1}))}}},c=u,m=a("2877"),p=Object(m["a"])(c,l,n,!1,null,null,null),v=p.exports,f=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:"编辑商品分类",width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"分类名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),t("a-form-item",{attrs:{label:"上级分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["parent_id"],expression:"['parent_id']"}],attrs:{treeData:e.categoryListTree,dropdownStyle:{maxHeight:"400px",overflow:"auto"},allowClear:""}})],1),t("a-form-item",{attrs:{label:"分类图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["image_id"],expression:"['image_id']"}],attrs:{defaultList:e.record.image?[e.record.image]:[]}})],1),t("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("显示")]),t("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},g=[],h=a("2ef0"),b=a.n(h),_={components:{SelectImage:s["h"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),categoryListTree:[],record:{}}},methods:{edit:function(e){this.visible=!0,this.record=e,this.getCategoryList(),this.setFieldsValue()},setFieldsValue:function(){var e=this.record,t=this.form.setFieldsValue;this.$nextTick((function(){t(b.a.pick(e,["name","parent_id","image_id","status","sort"]))}))},getCategoryList:function(){var e=this.categoryList,t=d["a"].formatTreeData(e);this.categoryListTree=[{title:"顶级分类",key:0,value:0}].concat(t)},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){e||t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,o["c"]({categoryId:this.record["category_id"],form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(e){t.confirmLoading=!1}))}}},C=_,y=Object(m["a"])(C,f,g,!1,null,null,null),w=y.exports,x={name:"Index",components:{STable:s["d"],AddForm:v,EditForm:w},data:function(){return{categoryList:[],queryParam:{},isLoading:!1,columns:[{title:"分类ID",dataIndex:"category_id"},{title:"分类名称",dataIndex:"name"},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}]}},created:function(){this.getCategoryList(!0)},methods:{getCategoryList:function(e){var t=this;e&&(this.isLoading=!0),o["d"]().then((function(e){t.categoryList=e.data.list})).finally((function(){return t.isLoading=!1}))},handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return o["b"]({categoryId:e["category_id"]}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleRefresh:function(){this.getCategoryList()}}},S=x,L=Object(m["a"])(S,r,i,!1,null,null,null);t["default"]=L.exports},"48d0":function(e,t,a){"use strict";a.r(t);a("b0c0");var r,i=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.pageTitle))]),t("div",{staticClass:"table-operator"},[t("a-row",{staticClass:"row-item-search"},[t("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[t("a-form-item",{attrs:{label:"导入状态"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:-1}],expression:"['status', { initialValue: -1 }]"}]},[t("a-select-option",{attrs:{value:-1}},[e._v("全部")]),e._l(e.ImportStatusEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])}))],2)],1),t("a-form-item",{staticClass:"search-btn"},[t("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1),t("div",{staticClass:"row-item-tab clearfix"},[e.$auth("/goods/import/batch")?t("a-button",{staticClass:"fl-l",attrs:{type:"primary",icon:"arrow-up"},on:{click:function(t){return e.handleImport()}}},[e._v("批量导入")]):e._e()],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"start_time",fn:function(a){return[t("span",[e._v(e._s(a||"--"))])]}},{key:"end_time",fn:function(a){return[t("span",[e._v(e._s(a||"--"))])]}},{key:"status",fn:function(a){return t("span",{},[t("a-tag",{attrs:{color:e.ImportStatusColorEnum[a]}},[e._v(e._s(e.ImportStatusEnum[a].name))])],1)}},{key:"action",fn:function(a,r){return t("span",{staticClass:"actions"},[t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")]),r.fail_count>0?t("a",{on:{click:function(t){return e.handleFailLog(r)}}},[e._v("失败日志")]):e._e()])}}])}),t("a-modal",{attrs:{title:"失败日志"},on:{ok:function(t){e.visibleFailLog=!1}},model:{value:e.visibleFailLog,callback:function(t){e.visibleFailLog=t},expression:"visibleFailLog"}},[t("div",{staticClass:"modal-content"},e._l(e.failLogContent,(function(a,r){return t("p",{key:r,staticClass:"log-item"},[t("span",{staticClass:"mr-5"},[e._v("序号["+e._s(a.goodsSn)+"]")]),t("span",[e._v(e._s(a.message))])])})),0)])],1)},o=[],s=a("5530"),l=a("ade3"),n=(a("d3b7"),a("1827")),d=a("2af9"),u=a("59aa"),c=(r={},Object(l["a"])(r,u["c"].NORMAL.value,""),Object(l["a"])(r,u["c"].COMPLETED.value,"green"),r),m={name:"Index",components:{STable:d["d"]},data:function(){var e=this;return{searchForm:this.$form.createForm(this),queryParam:{},isLoading:!1,ImportStatusEnum:u["c"],ImportStatusColorEnum:c,columns:[{title:"记录ID",dataIndex:"id"},{title:"导入总数量",dataIndex:"total_count"},{title:"导入成功数量",dataIndex:"success_count"},{title:"导入失败数量",dataIndex:"fail_count"},{title:"开始时间",dataIndex:"start_time",scopedSlots:{customRender:"start_time"}},{title:"结束时间",dataIndex:"end_time",scopedSlots:{customRender:"end_time"}},{title:"导入状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(t){return n["c"](Object(s["a"])(Object(s["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))},visibleFailLog:!1,failLogContent:[]}},created:function(){},methods:{handleImport:function(){this.$router.push("/goods/import/batch")},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return n["b"]({id:e["id"]}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleFailLog:function(e){this.visibleFailLog=!0,this.failLogContent=e.fail_log},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},handleSearch:function(e){var t=this;e.preventDefault(),this.searchForm.validateFields((function(e,a){e||(t.queryParam=Object(s["a"])(Object(s["a"])({},t.queryParam),a),t.handleRefresh(!0))}))}}},p=m,v=(a("861f1"),a("2877")),f=Object(v["a"])(p,i,o,!1,null,"4e067966",null);t["default"]=f.exports},"4cfe":function(e,t,a){"use strict";a("16e5")},"540d":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[t("span",[e._v(e._s(e.$route.meta.pageTitle))])]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("div",{staticClass:"container"},[t("div",{staticClass:"upload-dragger"},[t("a-upload-dragger",{attrs:{accept:".xls, .xlsx",multiple:!1,fileList:e.fileList,showUploadList:!1,beforeUpload:e.beforeUpload,remove:e.handleRemove}},[t("p",{staticClass:"ant-upload-drag-icon"},[t("a-icon",{attrs:{type:"cloud-upload"}})],1),e.fileList.length?t("div",[t("p",{staticClass:"ant-upload-text"},[t("span",[e._v(e._s(e.fileList[0].name))]),t("a",{staticClass:"ml-10",attrs:{href:"javascript:void(0);"},on:{click:function(t){return t.stopPropagation(),e.handleRemove(e.fileList[0])}}},[e._v("删除")])]),t("a-button",{staticClass:"mt-20",attrs:{type:"primary"},on:{click:function(t){return t.stopPropagation(),e.onFormSubmit.apply(null,arguments)}}},[e._v("立即导入")])],1):t("div",[t("p",{staticClass:"ant-upload-text"},[e._v("点击选择文件，或者将文件拖拽至此区域")]),t("p",{staticClass:"ant-upload-hint"},[e._v("仅支持 .xls, .xlsx 格式的excel文件，限2M以内")])])])],1),t("div",{staticClass:"import-explain"},[t("h2",{staticClass:"title"},[e._v("导入说明")]),t("a-timeline",{staticClass:"timeline"},[t("a-timeline-item",{staticClass:"timeline-item"},[t("p",{staticClass:"name"},[e._v("下载模板")]),t("ul",{staticClass:"content"},[t("li",{staticClass:"content-li"},[t("span",[e._v("批量导入商品需要系统开启队列服务，可在超管后台中查看是否开启")])]),t("li",{staticClass:"content-li"},[t("span",{staticClass:"mr-5"},[e._v("商品导入前需要您用Excel整理需要导入的商品资料，请先")]),t("a",{attrs:{href:"static/template/batch-goods.xlsx",target:"_blank"}},[e._v("下载商品导入模板")])])])]),t("a-timeline-item",{staticClass:"timeline-item"},[t("p",{staticClass:"name"},[e._v("使用模板")]),t("ul",{staticClass:"content"},[t("li",{staticClass:"content-li"},[t("span",[e._v("模板中最多不能超过500个商品，如超过500个商品，请分批导入")])]),t("li",{staticClass:"content-li"},[t("span",[e._v("模板中的字段含义以及填写规则，可查看模板文件中标题栏的标注")])])])]),t("a-timeline-item",{staticClass:"timeline-item"},[t("p",{staticClass:"name"},[e._v("上传图片")]),t("ul",{staticClass:"content"},[t("li",{staticClass:"content-li"},[t("span",[e._v('需提前在后台上传商品图片，然后填写到模板文件的 "商品图片" 列中')])]),t("li",{staticClass:"content-li"},[t("span",{staticClass:"mr-5"},[e._v("通过文件库上传图片，并复制图片ID集")]),t("a",{attrs:{href:"javascript:void(0);"},on:{click:function(t){return e.handleSelectImage()}}},[e._v("点击上传图片")])])])])],1)],1),t("FilesModal",{ref:"FilesModal",attrs:{multiple:!0,maxNum:10,actions:["delete","move","copyIds"]}})],1)])],1)},i=[],o=(a("a434"),a("d3b7"),a("1827")),s=a("fd0d"),l={components:{FilesModal:s["d"]},data:function(){return{isLoading:!1,fileList:[],uploadSizeLimit:"20"}},created:function(){},methods:{handleSelectImage:function(){this.$refs.FilesModal.show()},beforeUpload:function(e){var t=e.size/1024/1024;return t>this.uploadSizeLimit?(this.$message.error("上传的文件大小不能超出".concat(this.uploadSizeLimit,"MB")),!1):(this.fileList=[e],!1)},handleRemove:function(e){var t=this.fileList,a=t.indexOf(e);a>-1&&t.splice(a,1)},onFormSubmit:function(){var e=this,t=this.fileList,a=new FormData;a.append("file",t[0]),this.isLoading=!0,o["a"](a).then((function(t){e.fileList=[],e.$message.success(t.message,1.8),setTimeout((function(){return e.$router.push("/goods/import/list")}),1500)})).finally((function(){return e.isLoading=!1}))}}},n=l,d=(a("2d98"),a("2877")),u=Object(d["a"])(n,r,i,!1,null,"aa9792ca",null);t["default"]=u.exports},5633:function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form,selfUpdate:!0},on:{submit:e.handleSubmit}},[t("a-tabs",{attrs:{activeKey:e.tabKey,tabBarStyle:{marginBottom:"30px"}},on:{change:e.handleTabs}},[t("a-tab-pane",{key:0,attrs:{tab:"基本信息"}}),t("a-tab-pane",{key:1,attrs:{tab:"规格/库存"}}),t("a-tab-pane",{key:2,attrs:{tab:"商品详情"}}),t("a-tab-pane",{key:3,attrs:{tab:"更多设置"}})],1),t("div",{staticClass:"tabs-content"},[t("div",{directives:[{name:"show",rawName:"v-show",value:0==e.tabKey,expression:"tabKey == 0"}],staticClass:"tab-pane"},[t("a-form-item",{attrs:{label:"商品类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("GoodsType",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_type",{rules:[{required:!0}]}],expression:"['goods_type', { rules: [{ required: true }] }]"}],attrs:{onlyShowChecked:!0},on:{change:function(t){return e.onForceUpdate(!0)}}})],1),t("a-form-item",{attrs:{label:"商品名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['goods_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入商品名称"}})],1),t("a-form-item",{attrs:{label:"商品分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["categorys",{rules:[{required:!0,message:"请至少选择1个商品分类"}]}],expression:"['categorys', { rules: [{ required: true, message: '请至少选择1个商品分类' }] }]"}],attrs:{placeholder:"请选择商品分类",dropdownStyle:{maxHeight:"500px",overflow:"auto"},treeData:e.formData.categoryList,treeCheckable:"",treeCheckStrictly:"",allowClear:""}}),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{target:"_blank",to:{path:"/goods/category/index"}}},[e._v("去新增")]),t("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadCategoryList}},[e._v("刷新")])],1)],1),t("a-form-item",{attrs:{label:"商品图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸：750*750像素, 最多上传10张, 可拖拽图片调整顺序, 第1张将作为商品首图"}},[t("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["imagesIds",{rules:[{required:!0,message:"请至少上传1张商品图片"}]}],expression:"['imagesIds', { rules: [{ required: true, message: '请至少上传1张商品图片' }] }]"}],attrs:{multiple:"",maxNum:10,defaultList:e.formData.goods.goods_images}})],1),t("a-form-item",{attrs:{label:"商品编码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_no"],expression:"['goods_no']"}],attrs:{placeholder:"请输入商品编码"}})],1),10==e.form.getFieldValue("goods_type")?t("a-form-item",{attrs:{label:"配送方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_ind_delivery_type",{initialValue:0,rules:[{required:!0}]}],expression:"['is_ind_delivery_type', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate()}}},[t("a-radio",{attrs:{value:0}},[e._v("系统默认")]),t("a-radio",{attrs:{value:1}},[e._v("单独配置")])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_ind_delivery_type"),expression:"form.getFieldValue('is_ind_delivery_type')"}]},[t("a-form-item",{attrs:{extra:"需在 [设置 - 配送方式] 中，开启支持的配送方式才可生效"}},[t("a-checkbox-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["delivery_type",{initialValue:[10,20],rules:[{required:!0,message:"配送方式至少选择一个"}]}],expression:"['delivery_type', { initialValue: [10, 20], rules: [{ required: true, message: '配送方式至少选择一个' }] }]"}],attrs:{options:[{label:"快递配送",value:10},{label:"上门自提",value:20}]}})],1)],1)],1):e._e(),10==e.form.getFieldValue("goods_type")?t("a-form-item",{attrs:{label:"运费模板",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["delivery_id",{rules:[{required:!0,message:"请选择运费模板"}]}],expression:"['delivery_id', { rules: [{ required: true, message: '请选择运费模板' }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请选择运费模板"}},e._l(e.formData.deliveryList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.delivery_id}},[e._v(e._s(a.name))])})),1),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{target:"_blank",to:{path:"/setting/delivery/template/create"}}},[e._v("新增模板")]),t("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadDeliveryList}},[e._v("刷新")])],1)],1):e._e(),t("a-form-item",{attrs:{label:"商品状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:10,rules:[{required:!0}]}],expression:"['status', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("上架")]),t("a-radio",{attrs:{value:20}},[e._v("下架")])],1)],1),t("a-form-item",{attrs:{label:"商品排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true }] }]"}],attrs:{min:0}})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:1==e.tabKey,expression:"tabKey == 1"}],staticClass:"tab-pane"},[t("a-form-item",{attrs:{label:"规格类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["spec_type",{initialValue:10,rules:[{required:!0}]}],expression:"['spec_type', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate()}}},[t("a-radio",{attrs:{value:10}},[e._v("单规格")]),t("a-radio",{attrs:{value:20}},[e._v("多规格")])],1)],1),20==e.form.getFieldValue("spec_type")?t("div",[t("MultiSpec",{ref:"MultiSpec",attrs:{defaultSpecList:e.formData.goods.specList,defaultSkuList:e.formData.goods.skuList}})],1):e._e(),10==e.form.getFieldValue("spec_type")?t("div",[t("a-form-item",{attrs:{label:"商品价格",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际购买金额，最低0.00"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_price",{rules:[{required:!0,message:"请输入商品价格"}]}],expression:"['goods_price', { rules: [{ required: true, message: '请输入商品价格' }] }]"}],attrs:{min:0,precision:2}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"划线价",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"划线价仅用于商品页展示"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["line_price"],expression:"['line_price']"}],attrs:{min:0,precision:2}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"当前库存数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际库存数量，为0时用户无法下单"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["stock_num",{initialValue:100,rules:[{required:!0,message:"请输入库存数量"}]}],expression:"['stock_num', { initialValue: 100, rules: [{ required: true, message: '请输入库存数量' }] }]"}],attrs:{min:0,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("件")])],1),t("a-form-item",{attrs:{label:"商品重量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际重量，用于计算运费"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_weight",{initialValue:0,rules:[{required:!0,message:"请输入库存数量"}]}],expression:"['goods_weight', { initialValue: 0, rules: [{ required: true, message: '请输入库存数量' }] }]"}],attrs:{min:0}}),t("span",{staticClass:"ml-10"},[e._v("千克 (Kg)")])],1)],1):e._e(),t("a-form-item",{attrs:{label:"库存计算方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["deduct_stock_type",{initialValue:10,rules:[{required:!0}]}],expression:"['deduct_stock_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("下单减库存")]),t("a-radio",{attrs:{value:20}},[e._v("付款减库存")])],1)],1),t("a-form-item",{attrs:{label:"商品限购",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用于限制每人购买该商品的数量"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_restrict",{initialValue:0,rules:[{required:!0}]}],expression:"['is_restrict', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate()}}},[t("a-radio",{attrs:{value:0}},[e._v("关闭")]),t("a-radio",{attrs:{value:1}},[e._v("开启")])],1),e.form.getFieldValue("is_restrict")?t("div",{staticClass:"mt-10"},[t("a-form-item",[t("span",{staticClass:"mr-10"},[e._v("总限购")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_total",{rules:[{required:!0,message:"请输入总限购数量"}]}],expression:"['restrict_total', { rules: [{ required: true, message: '请输入总限购数量' }] }]"}],attrs:{min:1,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("件/人")])],1),t("a-form-item",[t("span",{staticClass:"mr-10"},[e._v("每单限购")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_single",{rules:[{required:!0,message:"请输入每单限购数量"}]}],expression:"['restrict_single', { rules: [{ required: true, message: '请输入每单限购数量' }] }]"}],attrs:{min:1,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("件/人")])],1)],1):e._e()],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:2==e.tabKey,expression:"tabKey == 2"}],staticClass:"tab-pane"},[t("a-form-item",{attrs:{label:"商品详情",labelCol:e.labelCol,wrapperCol:{span:16}}},[t("Ueditor",{directives:[{name:"decorator",rawName:"v-decorator",value:["content",{rules:[{required:!0,message:"商品详情不能为空"}]}],expression:"['content', { rules: [{ required: true, message: '商品详情不能为空' }] }]"}]})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:3==e.tabKey,expression:"tabKey == 3"}],staticClass:"tab-pane"},[t("a-form-item",{attrs:{label:"主图视频",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议视频宽高比19:9，建议时长8-45秒"}},[t("SelectVideo",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_id"],expression:"['video_id']"}],attrs:{multiple:!1,defaultList:e.formData.goods.video?[e.formData.goods.video]:[]}})],1),t("a-form-item",{attrs:{label:"视频封面",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸：750像素*750像素"}},[t("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_cover_id"],expression:"['video_cover_id']"}],attrs:{multiple:!1,defaultList:e.formData.goods.videoCover?[e.formData.goods.videoCover]:[]}})],1),t("a-form-item",{attrs:{label:"商品卖点",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"一句话简述，例如：此款商品美观大方 性价比较高 不容错过"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["selling_point"],expression:"['selling_point']"}],attrs:{placeholder:"请输入商品卖点"}})],1),t("a-form-item",{attrs:{label:"服务与承诺",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[e.formData.serviceList?t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["serviceIds"],expression:"['serviceIds']"}],attrs:{mode:"multiple",placeholder:"请选择服务与承诺"}},e._l(e.formData.serviceList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.service_id}},[e._v(e._s(a.name))])})),1):e._e(),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{target:"_blank",to:{path:"/goods/service/index"}}},[e._v("去新增")]),t("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadServiceList}},[e._v("刷新")])],1)],1),t("a-form-item",{attrs:{label:"初始销量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端展示的销量 = 初始销量 + 实际销量"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sales_initial",{initialValue:0}],expression:"['sales_initial', { initialValue: 0 }]"}]})],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("market-points"),expression:"$module('market-points')"}]},[t("a-divider",{attrs:{orientation:"left"}},[e._v("积分设置")]),t("a-form-item",{attrs:{label:"积分赠送",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后用户购买此商品将获得积分"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_points_gift",{initialValue:1,rules:[{required:!0}]}],expression:"['is_points_gift', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),t("a-form-item",{attrs:{label:"积分抵扣",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后用户购买此商品可以使用积分进行抵扣"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_points_discount",{initialValue:1,rules:[{required:!0}]}],expression:"['is_points_discount', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("user-grade"),expression:"$module('user-grade')"}]},[t("a-divider",{attrs:{orientation:"left"}},[e._v("会员折扣设置")]),t("a-form-item",{attrs:{label:"会员折扣",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后会员折扣，会员购买此商品可以享受会员等级折扣价"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_enable_grade",{initialValue:1,rules:[{required:!0}]}],expression:"['is_enable_grade', { initialValue: 1, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate(!0)}}},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_enable_grade"),expression:"form.getFieldValue('is_enable_grade')"}],attrs:{label:"会员折扣设置",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_alone_grade",{initialValue:0,rules:[{required:!0}]}],expression:"['is_alone_grade', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate(!0)}}},[t("a-radio",{attrs:{value:0}},[e._v("默认等级折扣")]),t("a-radio",{attrs:{value:1}},[e._v("单独设置折扣")])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_alone_grade"),expression:"form.getFieldValue('is_alone_grade')"}]},e._l(e.formData.userGradeList,(function(a){return t("a-form-item",{key:a.grade_id},[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["alone_grade_equity[grade_id:".concat(a.grade_id,"]"),{initialValue:e.formData.defaultUserGradeValue[a.grade_id],rules:[{required:!0,message:"折扣率不能为空"}]}],expression:"[`alone_grade_equity[grade_id:${item.grade_id}]`, {\n                    initialValue: formData.defaultUserGradeValue[item.grade_id], rules: [{ required: true, message: '折扣率不能为空' }]\n                  }]"}],attrs:{addonBefore:a.name,addonAfter:"折",inputProps:{min:0,max:9.9}}})],1)})),1),t("div",{staticClass:"form-item-help"},[e.form.getFieldValue("is_alone_grade")?t("p",{staticClass:"extra"},[e._v("单独折扣：折扣率范围0.0-9.9，例如: 9.8代表98折，0代表不折扣")]):t("p",{staticClass:"extra"},[e._v("默认折扣：默认为用户所属会员等级的折扣率")])])],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("apps-dealer"),expression:"$module('apps-dealer')"}]},[t("a-divider",{attrs:{orientation:"left"}},[e._v("分销设置")]),t("a-form-item",{attrs:{label:"分销佣金",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_ind_dealer",{initialValue:0,rules:[{required:!0}]}],expression:"['is_ind_dealer', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate(!0)}}},[t("a-radio",{attrs:{value:0}},[e._v("系统默认")]),t("a-radio",{attrs:{value:1}},[e._v("单独设置")])],1),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("若使用分销功能必须在 [分销中心 - 分销设置] 中开启")])])],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_ind_dealer"),expression:"form.getFieldValue('is_ind_dealer')"}],attrs:{label:"分销佣金类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["dealer_money_type",{initialValue:10,rules:[{required:!0}]}],expression:"['dealer_money_type', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate(!0)}}},[t("a-radio",{attrs:{value:10}},[e._v("百分比")]),t("a-radio",{attrs:{value:20}},[e._v("固定金额")])],1),e._l(e.formData.dealer.levelList,(function(a,r){return t("a-form-item",{key:r},[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:[a.value,{rules:[{required:!0,message:"佣金不能为空"}]}],expression:"[item.value, { rules: [{ required: true, message: '佣金不能为空'}] }]"}],attrs:{addonBefore:a.name,addonAfter:10==e.form.getFieldValue("dealer_money_type")?"%":"元",inputProps:{min:0,precision:2}}})],1)}))],2)],1)],1)]),t("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1)],1)],1)},i=[],o=(a("d81d"),a("b64b"),a("d3b7"),a("d084")),s=a("2af9"),l=a("e1fe"),n=a("b78d"),d=a("ca00"),u={components:{GoodsType:n["a"],SelectImage:s["h"],SelectVideo:s["m"],Ueditor:s["n"],InputNumberGroup:s["c"],MultiSpec:n["b"]},data:function(){return{tabKey:0,labelCol:{span:3},wrapperCol:{span:10},isLoading:!1,isBtnLoading:!1,form:this.$form.createForm(this),goodsId:null,formData:l["a"].formData}},created:function(){this.initData()},beforeDestroy:function(){l["a"].formData.goods={}},methods:{initData:function(){var e=this;this.goodsId=this.$route.query.goodsId,this.isLoading=!0,l["a"].getFromData(this.goodsId).then((function(){Object(d["g"])(e.form.getFieldsValue())||(e.form.setFieldsValue(l["a"].getFieldsValue()),e.$nextTick((function(){e.form.setFieldsValue(l["a"].getFieldsValue2()),e.onForceUpdate()}))),e.isLoading=!1}))},onForceUpdate:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$forceUpdate(),t&&setTimeout((function(){e.$forceUpdate()}),10)},handleTabs:function(e){this.tabKey=e},onReloadCategoryList:function(){var e=this;this.isLoading=!0,l["a"].getCategoryList().then((function(){e.isLoading=!1}))},onReloadServiceList:function(){var e=this;this.isLoading=!0,l["a"].getServiceList().then((function(){e.isLoading=!1}))},onReloadDeliveryList:function(){var e=this;this.isLoading=!0,l["a"].getDeliveryList().then((function(){e.isLoading=!1}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){if(e)return t.onTargetTabError(e),!1;if(20===a.spec_type){var r=t.$refs.MultiSpec;if(!r.verifyForm())return t.tabKey=1,!1;a.specData=r.getFromSpecData()}return a.categoryIds=a.categorys.map((function(e){return e.value})),delete a.categorys,t.onFormSubmit(a),!0}))},onTargetTabError:function(e){var t=[["goods_type","goods_name","categorys","imagesIds","delivery_id","is_ind_delivery_type","delivery_type"],["spec_type","goods_price","is_restrict","restrict_total","restrict_single"],["content"],["alone_grade_equity","first_money","second_money","third_money"]],a=Object.keys(e).shift();for(var r in t)if(t[r].indexOf(a)>-1){this.tabKey=parseInt(r);break}},onFormSubmit:function(e){var t=this;this.isLoading=!0,this.isBtnLoading=!0,o["a"]({form:e}).then((function(e){t.$message.success(e.message,1.5),setTimeout((function(){t.$router.push("./index")}),1500)})).catch((function(){t.isBtnLoading=!1})).finally((function(){return t.isLoading=!1}))}}},c=u,m=(a("4cfe"),a("2877")),p=Object(m["a"])(c,r,i,!1,null,"25fe54e2",null);t["default"]=p.exports},5915:function(e,t,a){"use strict";a("07b4")},"59aa":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return s}));var r=a("5c06"),i=new r["a"]([{key:"PHYSICAL",name:"实物商品",value:10},{key:"VIRTUAL",name:"虚拟商品",value:20}]),o=(new r["a"]([{key:"SINGLE",name:"单规格",value:10},{key:"MULTI",name:"多规格",value:20}]),new r["a"]([{key:"NORMAL",name:"进行中",value:10},{key:"COMPLETED",name:"导入完成",value:20}])),s=new r["a"]([{key:"NORMAL",name:"进行中",value:10},{key:"COMPLETED",name:"采集完成",value:20}])},"5c00":function(e,t,a){},"63a1":function(e,t,a){},6760:function(e,t,a){"use strict";a("5c00")},"6a54":function(e,t,a){"use strict";a.d(t,"d",(function(){return o})),a.d(t,"a",(function(){return s})),a.d(t,"c",(function(){return l})),a.d(t,"b",(function(){return n}));var r=a("b775"),i={list:"/xj.category/list",add:"/xj.category/add",edit:"/xj.category/edit",delete:"/xj.category/delete"};function o(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function s(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function l(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function n(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},"7c8f":function(e,t,a){},"861f1":function(e,t,a){"use strict";a("8a9f")},"88bc":function(e,t,a){(function(t){var a=1/0,r=9007199254740991,i="[object Arguments]",o="[object Function]",s="[object GeneratorFunction]",l="[object Symbol]",n="object"==typeof t&&t&&t.Object===Object&&t,d="object"==typeof self&&self&&self.Object===Object&&self,u=n||d||Function("return this")();function c(e,t,a){switch(a.length){case 0:return e.call(t);case 1:return e.call(t,a[0]);case 2:return e.call(t,a[0],a[1]);case 3:return e.call(t,a[0],a[1],a[2])}return e.apply(t,a)}function m(e,t){var a=-1,r=e?e.length:0,i=Array(r);while(++a<r)i[a]=t(e[a],a,e);return i}function p(e,t){var a=-1,r=t.length,i=e.length;while(++a<r)e[i+a]=t[a];return e}var v=Object.prototype,f=v.hasOwnProperty,g=v.toString,h=u.Symbol,b=v.propertyIsEnumerable,_=h?h.isConcatSpreadable:void 0,C=Math.max;function y(e,t,a,r,i){var o=-1,s=e.length;a||(a=L),i||(i=[]);while(++o<s){var l=e[o];t>0&&a(l)?t>1?y(l,t-1,a,r,i):p(i,l):r||(i[i.length]=l)}return i}function w(e,t){return e=Object(e),x(e,t,(function(t,a){return a in e}))}function x(e,t,a){var r=-1,i=t.length,o={};while(++r<i){var s=t[r],l=e[s];a(l,s)&&(o[s]=l)}return o}function S(e,t){return t=C(void 0===t?e.length-1:t,0),function(){var a=arguments,r=-1,i=C(a.length-t,0),o=Array(i);while(++r<i)o[r]=a[t+r];r=-1;var s=Array(t+1);while(++r<t)s[r]=a[r];return s[t]=o,c(e,this,s)}}function L(e){return q(e)||V(e)||!!(_&&e&&e[_])}function k(e){if("string"==typeof e||O(e))return e;var t=e+"";return"0"==t&&1/e==-a?"-0":t}function V(e){return D(e)&&f.call(e,"callee")&&(!b.call(e,"callee")||g.call(e)==i)}var q=Array.isArray;function F(e){return null!=e&&I(e.length)&&!N(e)}function D(e){return j(e)&&F(e)}function N(e){var t=$(e)?g.call(e):"";return t==o||t==s}function I(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=r}function $(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function j(e){return!!e&&"object"==typeof e}function O(e){return"symbol"==typeof e||j(e)&&g.call(e)==l}var R=S((function(e,t){return null==e?{}:w(e,m(y(t,1),k))}));e.exports=R}).call(this,a("c8ba"))},"8a9f":function(e,t,a){},"967a":function(e,t,a){"use strict";a.d(t,"f",(function(){return o})),a.d(t,"b",(function(){return s})),a.d(t,"d",(function(){return l})),a.d(t,"a",(function(){return n})),a.d(t,"e",(function(){return d})),a.d(t,"c",(function(){return u}));var r=a("b775"),i={list:"/setting.delivery/list",all:"/setting.delivery/all",detail:"/setting.delivery/detail",add:"/setting.delivery/add",edit:"/setting.delivery/edit",delete:"/setting.delivery/delete"};function o(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function s(e){return Object(r["b"])({url:i.all,method:"get",params:e})}function l(e){return Object(r["b"])({url:i.detail,method:"get",params:e})}function n(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function d(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function u(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},a53d:function(e,t,a){},acbe:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",{staticClass:"row-item-search"},[t("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[t("a-form-item",{attrs:{label:"商品名称"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goodsName"],expression:"['goodsName']"}],attrs:{placeholder:"请输入商品名称/编码"}})],1),t("a-form-item",{attrs:{label:"订单号"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["orderNo"],expression:"['orderNo']"}],attrs:{placeholder:"请输入订单号"}})],1),t("a-form-item",{attrs:{label:"会员ID"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["userId"],expression:"['userId']"}],attrs:{placeholder:"请输入会员ID"}})],1),t("a-form-item",{attrs:{label:"状态"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:-1}],expression:"['status', { initialValue: -1 }]"}]},[t("a-select-option",{attrs:{value:-1}},[e._v("全部")]),t("a-select-option",{attrs:{value:1}},[e._v("显示")]),t("a-select-option",{attrs:{value:0}},[e._v("隐藏")])],1)],1),t("a-form-item",{staticClass:"search-btn"},[t("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1),t("div",{staticClass:"row-item-tab clearfix"},[t("div",{staticClass:"tab-list fl-l"},[t("a-radio-group",{attrs:{defaultValue:e.queryParam.score},on:{change:e.handleTabs}},[t("a-radio-button",{attrs:{value:0}},[e._v("全部")]),t("a-radio-button",{attrs:{value:10}},[e._v("好评")]),t("a-radio-button",{attrs:{value:20}},[e._v("中评")]),t("a-radio-button",{attrs:{value:30}},[e._v("差评")])],1)],1)])],1),t("s-table",{ref:"table",attrs:{rowKey:"comment_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15,scroll:{x:1450}},scopedSlots:e._u([{key:"orderGoods",fn:function(e){return t("span",{},[t("GoodsItem",{attrs:{data:{image:e.goods_image,imageAlt:"商品图片",title:e.goods_name,goodsProps:e.goods_props}}})],1)}},{key:"user",fn:function(e){return t("span",{},[t("UserItem",{attrs:{user:e}})],1)}},{key:"score",fn:function(a){return t("span",{},[t("a-tag",{attrs:{color:{10:"green",20:"",30:"red"}[a]}},[e._v(e._s({10:"好评",20:"中评",30:"差评"}[a]))])],1)}},{key:"content",fn:function(a){return t("span",{},[t("p",{staticClass:"twoline-hide",staticStyle:{width:"260px"}},[e._v(e._s(a))])])}},{key:"is_picture",fn:function(a){return t("span",{},[t("a-tag",{attrs:{color:a?"green":""}},[e._v(e._s(a?"是":"否"))])],1)}},{key:"status",fn:function(a){return t("span",{},[t("a-tag",{attrs:{color:a?"green":""}},[e._v(e._s(a?"显示":"隐藏"))])],1)}},{key:"action",fn:function(a,r){return t("span",{},[t("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),t("EditForm",{ref:"EditForm",on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=a("5530"),s=(a("d3b7"),a("b775")),l={list:"/goods.comment/list",detail:"/goods.comment/detail",edit:"/goods.comment/edit",delete:"/goods.comment/delete"};function n(e){return Object(s["b"])({url:l.list,method:"get",params:e})}function d(e){return Object(s["b"])({url:l.detail,method:"get",params:e})}function u(e){return Object(s["b"])({url:l.edit,method:"post",data:e})}function c(e){return Object(s["b"])({url:l.delete,method:"post",data:e})}var m=a("ab09"),p=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"评分",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["score",{initialValue:10,rules:[{required:!0}]}],expression:"['score', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("好评")]),t("a-radio",{attrs:{value:20}},[e._v("中评")]),t("a-radio",{attrs:{value:30}},[e._v("差评")])],1)],1),t("a-form-item",{attrs:{label:"评价内容",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["content"],expression:"['content']"}],attrs:{placeholder:"请输入评价内容 (300个字符以内)",autoSize:{minRows:4}}})],1),t("a-form-item",{attrs:{label:"评价图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"最多允许6张，可拖拽调整显示顺序"}},[t("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["imageIds"],expression:"['imageIds']"}],attrs:{multiple:"",maxNum:6,defaultList:e.record.imageList}})],1),t("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("显示")]),t("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},v=[],f=a("88bc"),g=a.n(f),h=a("2af9"),b={components:{SelectImage:h["h"]},data:function(){return{title:"编辑评价",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),commentId:null,record:{}}},methods:{edit:function(e){this.visible=!0,this.commentId=e,this.getRecordData()},getRecordData:function(){var e=this;this.confirmLoading=!0,d({commentId:this.commentId}).then((function(t){e.record=t.data.detail,e.setFieldsValue()})).finally((function(t){e.confirmLoading=!1}))},setFieldsValue:function(){var e=this,t=this.form.setFieldsValue;this.$nextTick((function(){t(g()(e.record,["score","content","status","sort","imageIds"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.record={},this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,u({commentId:this.commentId,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(e){t.confirmLoading=!1}))}}},_=b,C=a("2877"),y=Object(C["a"])(_,p,v,!1,null,null,null),w=y.exports,x=[{title:"ID",dataIndex:"comment_id"},{title:"商品信息",dataIndex:"orderGoods",width:"320px",scopedSlots:{customRender:"orderGoods"}},{title:"买家",dataIndex:"user",width:"180px",scopedSlots:{customRender:"user"}},{title:"评分",dataIndex:"score",scopedSlots:{customRender:"score"}},{title:"评价内容",dataIndex:"content",width:"280px",scopedSlots:{customRender:"content"}},{title:"图片评价",dataIndex:"is_picture",scopedSlots:{customRender:"is_picture"}},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"评价时间",width:"180px",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"150px",fixed:"right",scopedSlots:{customRender:"action"}}],S={name:"Index",components:{STable:m["b"],GoodsItem:m["a"],UserItem:m["c"],EditForm:w},data:function(){var e=this;return{searchForm:this.$form.createForm(this),categoryListTree:[],queryParam:{score:0},isLoading:!1,columns:x,loadData:function(t){return n(Object(o["a"])(Object(o["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleTabs:function(e){this.queryParam.score=e.target.value,this.handleRefresh(!0)},handleSearch:function(e){var t=this;e.preventDefault(),this.searchForm.validateFields((function(e,a){e||(t.queryParam=Object(o["a"])(Object(o["a"])({},t.queryParam),a),t.handleRefresh(!0))}))},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return c({commentId:e.comment_id}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleEdit:function(e){this.$refs.EditForm.edit(e.comment_id)},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)}}},L=S,k=(a("2742"),Object(C["a"])(L,r,i,!1,null,"669c44a3",null));t["default"]=k.exports},b78d:function(e,t,a){"use strict";a.d(t,"a",(function(){return c})),a.d(t,"b",(function(){return F}));var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"goods-type"},e._l(e.list,(function(a,r){return t("div",{directives:[{name:"show",rawName:"v-show",value:!e.onlyShowChecked||e.value==a.value,expression:"!onlyShowChecked || value == item.value"}],key:r,staticClass:"type-item",class:{checked:e.value==a.value},on:{click:function(t){return e.handleItem(a.value)}}},[t("div",{staticClass:"type-title"},[e._v(e._s(a.title))]),t("div",{staticClass:"type-help"},[e._v(e._s(a.help))]),e.value==a.value?t("a-icon",{staticClass:"icon-checked",attrs:{type:"check"}}):e._e()],1)})),0)},i=[],o=(a("b0c0"),a("4d91")),s=a("59aa"),l={name:"GoodsType",model:{prop:"value",event:"change"},props:{value:o["a"].any,onlyShowChecked:o["a"].bool.def(!1)},data:function(){return{defaultValue:s["b"].PHYSICAL.value,list:[{title:s["b"].PHYSICAL.name,help:"物流发货",value:s["b"].PHYSICAL.value},{title:s["b"].VIRTUAL.name,help:"无需物流",value:s["b"].VIRTUAL.value}]}},methods:{handleItem:function(e){this.$emit("change",e)}}},n=l,d=(a("1457"),a("2877")),u=Object(d["a"])(n,r,i,!1,null,"7fa6c589",null),c=u.exports,m=function(){var e=this,t=e._self._c;return t("div",[t("a-form-item",{attrs:{label:"商品规格",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("div",{staticClass:"form-item-help",staticStyle:{"line-height":"36px"}},[t("small",[e._v("最多添加3个商品规格组，生成的SKU数量不能超出50个")])]),e._l(e.multiSpecData.specList,(function(a,r){return t("div",{key:r,staticClass:"spec-group"},[t("div",{staticClass:"spec-group-item clearfix"},[t("a-input",{staticClass:"group-item-input",attrs:{readOnly:e.isSpecLocked,placeholder:"请输入规格名称"},on:{change:e.onChangeSpecGroupIpt},model:{value:a.spec_name,callback:function(t){e.$set(a,"spec_name",t)},expression:"item.spec_name"}}),e.isSpecLocked?e._e():t("a",{staticClass:"group-item-delete",attrs:{href:"javascript:;"},on:{click:function(t){return e.handleDeleteSpecGroup(r)}}},[e._v("删除规格组")])],1),t("div",{staticClass:"spec-value clearfix"},[e._l(a.valueList,(function(a,i){return t("div",{key:i,staticClass:"spec-value-item"},[t("a-input",{staticClass:"value-item-input",attrs:{readOnly:e.isSpecLocked,placeholder:"请输入规格值"},on:{change:e.onChangeSpecValueIpt},model:{value:a.spec_value,callback:function(t){e.$set(a,"spec_value",t)},expression:"itm.spec_value"}}),e.isSpecLocked?e._e():t("a-icon",{staticClass:"icon-close",attrs:{theme:"filled",type:"close-circle"},on:{click:function(t){return e.handleDeleteSpecValue(r,i)}}})],1)})),e.isSpecLocked?e._e():t("div",{staticClass:"spec-value-add"},[t("a",{staticClass:"group-item-delete",attrs:{href:"javascript:;"},on:{click:function(t){return e.handleAddSpecValue(r)}}},[e._v("新增规格值")])])],2)])})),!e.isSpecLocked&&e.multiSpecData.specList.length<3?t("a-button",{staticClass:"spec-group-add-btn",attrs:{icon:"plus"},on:{click:e.handleAddSpecGroup}},[e._v("添加规格组")]):e._e()],2),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.multiSpecData.skuList.length,expression:"multiSpecData.skuList.length"}],attrs:{label:"SKU列表",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[e.multiSpecData.skuList.length>1?t("div",{staticClass:"sku-batch"},[t("span",{staticClass:"title"},[e._v("批量设置:")]),t("a-input-number",{attrs:{placeholder:"商品价格",min:0,precision:2},model:{value:e.multiSpecData.skuBatchForm.goods_price,callback:function(t){e.$set(e.multiSpecData.skuBatchForm,"goods_price",t)},expression:"multiSpecData.skuBatchForm.goods_price"}}),t("a-input-number",{attrs:{placeholder:"划线价格",min:0,precision:2},model:{value:e.multiSpecData.skuBatchForm.line_price,callback:function(t){e.$set(e.multiSpecData.skuBatchForm,"line_price",t)},expression:"multiSpecData.skuBatchForm.line_price"}}),t("a-input-number",{attrs:{placeholder:"库存数量",min:0,precision:0},model:{value:e.multiSpecData.skuBatchForm.stock_num,callback:function(t){e.$set(e.multiSpecData.skuBatchForm,"stock_num",t)},expression:"multiSpecData.skuBatchForm.stock_num"}}),t("a-input-number",{attrs:{placeholder:"商品重量",min:0,precision:2},model:{value:e.multiSpecData.skuBatchForm.goods_weight,callback:function(t){e.$set(e.multiSpecData.skuBatchForm,"goods_weight",t)},expression:"multiSpecData.skuBatchForm.goods_weight"}}),t("a-input",{attrs:{placeholder:"sku编码"},model:{value:e.multiSpecData.skuBatchForm.goods_sku_no,callback:function(t){e.$set(e.multiSpecData.skuBatchForm,"goods_sku_no",t)},expression:"multiSpecData.skuBatchForm.goods_sku_no"}}),t("a-button",{on:{click:e.handleSkuBatch}},[e._v("立即设置")])],1):e._e(),t("a-table",{staticClass:"sku-list",attrs:{columns:e.multiSpecData.skuColumns,dataSource:e.multiSpecData.skuList,scroll:{x:!0},pagination:!1,bordered:""},scopedSlots:e._u([{key:"image",fn:function(a,r){return[t("SelectImage",{attrs:{defaultList:r.image_id>0&&r.image?[r.image]:[],width:50},model:{value:r.image_id,callback:function(t){e.$set(r,"image_id",t)},expression:"item.image_id"}})]}},{key:"goods_price",fn:function(a,r){return[t("a-input-number",{attrs:{size:"small",min:.01,precision:2},model:{value:r.goods_price,callback:function(t){e.$set(r,"goods_price",t)},expression:"item.goods_price"}})]}},{key:"line_price",fn:function(a,r){return[t("a-input-number",{attrs:{size:"small",min:0,precision:2},model:{value:r.line_price,callback:function(t){e.$set(r,"line_price",t)},expression:"item.line_price"}})]}},{key:"stock_num",fn:function(a,r){return[t("a-input-number",{attrs:{size:"small",min:0,precision:0},model:{value:r.stock_num,callback:function(t){e.$set(r,"stock_num",t)},expression:"item.stock_num"}})]}},{key:"goods_weight",fn:function(a,r){return[t("a-input-number",{attrs:{size:"small",min:0,precision:2},model:{value:r.goods_weight,callback:function(t){e.$set(r,"goods_weight",t)},expression:"item.goods_weight"}})]}},{key:"goods_sku_no",fn:function(a,r){return[t("a-input",{attrs:{size:"small"},model:{value:r.goods_sku_no,callback:function(t){e.$set(r,"goods_sku_no",t)},expression:"item.goods_sku_no"}})]}}])})],1)],1)},p=[],v=a("5530"),f=a("d4ec"),g=a("bee2"),h=(a("d3b7"),a("159b"),a("a15b"),a("d81d"),a("99af"),a("7db0"),a("b64b"),a("a434"),a("2ef0")),b=a.n(h),_=a("ca00"),C=[{title:"预览图",dataIndex:"image",width:90,scopedSlots:{customRender:"image"}},{title:"商品价格",dataIndex:"goods_price",width:120,scopedSlots:{customRender:"goods_price"}},{title:"划线价格",dataIndex:"line_price",width:120,scopedSlots:{customRender:"line_price"}},{title:"库存数量",dataIndex:"stock_num",width:120,scopedSlots:{customRender:"stock_num"}},{title:"商品重量 (KG)",dataIndex:"goods_weight",width:120,scopedSlots:{customRender:"goods_weight"}},{title:"SKU编码",dataIndex:"goods_sku_no",width:140,scopedSlots:{customRender:"goods_sku_no"}}],y={image_id:0,image:{},goods_price:"",line_price:"",stock_num:"",goods_weight:"",goods_sku_no:""},w=function(){function e(){Object(f["a"])(this,e),this.multiSpecData={},this.error="",this.multiSpecData={specList:[],skuList:[],skuColumns:b.a.cloneDeep(C),skuBatchForm:b.a.cloneDeep(y)}}return Object(g["a"])(e,[{key:"getData",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.length&&(this.multiSpecData.specList=b.a.cloneDeep(e),this.multiSpecData.skuList=b.a.cloneDeep(t));var a=this.specGroupArr(),r=S(a),i=this.rowSpanArr(a,r);return this.buildSkuColumns(i),this.buildSkuList(r),this.multiSpecData}},{key:"isEmpty",value:function(){return 0===this.multiSpecData.specList.length}},{key:"getError",value:function(){return this.error}},{key:"specGroupArr",value:function(){var e=[];return this.multiSpecData.specList.forEach((function(t){var a=[];t.valueList.forEach((function(e){a.push(e)})),e.push(a)})),e}},{key:"rowSpanArr",value:function(e,t){for(var a=[],r=t.length,i=0;i<e.length;i++)a[i]=parseInt(r/e[i].length),r=a[i];return a}},{key:"buildSkuList",value:function(e){for(var t=[],a=function(){var a=Object(v["a"])(Object(v["a"])({},y),{},{key:r,tempId:e[r].map((function(e){return e.spec_value})).join("_"),skuKeys:e[r].map((function(e){return{groupKey:e.groupKey,valueKey:e.key}}))});e[r].forEach((function(e,t){a["spec_value_".concat(t)]=e.spec_value})),t.push(a)},r=0;r<e.length;r++)a();this.multiSpecData.skuList=this.oldSkuList(t)}},{key:"oldSkuList",value:function(e){var t=this.multiSpecData.skuList.concat();if(!t.length||!e.length)return e;var a=function(a){var r={};r=t.length===e.length?b.a.cloneDeep(t[a]):t.find((function(t){return t.tempId===e[a].tempId})),r&&(e[a]=Object(v["a"])(Object(v["a"])({},e[a]),b.a.pick(r,Object.keys(y))))};for(var r in e)a(r);return e}},{key:"buildSkuColumns",value:function(e){for(var t=this.multiSpecData.specList,a=C.concat(),r=function(t,a,r,i){var o={children:a,attrs:{}},s=e[t-1];return o.attrs.rowSpan=i%s===0?s:0,o},i=function(e){var i=t[e-1];a.unshift({title:i.spec_name,dataIndex:"spec_value_".concat(e-1),customRender:function(t,a,i){return r(e,t,a,i)}})},o=t.length;o>0;o--)i(o);this.multiSpecData.skuColumns=a}},{key:"handleAddSpecGroup",value:function(){var e=this.multiSpecData.specList;e.push({key:e.length||0,spec_name:"",valueList:[]});var t=e.length-1;this.handleAddSpecValue(t)}},{key:"handleAddSpecValue",value:function(e){var t=this.multiSpecData.specList[e],a=t.valueList;a.push({key:a.length||0,groupKey:t.key,spec_value:""}),this.onRefreshSpecValueKey(e)}},{key:"handleDeleteSpecGroup",value:function(e){this.multiSpecData.specList.splice(e,1),this.onUpdate(!1)}},{key:"handleDeleteSpecValue",value:function(e,t){this.multiSpecData.specList[e].valueList.splice(t,1),this.onRefreshSpecValueKey(e),this.onUpdate(!1)}},{key:"onRefreshSpecValueKey",value:function(e){var t=this.multiSpecData.specList[e],a=t.valueList;a.forEach((function(e,t){a[t].key=t}))}},{key:"handleSkuBatch",value:function(){var e=this.getFilterObject(this.multiSpecData.skuBatchForm),t=this.multiSpecData.skuList;for(var a in t)t[a]=Object(v["a"])(Object(v["a"])({},t[a]),e);this.onUpdate(!1)}},{key:"getFilterObject",value:function(e){var t={};for(var a in e){var r=e[a];Object(_["f"])(r)||(t[a]=r)}return t}},{key:"verifyForm",value:function(){return!!this.verifySpec()&&!!this.verifySkuList()}},{key:"verifySkuList",value:function(){var e=[{field:"goods_price",name:"商品价格"},{field:"stock_num",name:"库存数量"},{field:"goods_weight",name:"商品重量"}],t=this.multiSpecData.skuList;for(var a in t){var r=t[a];for(var i in e){var o=r[e[i].field];if(""===o||null===o)return this.error="".concat(e[i].name,"不能为空"),!1}}return!0}},{key:"verifySpec",value:function(){var e=this.multiSpecData.specList;if(!e.length)return this.error="亲，还没有添加规格组~",!1;for(var t in e){var a=e[t];if(Object(_["f"])(a.spec_name))return this.error="规格组名称不能为空~",!1;var r=a.valueList;if(!r.length)return this.error="还没有添加规格值~",!1;for(var i in r)if(Object(_["f"])(r[i].spec_value))return this.error="规格值不能为空~",!1}return!0}},{key:"getFromSpecData",value:function(){var e=this.multiSpecData,t=e.specList,a=e.skuList,r={specList:b.a.cloneDeep(t),skuList:b.a.cloneDeep(a)};for(var i in r.skuList){var o=r.skuList[i];delete o.image,delete o.key}return r}},{key:"onUpdate",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e?Object(_["c"])(x,200)(this):x(this)}}]),e}(),x=function(e){return e.getData()},S=function(e){return e.length?Array.prototype.reduce.call(e,(function(e,t){var a=[];return e.forEach((function(e){t.forEach((function(t){a.push(e.concat([t]))}))})),a}),[[]]):[]},L=a("2af9"),k={components:{SelectImage:L["h"]},props:{defaultSpecList:o["a"].array.def([]),defaultSkuList:o["a"].array.def([]),isSpecLocked:o["a"].bool.def(!1)},data:function(){return{labelCol:{span:3},wrapperCol:{span:21},MultiSpecModel:new w,multiSpecData:{specList:[],skuList:[]}}},watch:{defaultSpecList:function(e){e.length&&this.MultiSpecModel.isEmpty()&&this.getData()}},created:function(){this.getData()},methods:{getData:function(){var e=this.defaultSpecList,t=this.defaultSkuList;this.multiSpecData=this.MultiSpecModel.getData(e,t)},getFromSpecData:function(){return this.MultiSpecModel.getFromSpecData()},handleAddSpecGroup:function(){this.checkSkuMaxNum()&&this.MultiSpecModel.handleAddSpecGroup()},handleDeleteSpecGroup:function(e){var t=this,a=this.$confirm({title:"您确定要删除该规格组吗?",content:"删除后不可恢复",onOk:function(){t.MultiSpecModel.handleDeleteSpecGroup(e),a.destroy()}})},handleAddSpecValue:function(e){this.checkSkuMaxNum()&&this.MultiSpecModel.handleAddSpecValue(e)},handleDeleteSpecValue:function(e,t){var a=this,r=this.$confirm({title:"您确定要删除该规格值吗?",content:"删除后不可恢复",onOk:function(){a.MultiSpecModel.handleDeleteSpecValue(e,t),r.destroy()}})},onChangeSpecGroupIpt:function(){this.MultiSpecModel.onUpdate(!0)},onChangeSpecValueIpt:function(e,t){this.MultiSpecModel.onUpdate(!0)},checkSkuMaxNum:function(){var e=this.multiSpecData.skuList;return!(e.length>=50)||(this.$message.error("生成的sku列表数量不能大于50个，当前数量：".concat(e.length,"个"),2.5),!1)},handleSkuBatch:function(){this.MultiSpecModel.handleSkuBatch()},verifyForm:function(){return!!this.MultiSpecModel.verifyForm()||(this.$message.error(this.MultiSpecModel.getError(),2),!1)}}},V=k,q=(a("6760"),Object(d["a"])(V,m,p,!1,null,"7fcd29e6",null)),F=q.exports},b84e:function(e,t,a){"use strict";a("63a1")},bfce:function(e,t,a){},c1df6:function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form,selfUpdate:!0},on:{submit:e.handleSubmit}},[t("a-tabs",{attrs:{activeKey:e.tabKey,tabBarStyle:{marginBottom:"30px"}},on:{change:e.handleTabs}},[t("a-tab-pane",{key:0,attrs:{tab:"基本信息"}}),t("a-tab-pane",{key:1,attrs:{tab:"规格/库存"}}),t("a-tab-pane",{key:2,attrs:{tab:"商品详情"}}),t("a-tab-pane",{key:3,attrs:{tab:"更多设置"}})],1),t("div",{staticClass:"tabs-content"},[t("div",{directives:[{name:"show",rawName:"v-show",value:0==e.tabKey,expression:"tabKey == 0"}],staticClass:"tab-pane"},[t("a-form-item",{attrs:{label:"商品类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("GoodsType",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_type",{rules:[{required:!0}]}],expression:"['goods_type', { rules: [{ required: true }] }]"}],attrs:{onlyShowChecked:!0},on:{change:function(t){return e.onForceUpdate(!0)}}})],1),t("a-form-item",{attrs:{label:"商品名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['goods_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入商品名称"}})],1),t("a-form-item",{attrs:{label:"商品分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["categorys",{rules:[{required:!0,message:"请至少选择1个商品分类"}]}],expression:"['categorys', { rules: [{ required: true, message: '请至少选择1个商品分类' }] }]"}],attrs:{placeholder:"请选择商品分类",dropdownStyle:{maxHeight:"500px",overflow:"auto"},treeData:e.formData.categoryList,treeCheckable:"",treeCheckStrictly:"",allowClear:""}}),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{target:"_blank",to:{path:"/goods/category/index"}}},[e._v("去新增")]),t("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadCategoryList}},[e._v("刷新")])],1)],1),t("a-form-item",{attrs:{label:"商品图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸：750*750像素, 最多上传10张, 可拖拽图片调整顺序, 第1张将作为商品首图"}},[t("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["imagesIds",{rules:[{required:!0,message:"请至少上传1张商品图片"}]}],expression:"['imagesIds', { rules: [{ required: true, message: '请至少上传1张商品图片' }] }]"}],attrs:{multiple:"",maxNum:10,defaultList:e.formData.goods.goods_images}})],1),t("a-form-item",{attrs:{label:"商品编码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_no"],expression:"['goods_no']"}],attrs:{placeholder:"请输入商品编码"}})],1),10==e.form.getFieldValue("goods_type")?t("a-form-item",{attrs:{label:"配送方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_ind_delivery_type",{initialValue:0,rules:[{required:!0}]}],expression:"['is_ind_delivery_type', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate()}}},[t("a-radio",{attrs:{value:0}},[e._v("系统默认")]),t("a-radio",{attrs:{value:1}},[e._v("单独配置")])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_ind_delivery_type"),expression:"form.getFieldValue('is_ind_delivery_type')"}]},[t("a-form-item",{attrs:{extra:"需在 [设置 - 配送方式] 中，开启支持的配送方式才可生效"}},[t("a-checkbox-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["delivery_type",{initialValue:[10,20],rules:[{required:!0,message:"配送方式至少选择一个"}]}],expression:"['delivery_type', { initialValue: [10, 20], rules: [{ required: true, message: '配送方式至少选择一个' }] }]"}],attrs:{options:[{label:"快递配送",value:10},{label:"上门自提",value:20}]}})],1)],1)],1):e._e(),10==e.form.getFieldValue("goods_type")?t("a-form-item",{attrs:{label:"运费模板",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["delivery_id",{rules:[{required:!0,message:"请选择运费模板"}]}],expression:"['delivery_id', { rules: [{ required: true, message: '请选择运费模板' }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请选择运费模板"}},e._l(e.formData.deliveryList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.delivery_id}},[e._v(e._s(a.name))])})),1),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{target:"_blank",to:{path:"/setting/delivery/template/create"}}},[e._v("新增模板")]),t("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadDeliveryList}},[e._v("刷新")])],1)],1):e._e(),t("a-form-item",{attrs:{label:"商品状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:10,rules:[{required:!0}]}],expression:"['status', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("上架")]),t("a-radio",{attrs:{value:20}},[e._v("下架")])],1)],1),t("a-form-item",{attrs:{label:"商品排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true }] }]"}],attrs:{min:0}})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:1==e.tabKey,expression:"tabKey == 1"}],staticClass:"tab-pane"},[t("a-form-item",{attrs:{label:"规格类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["spec_type",{initialValue:10,rules:[{required:!0}]}],expression:"['spec_type', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate()}}},[t("a-radio",{attrs:{value:10,disabled:e.formData.goods.isSpecLocked}},[e._v("单规格")]),t("a-radio",{attrs:{value:20,disabled:e.formData.goods.isSpecLocked}},[e._v("多规格")])],1),e.formData.goods.isSpecLocked?t("p",{staticClass:"form-item-help"},[t("small",{staticClass:"c-red"},[e._v("注：该商品当前正在参与其他活动，商品规格不允许更改")])]):e._e()],1),20==e.form.getFieldValue("spec_type")?t("div",[t("MultiSpec",{ref:"MultiSpec",attrs:{isSpecLocked:e.formData.goods.isSpecLocked,defaultSpecList:e.formData.goods.specList,defaultSkuList:e.formData.goods.skuList}})],1):e._e(),10==e.form.getFieldValue("spec_type")?t("div",[t("a-form-item",{attrs:{label:"商品价格",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际购买金额，最低0.00"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_price",{rules:[{required:!0,message:"请输入商品价格"}]}],expression:"['goods_price', { rules: [{ required: true, message: '请输入商品价格' }] }]"}],attrs:{min:0,precision:2}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"划线价",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"划线价仅用于商品页展示"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["line_price"],expression:"['line_price']"}],attrs:{min:0,precision:2}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"当前库存数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际库存数量，为0时用户无法下单"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["stock_num",{initialValue:100,rules:[{required:!0,message:"请输入库存数量"}]}],expression:"['stock_num', { initialValue: 100, rules: [{ required: true, message: '请输入库存数量' }] }]"}],attrs:{min:0,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("件")])],1),t("a-form-item",{attrs:{label:"商品重量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际重量，用于计算运费"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_weight",{initialValue:0,rules:[{required:!0,message:"请输入库存数量"}]}],expression:"['goods_weight', { initialValue: 0, rules: [{ required: true, message: '请输入库存数量' }] }]"}],attrs:{min:0}}),t("span",{staticClass:"ml-10"},[e._v("千克 (Kg)")])],1)],1):e._e(),t("a-form-item",{attrs:{label:"库存计算方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["deduct_stock_type",{initialValue:10,rules:[{required:!0}]}],expression:"['deduct_stock_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("下单减库存")]),t("a-radio",{attrs:{value:20}},[e._v("付款减库存")])],1)],1),t("a-form-item",{attrs:{label:"商品限购",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用于限制每人购买该商品的数量"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_restrict",{initialValue:0,rules:[{required:!0}]}],expression:"['is_restrict', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate()}}},[t("a-radio",{attrs:{value:0}},[e._v("关闭")]),t("a-radio",{attrs:{value:1}},[e._v("开启")])],1),e.form.getFieldValue("is_restrict")?t("div",{staticClass:"mt-10"},[t("a-form-item",[t("span",{staticClass:"mr-10"},[e._v("总限购")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_total",{rules:[{required:!0,message:"请输入总限购数量"}]}],expression:"['restrict_total', { rules: [{ required: true, message: '请输入总限购数量' }] }]"}],attrs:{min:1,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("件/人")])],1),t("a-form-item",[t("span",{staticClass:"mr-10"},[e._v("每单限购")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_single",{rules:[{required:!0,message:"请输入每单限购数量"}]}],expression:"['restrict_single', { rules: [{ required: true, message: '请输入每单限购数量' }] }]"}],attrs:{min:1,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("件/人")])],1)],1):e._e()],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:2==e.tabKey,expression:"tabKey == 2"}],staticClass:"tab-pane"},[t("a-form-item",{attrs:{label:"商品详情",labelCol:e.labelCol,wrapperCol:{span:16}}},[t("Ueditor",{directives:[{name:"decorator",rawName:"v-decorator",value:["content",{rules:[{required:!0,message:"商品详情不能为空"}]}],expression:"['content', { rules: [{ required: true, message: '商品详情不能为空' }] }]"}]})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:3==e.tabKey,expression:"tabKey == 3"}],staticClass:"tab-pane"},[t("a-form-item",{attrs:{label:"主图视频",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议视频宽高比19:9，建议时长8-45秒"}},[t("SelectVideo",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_id"],expression:"['video_id']"}],attrs:{multiple:!1,defaultList:e.formData.goods.video?[e.formData.goods.video]:[]}})],1),t("a-form-item",{attrs:{label:"视频封面",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸：750像素*750像素"}},[t("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_cover_id"],expression:"['video_cover_id']"}],attrs:{multiple:!1,defaultList:e.formData.goods.videoCover?[e.formData.goods.videoCover]:[]}})],1),t("a-form-item",{attrs:{label:"商品卖点",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"一句话简述，例如：此款商品美观大方 性价比较高 不容错过"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["selling_point"],expression:"['selling_point']"}],attrs:{placeholder:"请输入商品卖点"}})],1),t("a-form-item",{attrs:{label:"服务与承诺",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[e.formData.serviceList?t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["serviceIds"],expression:"['serviceIds']"}],attrs:{mode:"multiple",placeholder:"请选择服务与承诺"}},e._l(e.formData.serviceList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.service_id}},[e._v(e._s(a.name))])})),1):e._e(),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{target:"_blank",to:{path:"/goods/service/index"}}},[e._v("去新增")]),t("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadServiceList}},[e._v("刷新")])],1)],1),t("a-form-item",{attrs:{label:"初始销量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端展示的销量 = 初始销量 + 实际销量"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sales_initial",{initialValue:0}],expression:"['sales_initial', { initialValue: 0}]"}]})],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("market-points"),expression:"$module('market-points')"}]},[t("a-divider",{attrs:{orientation:"left"}},[e._v("积分设置")]),t("a-form-item",{attrs:{label:"积分赠送",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后用户购买此商品将获得积分"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_points_gift",{initialValue:1,rules:[{required:!0}]}],expression:"['is_points_gift', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),t("a-form-item",{attrs:{label:"积分抵扣",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后用户购买此商品可以使用积分进行抵扣"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_points_discount",{initialValue:1,rules:[{required:!0}]}],expression:"['is_points_discount', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("user-grade"),expression:"$module('user-grade')"}]},[t("a-divider",{attrs:{orientation:"left"}},[e._v("会员折扣设置")]),t("a-form-item",{attrs:{label:"会员折扣",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后会员折扣，会员购买此商品可以享受会员等级折扣价"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_enable_grade",{initialValue:1,rules:[{required:!0}]}],expression:"['is_enable_grade', { initialValue: 1, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate(!0)}}},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_enable_grade"),expression:"form.getFieldValue('is_enable_grade')"}],attrs:{label:"会员折扣设置",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_alone_grade",{initialValue:0,rules:[{required:!0}]}],expression:"['is_alone_grade', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate(!0)}}},[t("a-radio",{attrs:{value:0}},[e._v("默认等级折扣")]),t("a-radio",{attrs:{value:1}},[e._v("单独设置折扣")])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_alone_grade"),expression:"form.getFieldValue('is_alone_grade')"}]},e._l(e.formData.userGradeList,(function(a){return t("a-form-item",{key:a.grade_id},[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["alone_grade_equity[grade_id:".concat(a.grade_id,"]"),{initialValue:e.formData.defaultUserGradeValue[a.grade_id],rules:[{required:!0,message:"折扣率不能为空"}]}],expression:"[`alone_grade_equity[grade_id:${item.grade_id}]`, {\n                    initialValue: formData.defaultUserGradeValue[item.grade_id], rules: [{ required: true, message: '折扣率不能为空'}]\n                  }]"}],attrs:{addonBefore:a.name,addonAfter:"折",inputProps:{min:0,max:9.9}}})],1)})),1),t("div",{staticClass:"form-item-help"},[e.form.getFieldValue("is_alone_grade")?t("p",{staticClass:"extra"},[e._v("单独折扣：折扣率范围0.0-9.9，例如: 9.8代表98折，0代表不折扣")]):t("p",{staticClass:"extra"},[e._v("默认折扣：默认为用户所属会员等级的折扣率")])])],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("apps-dealer"),expression:"$module('apps-dealer')"}]},[t("a-divider",{attrs:{orientation:"left"}},[e._v("分销设置")]),t("a-form-item",{attrs:{label:"分销佣金",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_ind_dealer",{initialValue:0,rules:[{required:!0}]}],expression:"['is_ind_dealer', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate(!0)}}},[t("a-radio",{attrs:{value:0}},[e._v("系统默认")]),t("a-radio",{attrs:{value:1}},[e._v("单独设置")])],1),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("若使用分销功能必须在 [分销中心 - 分销设置] 中开启")])])],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_ind_dealer"),expression:"form.getFieldValue('is_ind_dealer')"}],attrs:{label:"分销佣金类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["dealer_money_type",{initialValue:10,rules:[{required:!0}]}],expression:"['dealer_money_type', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate(!0)}}},[t("a-radio",{attrs:{value:10}},[e._v("百分比")]),t("a-radio",{attrs:{value:20}},[e._v("固定金额")])],1),e._l(e.formData.dealer.levelList,(function(a,r){return t("a-form-item",{key:r},[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:[a.value,{rules:[{required:!0,message:"佣金不能为空"}]}],expression:"[item.value, { rules: [{ required: true, message: '佣金不能为空'}] }]"}],attrs:{addonBefore:a.name,addonAfter:10==e.form.getFieldValue("dealer_money_type")?"%":"元",inputProps:{min:0,precision:2}}})],1)}))],2)],1)],1)]),t("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1)],1)],1)},i=[],o=(a("d81d"),a("b64b"),a("d3b7"),a("d084")),s=a("2af9"),l=a("e1fe"),n=a("b78d"),d=a("ca00"),u={components:{GoodsType:n["a"],SelectImage:s["h"],SelectVideo:s["m"],Ueditor:s["n"],InputNumberGroup:s["c"],MultiSpec:n["b"]},data:function(){return{tabKey:0,labelCol:{span:3},wrapperCol:{span:10},isLoading:!1,isBtnLoading:!1,form:this.$form.createForm(this),goodsId:null,formData:l["a"].formData}},created:function(){this.initData()},beforeDestroy:function(){l["a"].formData.goods={}},methods:{initData:function(){var e=this;this.goodsId=this.$route.query.goodsId,this.isLoading=!0,l["a"].getFromData(this.goodsId).then((function(){Object(d["g"])(e.form.getFieldsValue())||(e.form.setFieldsValue(l["a"].getFieldsValue()),e.$nextTick((function(){e.form.setFieldsValue(l["a"].getFieldsValue2()),e.onForceUpdate()}))),e.isLoading=!1}))},onForceUpdate:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$forceUpdate(),t&&setTimeout((function(){e.$forceUpdate()}),10)},handleTabs:function(e){this.tabKey=e},onReloadCategoryList:function(){var e=this;this.isLoading=!0,l["a"].getCategoryList().then((function(){e.isLoading=!1}))},onReloadServiceList:function(){var e=this;this.isLoading=!0,l["a"].getServiceList().then((function(){e.isLoading=!1}))},onReloadDeliveryList:function(){var e=this;this.isLoading=!0,l["a"].getDeliveryList().then((function(){e.isLoading=!1}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){if(e)return t.onTargetTabError(e),!1;if(20===a.spec_type){var r=t.$refs.MultiSpec;if(!r.verifyForm())return t.tabKey=1,!1;a.specData=r.getFromSpecData()}return a.categoryIds=a.categorys.map((function(e){return e.value})),delete a.categorys,t.onFormSubmit(a),!0}))},onTargetTabError:function(e){var t=[["goods_type","goods_name","categorys","imagesIds","delivery_id","is_ind_delivery_type","delivery_type"],["spec_type","goods_price","is_restrict","restrict_total","restrict_single"],["content"],["alone_grade_equity","first_money","second_money","third_money"]],a=Object.keys(e).shift();for(var r in t)if(t[r].indexOf(a)>-1){this.tabKey=parseInt(r);break}},onFormSubmit:function(e){var t=this;this.isLoading=!0,this.isBtnLoading=!0,o["e"]({goodsId:this.goodsId,form:e}).then((function(e){t.$message.success(e.message,1.5),setTimeout((function(){t.$router.push("./index")}),1500)})).catch((function(){t.isBtnLoading=!1})).finally((function(){return t.isLoading=!1}))}}},c=u,m=(a("09ff"),a("2877")),p=Object(m["a"])(c,r,i,!1,null,"775974e6",null);t["default"]=p.exports},cfdf:function(e,t,a){},db4e:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e.isLoading?e._e():t("a-table",{attrs:{rowKey:"category_id",columns:e.columns,dataSource:e.categoryList,defaultExpandAllRows:!0,expandIconColumnIndex:1,pagination:!1,loading:e.isLoading},scopedSlots:e._u([{key:"status",fn:function(a){return t("span",{},[t("a-tag",{attrs:{color:a?"green":""}},[e._v(e._s(a?"显示":"隐藏"))])],1)}},{key:"action",fn:function(a,r){return t("span",{},[t("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")])])}}],null,!1,1032135766)}),t("AddForm",{ref:"AddForm",on:{handleSubmit:e.handleRefresh}}),t("EditForm",{ref:"EditForm",on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=(a("d3b7"),a("6a54")),s=a("2af9"),l=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:"编辑商品分类",width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"分类名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),t("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("显示")]),t("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字'  }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},n=[],d={data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{add:function(){this.visible=!0},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){e||t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,o["a"]({form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(e){t.confirmLoading=!1}))}}},u=d,c=a("2877"),m=Object(c["a"])(u,l,n,!1,null,null,null),p=m.exports,v=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:"编辑商品分类",width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"分类名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),t("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("显示")]),t("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},f=[],g=a("88bc"),h=a.n(g),b={data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{edit:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this,t=this.$nextTick,a=this.form.setFieldsValue;t((function(){a(h()(e.record,["name","status","sort"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){e||t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,o["c"]({categoryId:this.record["category_id"],form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(e){t.confirmLoading=!1}))}}},_=b,C=Object(c["a"])(_,v,f,!1,null,null,null),y=C.exports,w={name:"Index",components:{STable:s["d"],AddForm:p,EditForm:y},data:function(){return{categoryList:[],queryParam:{},isLoading:!1,columns:[{title:"分类ID",dataIndex:"category_id"},{title:"分类名称",dataIndex:"name"},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}]}},created:function(){this.getCategoryList(!0)},methods:{getCategoryList:function(e){var t=this;e&&(this.isLoading=!0),o["d"]().then((function(e){t.categoryList=e.data.list})).finally((function(){return t.isLoading=!1}))},handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return o["b"]({categoryId:e["category_id"]}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleRefresh:function(){this.getCategoryList()}}},x=w,S=Object(c["a"])(x,r,i,!1,null,null,null);t["default"]=S.exports},dd3e:function(e,t,a){"use strict";a("cfdf")},e1fe:function(e,t,a){"use strict";var r=a("5530"),i=(a("d3b7"),a("3ca3"),a("ddb0"),a("d81d"),a("4de4"),a("159b"),a("2ef0")),o=a.n(i),s=a("8243"),l=a("d084"),n=a("2e1c"),d=a("19d3"),u=a("967a");t["a"]={goodsId:null,formData:{goods:{},categoryList:[],deliveryList:[],serviceList:[],defaultServiceIds:[],userGradeList:[],defaultUserGradeValue:{},dealer:{levelList:[{name:"一级佣金",value:"first_money"},{name:"二级佣金",value:"second_money"},{name:"三级佣金",value:"third_money"}]}},getFromData:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return this.goodsId=t,new Promise((function(a,r){Promise.all([e.getGoodsDetail(t),e.getCategoryList(),e.getDeliveryList(),e.getServiceList(),e.getUserGradeList()]).then((function(){e.setDefaultData(),a({formData:e.formData})}))}))},getGoodsDetail:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return!!t&&new Promise((function(a,r){l["d"]({goodsId:t}).then((function(t){e.formData.goods=t.data.goodsInfo,a()}))}))},getFieldsValue:function(){var e=this.formData.goods;e.categorys=this.formatCategoryIds(e.categoryIds);var t=o.a.pick(e,["goods_type","goods_name","categorys","goods_no","sort","status","spec_type","deduct_stock_type","is_restrict","content","selling_point","serviceIds","sales_initial","is_points_gift","is_points_discount","is_enable_grade","is_alone_grade","is_ind_dealer","dealer_money_type","first_money","second_money","third_money"]);return Object(r["a"])({},t)},getFieldsValue2:function(){var e=this.formData.goods,t={};if(10==e.goods_type&&(t["delivery_id"]=e["delivery_id"],t["is_ind_delivery_type"]=e["is_ind_delivery_type"],t["delivery_type"]=e["delivery_type"]),10==e.spec_type){var a=o.a.pick(e.skuList[0],["goods_price","line_price","stock_num","goods_weight"]);t=Object(r["a"])(Object(r["a"])({},t),a)}return 1==e.is_restrict&&(t["restrict_total"]=e["restrict_total"],t["restrict_single"]=e["restrict_single"]),t},formatCategoryIds:function(e){return e.map((function(e){return{value:e}}))},getCategoryList:function(){var e=this;return new Promise((function(t,a){s["a"].getCategoryTreeSelect().then((function(a){e.formData.categoryList=a,t()}))}))},getDeliveryList:function(){var e=this;return new Promise((function(t,a){u["b"]().then((function(a){e.formData.deliveryList=a.data.list,t()}))}))},getServiceList:function(){var e=this;return new Promise((function(t,a){d["b"]().then((function(a){e.formData.serviceList=a.data.list,t()}))}))},getUserGradeList:function(){var e=this;return new Promise((function(t,a){n["b"]({status:1}).then((function(a){e.formData.userGradeList=a.data.list,t()}))}))},setDefaultData:function(){this.setDefaultServiceIds(),this.setDefaultUserGradeValue()},setDefaultServiceIds:function(){var e=this.formData.serviceList;if(!this.goodsId){var t=e.filter((function(e){return e.is_default}));this.formData.defaultServiceIds=t.map((function(e){return e.service_id}))}},setDefaultUserGradeValue:function(){var e=this.formData.userGradeList,t=this.goodsId&&this.formData.goods.alone_grade_equity?this.formData.goods.alone_grade_equity:{},a={};e.forEach((function(e){a[e.grade_id]=t[e.grade_id]||e.equity.discount})),this.formData.defaultUserGradeValue=Object(r["a"])({},a)}}},ee5f:function(e,t,a){"use strict";a.r(t);a("ac1f"),a("841c");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.pageTitle))]),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{attrs:{span:6}},[t("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),t("a-col",{attrs:{span:8,offset:10}},[t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px",float:"right"},attrs:{placeholder:"请输入服务名称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"service_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15,scroll:{x:1300}},scopedSlots:e._u([{key:"summary",fn:function(a){return t("span",{},[t("p",{staticClass:"summary oneline-hide"},[e._v(e._s(a))])])}},{key:"is_default",fn:function(a){return t("span",{},[t("a-tag",{attrs:{color:a?"green":""}},[e._v(e._s(a?"是":"否"))])],1)}},{key:"status",fn:function(a){return t("span",{},[t("a-tag",{attrs:{color:a?"green":""}},[e._v(e._s(a?"显示":"隐藏"))])],1)}},{key:"action",fn:function(a,r){return t("div",{staticClass:"actions"},[t("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),t("AddForm",{ref:"AddForm",on:{handleSubmit:e.handleRefresh}}),t("EditForm",{ref:"EditForm",on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=a("5530"),s=(a("d3b7"),a("19d3")),l=a("2af9"),n=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"服务名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入服务与承诺的名称"}})],1),t("a-form-item",{attrs:{label:"概述",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["summary"],expression:"['summary']"}],attrs:{placeholder:"请输入概述内容 (300个字符以内)",autoSize:{minRows:4}}})],1),t("a-form-item",{attrs:{label:"是否默认",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"新增商品时是否默认勾选"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_default",{initialValue:1,rules:[{required:!0}]}],expression:"['is_default', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("是")]),t("a-radio",{attrs:{value:0}},[e._v("否")])],1)],1),t("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("显示")]),t("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},d=[],u={data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{add:function(){this.title="新增记录",this.visible=!0},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,s["a"]({form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(e){t.confirmLoading=!1}))}}},c=u,m=a("2877"),p=Object(m["a"])(c,n,d,!1,null,null,null),v=p.exports,f=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"服务名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入服务与承诺的名称"}})],1),t("a-form-item",{attrs:{label:"概述",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["summary"],expression:"['summary']"}],attrs:{placeholder:"请输入概述内容 (300个字符以内)",autoSize:{minRows:4}}})],1),t("a-form-item",{attrs:{label:"是否默认",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"新增商品时是否默认勾选"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_default",{initialValue:1,rules:[{required:!0}]}],expression:"['is_default', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("是")]),t("a-radio",{attrs:{value:0}},[e._v("否")])],1)],1),t("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("显示")]),t("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},g=[],h=a("88bc"),b=a.n(h),_={data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{edit:function(e){this.title="编辑记录",this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this,t=this.form.setFieldsValue;this.$nextTick((function(){t(b()(e.record,["name","summary","is_default","status","sort"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,s["d"]({serviceId:this.record["service_id"],form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(e){t.confirmLoading=!1}))}}},C=_,y=Object(m["a"])(C,f,g,!1,null,null,null),w=y.exports,x={name:"Index",components:{STable:l["d"],AddForm:v,EditForm:w},data:function(){var e=this;return{queryParam:{},isLoading:!1,columns:[{title:"服务ID",dataIndex:"service_id"},{title:"服务名称",dataIndex:"name",width:"200px"},{title:"概述",dataIndex:"summary",width:"300px",scopedSlots:{customRender:"summary"}},{title:"是否默认",dataIndex:"is_default",scopedSlots:{customRender:"is_default"}},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"更新时间",width:"180px",dataIndex:"update_time"},{title:"操作",width:"150px",fixed:"right",scopedSlots:{customRender:"action"}}],loadData:function(t){return s["e"](Object(o["a"])(Object(o["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return s["c"]({serviceId:e["service_id"]}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},S=x,L=(a("b84e"),Object(m["a"])(S,r,i,!1,null,"beb54932",null));t["default"]=L.exports},f11f:function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form,selfUpdate:!0},on:{submit:e.handleSubmit}},[t("a-tabs",{attrs:{activeKey:e.tabKey,tabBarStyle:{marginBottom:"30px"}},on:{change:e.handleTabs}},[t("a-tab-pane",{key:0,attrs:{tab:"基本信息"}}),t("a-tab-pane",{key:1,attrs:{tab:"规格/库存"}}),t("a-tab-pane",{key:2,attrs:{tab:"商品详情"}}),t("a-tab-pane",{key:3,attrs:{tab:"更多设置"}})],1),t("div",{staticClass:"tabs-content"},[t("div",{directives:[{name:"show",rawName:"v-show",value:0==e.tabKey,expression:"tabKey == 0"}],staticClass:"tab-pane"},[t("a-form-item",{attrs:{label:"商品类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("GoodsType",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_type",{initialValue:10,rules:[{required:!0}]}],expression:"['goods_type', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate(!0)}}})],1),t("a-form-item",{attrs:{label:"商品名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['goods_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符'  }] }]"}],attrs:{placeholder:"请输入商品名称"}})],1),t("a-form-item",{attrs:{label:"商品分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-tree-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["categorys",{rules:[{required:!0,message:"请至少选择1个商品分类"}]}],expression:"['categorys', { rules: [{ required: true, message: '请至少选择1个商品分类'  }] }]"}],attrs:{placeholder:"请选择商品分类",dropdownStyle:{maxHeight:"500px",overflow:"auto"},treeData:e.formData.categoryList,treeCheckable:"",treeCheckStrictly:"",allowClear:""}}),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{target:"_blank",to:{path:"/goods/category/index"}}},[e._v("去新增")]),t("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadCategoryList}},[e._v("刷新")])],1)],1),t("a-form-item",{attrs:{label:"商品图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸：750*750像素, 最多上传10张, 可拖拽图片调整顺序, 第1张将作为商品首图"}},[t("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["imagesIds",{rules:[{required:!0,message:"请至少上传1张商品图片"}]}],expression:"['imagesIds', { rules: [{ required: true, message: '请至少上传1张商品图片'  }] }]"}],attrs:{multiple:"",maxNum:10}})],1),t("a-form-item",{attrs:{label:"商品编码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_no"],expression:"['goods_no']"}],attrs:{placeholder:"请输入商品编码"}})],1),10==e.form.getFieldValue("goods_type")?t("a-form-item",{attrs:{label:"配送方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_ind_delivery_type",{initialValue:0,rules:[{required:!0}]}],expression:"['is_ind_delivery_type', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate()}}},[t("a-radio",{attrs:{value:0}},[e._v("系统默认")]),t("a-radio",{attrs:{value:1}},[e._v("单独配置")])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("is_ind_delivery_type"),expression:"form.getFieldValue('is_ind_delivery_type')"}]},[t("a-form-item",{attrs:{extra:"需在 [设置 - 配送方式] 中，开启支持的配送方式才可生效"}},[t("a-checkbox-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["delivery_type",{initialValue:[10,20],rules:[{required:!0,message:"配送方式至少选择一个"}]}],expression:"['delivery_type', { initialValue: [10, 20], rules: [{ required: true, message: '配送方式至少选择一个' }] }]"}],attrs:{options:[{label:"快递配送",value:10},{label:"上门自提",value:20}]}})],1)],1)],1):e._e(),10==e.form.getFieldValue("goods_type")?t("a-form-item",{attrs:{label:"运费模板",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["delivery_id",{rules:[{required:!0,message:"请选择运费模板"}]}],expression:"['delivery_id', { rules: [{ required: true, message: '请选择运费模板'  }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请选择运费模板"}},e._l(e.formData.deliveryList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.delivery_id}},[e._v(e._s(a.name))])})),1),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{target:"_blank",to:{path:"/setting/delivery/template/create"}}},[e._v("新增模板")]),t("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadDeliveryList}},[e._v("刷新")])],1)],1):e._e(),t("a-form-item",{attrs:{label:"商品状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:10,rules:[{required:!0}]}],expression:"['status', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("上架")]),t("a-radio",{attrs:{value:20}},[e._v("下架")])],1)],1),t("a-form-item",{attrs:{label:"商品排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true }] }]"}],attrs:{min:0}})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:1==e.tabKey,expression:"tabKey == 1"}],staticClass:"tab-pane"},[t("a-form-item",{attrs:{label:"规格类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["spec_type",{initialValue:10,rules:[{required:!0}]}],expression:"['spec_type', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate()}}},[t("a-radio",{attrs:{value:10}},[e._v("单规格")]),t("a-radio",{attrs:{value:20}},[e._v("多规格")])],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:20==e.form.getFieldValue("spec_type"),expression:"form.getFieldValue('spec_type') == 20"}]},[t("MultiSpec",{ref:"MultiSpec"})],1),t("div",{directives:[{name:"show",rawName:"v-show",value:10==e.form.getFieldValue("spec_type"),expression:"form.getFieldValue('spec_type') == 10"}]},[t("a-form-item",{attrs:{label:"商品价格",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际购买金额，最低0.00"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_price",{initialValue:1,rules:[{required:!0,message:"请输入商品价格"}]}],expression:"['goods_price', { initialValue: 1, rules: [{ required: true, message: '请输入商品价格' }] }]"}],attrs:{min:0,precision:2}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"划线价",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"划线价仅用于商品页展示"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["line_price"],expression:"['line_price']"}],attrs:{min:0,precision:2}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"当前库存数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际库存数量，为0时用户无法下单"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["stock_num",{initialValue:100,rules:[{required:!0,message:"请输入库存数量"}]}],expression:"['stock_num', { initialValue: 100, rules: [{ required: true, message: '请输入库存数量' }] }]"}],attrs:{min:0,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("件")])],1),t("a-form-item",{attrs:{label:"商品重量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商品的实际重量，用于计算运费"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_weight",{initialValue:0,rules:[{required:!0,message:"请输入库存数量"}]}],expression:"['goods_weight', { initialValue: 0, rules: [{ required: true, message: '请输入库存数量' }] }]"}],attrs:{min:0}}),t("span",{staticClass:"ml-10"},[e._v("千克 (Kg)")])],1)],1),t("a-form-item",{attrs:{label:"库存计算方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["deduct_stock_type",{initialValue:10,rules:[{required:!0}]}],expression:"['deduct_stock_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("下单减库存")]),t("a-radio",{attrs:{value:20}},[e._v("付款减库存")])],1)],1),t("a-form-item",{attrs:{label:"商品限购",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用于限制每人购买该商品的数量"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_restrict",{initialValue:0,rules:[{required:!0}]}],expression:"['is_restrict', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate()}}},[t("a-radio",{attrs:{value:0}},[e._v("关闭")]),t("a-radio",{attrs:{value:1}},[e._v("开启")])],1),e.form.getFieldValue("is_restrict")?t("div",{staticClass:"mt-10"},[t("a-form-item",[t("span",{staticClass:"mr-10"},[e._v("总限购")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_total",{rules:[{required:!0,message:"请输入总限购数量"}]}],expression:"['restrict_total', { rules: [{ required: true, message: '请输入总限购数量' }] }]"}],attrs:{min:1,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("件/人")])],1),t("a-form-item",[t("span",{staticClass:"mr-10"},[e._v("每单限购")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_single",{rules:[{required:!0,message:"请输入每单限购数量"}]}],expression:"['restrict_single', { rules: [{ required: true, message: '请输入每单限购数量' }] }]"}],attrs:{min:1,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("件/人")])],1)],1):e._e()],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:2==e.tabKey,expression:"tabKey == 2"}],staticClass:"tab-pane"},[t("a-form-item",{attrs:{label:"商品详情",labelCol:e.labelCol,wrapperCol:{span:16}}},[t("Ueditor",{directives:[{name:"decorator",rawName:"v-decorator",value:["content",{rules:[{required:!0,message:"商品详情不能为空"}]}],expression:"['content', { rules: [{ required: true, message: '商品详情不能为空'  }] }]"}]})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:3==e.tabKey,expression:"tabKey == 3"}],staticClass:"tab-pane"},[t("a-form-item",{attrs:{label:"主图视频",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议视频宽高比19:9，建议时长8-45秒"}},[t("SelectVideo",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_id"],expression:"['video_id']"}],attrs:{multiple:!1}})],1),t("a-form-item",{attrs:{label:"视频封面",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸：750像素*750像素"}},[t("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_cover_id"],expression:"['video_cover_id']"}],attrs:{multiple:!1}})],1),t("a-form-item",{attrs:{label:"商品卖点",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"一句话简述，例如：此款商品美观大方 性价比较高 不容错过"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["selling_point"],expression:"['selling_point']"}],attrs:{placeholder:"请输入商品卖点"}})],1),t("a-form-item",{attrs:{label:"服务与承诺",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[e.formData.serviceList?t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["serviceIds",{initialValue:e.formData.defaultServiceIds}],expression:"['serviceIds', { initialValue: formData.defaultServiceIds }]"}],attrs:{mode:"multiple",placeholder:"请选择服务与承诺"}},e._l(e.formData.serviceList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.service_id}},[e._v(e._s(a.name))])})),1):e._e(),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{target:"_blank",to:{path:"/goods/service/index"}}},[e._v("去新增")]),t("a",{attrs:{href:"javascript:;"},on:{click:e.onReloadServiceList}},[e._v("刷新")])],1)],1),t("a-form-item",{attrs:{label:"初始销量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端展示的销量 = 初始销量 + 实际销量"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sales_initial",{initialValue:0}],expression:"['sales_initial', { initialValue: 0}]"}]})],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("market-points"),expression:"$module('market-points')"}]},[t("a-divider",{attrs:{orientation:"left"}},[e._v("积分设置")]),t("a-form-item",{attrs:{label:"积分赠送",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后用户购买此商品将获得积分"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_points_gift",{initialValue:1,rules:[{required:!0}]}],expression:"['is_points_gift', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),t("a-form-item",{attrs:{label:"积分抵扣",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后用户购买此商品可以使用积分进行抵扣"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_points_discount",{initialValue:1,rules:[{required:!0}]}],expression:"['is_points_discount', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("user-grade"),expression:"$module('user-grade')"}]},[t("a-divider",{attrs:{orientation:"left"}},[e._v("会员折扣设置")]),t("a-form-item",{attrs:{label:"会员折扣",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后会员折扣，会员购买此商品可以享受会员等级折扣价"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_enable_grade",{initialValue:1,rules:[{required:!0}]}],expression:"['is_enable_grade', { initialValue: 1, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate(!0)}}},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),e.form.getFieldValue("is_enable_grade")?t("a-form-item",{attrs:{label:"会员折扣设置",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_alone_grade",{initialValue:0,rules:[{required:!0}]}],expression:"['is_alone_grade', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate(!0)}}},[t("a-radio",{attrs:{value:0}},[e._v("默认等级折扣")]),t("a-radio",{attrs:{value:1}},[e._v("单独设置折扣")])],1),e.form.getFieldValue("is_alone_grade")?t("div",e._l(e.formData.userGradeList,(function(a){return t("a-form-item",{key:a.grade_id},[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["alone_grade_equity[grade_id:".concat(a.grade_id,"]"),{initialValue:e.formData.defaultUserGradeValue[a.grade_id],rules:[{required:!0,message:"折扣率不能为空"}]}],expression:"[`alone_grade_equity[grade_id:${item.grade_id}]`, {\n                    initialValue: formData.defaultUserGradeValue[item.grade_id], rules: [{ required: true, message: '折扣率不能为空' }]\n                  }]"}],attrs:{addonBefore:a.name,addonAfter:"折",inputProps:{min:0,max:9.9}}})],1)})),1):e._e(),t("div",{staticClass:"form-item-help"},[e.form.getFieldValue("is_alone_grade")?t("p",{staticClass:"extra"},[e._v("单独折扣：折扣率范围0.0-9.9，例如: 9.8代表98折，0代表不折扣")]):t("p",{staticClass:"extra"},[e._v("默认折扣：默认为用户所属会员等级的折扣率")])])],1):e._e()],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.$module("apps-dealer"),expression:"$module('apps-dealer')"}]},[t("a-divider",{attrs:{orientation:"left"}},[e._v("分销设置")]),t("a-form-item",{attrs:{label:"分销佣金",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_ind_dealer",{initialValue:0,rules:[{required:!0}]}],expression:"['is_ind_dealer', { initialValue: 0, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate(!0)}}},[t("a-radio",{attrs:{value:0}},[e._v("系统默认")]),t("a-radio",{attrs:{value:1}},[e._v("单独设置")])],1),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("若使用分销功能必须在 [分销中心 - 分销设置] 中开启")])])],1),e.form.getFieldValue("is_ind_dealer")?t("a-form-item",{attrs:{label:"分销佣金类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["dealer_money_type",{initialValue:10,rules:[{required:!0}]}],expression:"['dealer_money_type', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:function(t){return e.onForceUpdate(!0)}}},[t("a-radio",{attrs:{value:10}},[e._v("百分比")]),t("a-radio",{attrs:{value:20}},[e._v("固定金额")])],1),e._l(e.formData.dealer.levelList,(function(a,r){return t("a-form-item",{key:r},[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:[a.value,{rules:[{required:!0,message:"佣金不能为空"}]}],expression:"[item.value, { rules: [{ required: true, message: '佣金不能为空' }] }]"}],attrs:{addonBefore:a.name,addonAfter:10==e.form.getFieldValue("dealer_money_type")?"%":"元",inputProps:{min:0,precision:2}}})],1)}))],2):e._e()],1)],1)]),t("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1)],1)],1)},i=[],o=(a("d81d"),a("b64b"),a("d3b7"),a("d084")),s=a("2af9"),l=a("e1fe"),n=a("b78d"),d={components:{GoodsType:n["a"],SelectImage:s["h"],SelectVideo:s["m"],Ueditor:s["n"],InputNumberGroup:s["c"],MultiSpec:n["b"]},data:function(){return{tabKey:0,labelCol:{span:3},wrapperCol:{span:10},isLoading:!1,isBtnLoading:!1,form:this.$form.createForm(this),formData:l["a"].formData}},created:function(){var e=this;this.isLoading=!0,l["a"].getFromData().then((function(){e.isLoading=!1}))},methods:{onForceUpdate:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$forceUpdate(),t&&setTimeout((function(){e.$forceUpdate()}),10)},handleTabs:function(e){this.tabKey=e},onReloadCategoryList:function(){var e=this;this.isLoading=!0,l["a"].getCategoryList().then((function(){e.isLoading=!1}))},onReloadServiceList:function(){var e=this;this.isLoading=!0,l["a"].getServiceList().then((function(){e.isLoading=!1}))},onReloadDeliveryList:function(){var e=this;this.isLoading=!0,l["a"].getDeliveryList().then((function(){e.isLoading=!1}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){if(e)return t.onTargetTabError(e),!1;if(20===a.spec_type){var r=t.$refs.MultiSpec;if(!r.verifyForm())return t.tabKey=1,!1;a.specData=r.getFromSpecData()}return a.categoryIds=a.categorys.map((function(e){return e.value})),delete a.categorys,t.onFormSubmit(a),!0}))},onTargetTabError:function(e){var t=[["goods_type","goods_name","categorys","imagesIds","delivery_id","is_ind_delivery_type","delivery_type"],["spec_type","goods_price","is_restrict","restrict_total","restrict_single"],["content"],["alone_grade_equity","first_money","second_money","third_money"]],a=Object.keys(e).shift();for(var r in t)if(t[r].indexOf(a)>-1){this.tabKey=parseInt(r);break}},onFormSubmit:function(e){var t=this;this.isLoading=!0,this.isBtnLoading=!0,o["a"]({form:e}).then((function(e){t.$message.success(e.message,1.5),setTimeout((function(){t.$router.push("./index")}),1500)})).catch((function(){t.isBtnLoading=!1})).finally((function(){return t.isLoading=!1}))}}},u=d,c=(a("5915"),a("2877")),m=Object(c["a"])(u,r,i,!1,null,"4278310d",null);t["default"]=m.exports},fe55:function(e,t,a){}}]);