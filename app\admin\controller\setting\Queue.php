<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\admin\controller\setting;

use think\response\Json;
use app\admin\controller\Controller;
use app\job\controller\Test as Testjob;
use app\common\service\system\Process as SystemProcessService;

/**
 * 队列服务管理
 * Class Queue
 * @package app\admin\controller
 */
class Queue extends Controller
{
    /**
     * 测试队列服务是否开启
     * @return Json
     */
    public function test(): Json
    {
        // 发送发布：任务名，参数，队列名
        Testjob::dispatch([]);
        // 等待5秒钟（等待队列服务消费）
        sleep(5);
        // 判断队列任务的最后消费时间是否在30秒内
        if (\time() - SystemProcessService::getLastWorkingTime('queue') <= 30) {
            return $this->renderSuccess('恭喜您，队列服务已开启');
        }
        return $this->renderError('很抱歉，队列服务未开启');
    }
}