<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\groupon;

use app\api\service\Goods as GoodsService;
use app\common\model\groupon\GoodsSku as GrouponGoodsSkuModel;
use cores\exception\BaseException;

/**
 * 拼团商品SKU模型
 * Class GoodsSku
 * @package app\api\model\groupon
 */
class GoodsSku extends GrouponGoodsSkuModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'store_id',
        'create_time',
        'update_time',
    ];

    /**
     * 获取拼团商品sku信息(指定的)
     * @param int $goodsId
     * @param int $grouponGoodsId
     * @param string $goodsSkuId
     * @return \app\common\model\GoodsSku|array|null
     * @throws BaseException
     */
    public static function getSkuInfo(int $goodsId, int $grouponGoodsId, string $goodsSkuId)
    {
        $goodsSkuInfo = GoodsService::getSkuInfo($goodsId, $goodsSkuId);
        $grouponSkuInfo = static::detail($grouponGoodsId, $goodsSkuId);
        if (empty($goodsSkuInfo) || empty($grouponSkuInfo)) {
            throwError('未找到SKU信息');
        }
        $goodsSkuInfo['original_price'] = $goodsSkuInfo['goods_price'];
        $goodsSkuInfo['groupon_price'] = $grouponSkuInfo['groupon_price'];
        $goodsSkuInfo['steps_price_config'] = $grouponSkuInfo['steps_price_config'];
        return $goodsSkuInfo;
    }
}