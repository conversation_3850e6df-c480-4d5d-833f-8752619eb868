{"name": "myclabs/php-enum", "type": "library", "description": "PHP Enum implementation", "keywords": ["enum"], "homepage": "http://github.com/myclabs/php-enum", "license": "MIT", "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "autoload-dev": {"psr-4": {"MyCLabs\\Tests\\Enum\\": "tests/"}}, "require": {"php": ">=5.4", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.7|^6.0", "squizlabs/php_codesniffer": "1.*"}}