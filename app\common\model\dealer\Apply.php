<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\dealer;

use cores\BaseModel;
use think\model\relation\BelongsTo;

/**
 * 分销商申请模型
 * Class Apply
 * @package app\common\model\dealer
 */
class Apply extends BaseModel
{
    // 定义表名
    protected $name = 'dealer_apply';

    // 定义主键
    protected $pk = 'apply_id';

    /**
     * 获取器：申请时间
     * @param $value
     * @return false|string
     */
    public function getApplyTimeAttr($value)
    {
        return format_time($value);
    }

    /**
     * 获取器：审核时间
     * @param $value
     * @return false|string
     */
    public function getAuditTimeAttr($value)
    {
        return $value > 0 ? format_time($value) : 0;
    }

    /**
     * 关联用户表
     * @return BelongsTo
     */
    public function referee(): BelongsTo
    {
        $module = self::getCalledModule();
        return $this->belongsTo("app\\{$module}\\model\\User", 'referee_id');
    }

    /**
     * 关联会员记录表
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        $module = self::getCalledModule();
        return $this->belongsTo("app\\{$module}\\model\\User");
    }

    /**
     * 分销商申请记录详情
     * @param $where
     * @return static|array|null
     */
    public static function detail($where)
    {
        return self::get($where);
    }

    /**
     * 购买指定商品成为分销商
     * @param int $userId 用户ID
     * @param array $goodsIds 商品ID集
     * @param int $storeId 商城ID
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function becomeDealerUser(int $userId, array $goodsIds, int $storeId): bool
    {
        // 验证是否设置
        $config = Setting::getItem('condition', $storeId);
        if (!$config['becomeBuyGoods'] || empty($config['becomeBuyGoodsIds'])) {
            return false;
        }
        // 判断商品是否在设置范围内
        $intersect = array_intersect($goodsIds, $config['becomeBuyGoodsIds']);
        if (empty($intersect)) {
            return false;
        }
        // 新增分销商用户
        User::add($userId, [
            'referee_id' => Referee::getRefereeUserId($userId, 1),
            'store_id' => $storeId,
        ]);
        return true;
    }
}