<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\api\model\xj;

use app\api\service\User as UserService;
use app\common\model\User as UserModel;
use app\common\model\xj\Sign as SignModel;
use cores\exception\BaseException;
use think\facade\Db;

/**
 * 商品评价模型
 * Class Sign
 * @package app\api\model
 */
class Sign extends SignModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'virtual_views',
        'actual_views',
        'is_delete',
        'store_id',
        'create_time',
        'update_time',
    ];

    /**
     * 获取器：文章详情HTML实体转换回普通字符
     * @param $value
     * @return string
     */
    public function getSignContentAttr($value): string
    {
        return htmlspecialchars_decode($value);
    }

    public function addView()
    {
        // 当前用户ID
        $userId = UserService::getCurrentLoginUserId();

        //是否观看-24小时
        $time = strtotime(Date('Y-m-d'));
        $log  = Db::name('xj_sign_log')
            ->where('create_time', '>=', $time)->where('user_id', $userId)->find();
        if (! $log) {
            $points = 1;
            //增加积分
            $data['value']       = $points;
            $data['store_id']    = self::$storeId;
            $data['create_time'] = time();
            $data['user_id']     = $userId;

            Db::name('xj_sign_log')->insert($data);

            $describe = "签到获取积分";
            UserModel::setIncPoints($userId, $points, $describe);
            $score = 1;

        } else {
            throwError('今日已签到！');
        }

        return $score;
    }

    /**
     * 获取文章详情
     * @param int $articleId 文章ID
     * @return Sign|array|null
     * @throws \cores\exception\BaseException
     */
    public static function getDetail(int $articleId)
    {
        // 获取文章详情
        $detail = parent::detail($articleId, ['image', 'videos']);
        if (empty($detail) || $detail['is_delete']) {
            throwError('很抱歉，当前视频不存在');
        }
        // 累积文章实际阅读数
        // static::setIncActualViews($articleId);
        return $detail;
    }

    /**
     * 累积文章实际阅读数
     * @param int $articleId 文章ID
     * @return void
     */
    private static function setIncActualViews(int $articleId): void
    {
        (new static )->setInc($articleId, 'actual_views', 1);
    }

    /**
     * 获取文章列表
     * @param int $categoryId
     * @param int $limit
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getList(int $categoryId = 0, int $limit = 15)
    {
        // 当前用户ID
        $userId = UserService::getCurrentLoginUserId();
        // 检索查询条件
        $filter                      = [];
        $categoryId > 0 && $filter[] = ['category_id', '=', $categoryId];
        // 获取列表数据
        $list = $this
            ->where('user_id', '=', $userId)
          
            ->order(['create_time' => 'desc'])
            ->select()->toArray();
        $list = array_column($list, 'show_time');
        return $list;
    }
}
