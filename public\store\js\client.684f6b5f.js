(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["client"],{1008:function(e,t,a){"use strict";a("afa0")},"2fa9":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"答题数",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["number",{initialValue:10,rules:[{required:!0,message:"请输入答题数"}]}],expression:"['number', {initialValue: 10, rules: [{ required: true, message: '请输入答题数' }] }]"}]}),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[t("span",[e._v("前端随机显示题目数量")])])])],1),t("a-form-item",{attrs:{label:"时间",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"每题限时"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["time",{initialValue:10}],expression:"['time', {initialValue: 10}]"}]})],1),t("a-form-item",{staticClass:"mt-30",attrs:{label:"是否开启访问",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["enabled",{rules:[{required:!0}]}],expression:"['enabled', { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[t("span",[e._v("注：如关闭，用户则无法通过H5端访问")])])])],1),t("a-form-item",{attrs:{label:"H5站点地址",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["baseUrl",{rules:[{required:!0,message:"请输入H5站点地址"}]}],expression:"['baseUrl', { rules: [{ required: true, message: '请输入H5站点地址' }] }]"}]}),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[t("span",[e._v("请填写H5端实际的访问地址，以")]),t("a-tag",{staticClass:"mlr-5"},[e._v("https://")]),e._v("开头； 斜杠 "),t("a-tag",{staticClass:"mlr-5"},[e._v("/")]),t("span",[e._v("结尾")])],1),t("p",{staticClass:"extra"},[t("span",[e._v("例如：https://www.aaa.com/")])])])],1),t("a-form-item",{attrs:{"wrapper-col":{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},i=[],l=(a("d3b7"),a("88bc")),s=a.n(l),n=a("ca00"),o=a("5530"),c=a("b775"),u={detail:"/client.h5.setting/detail",update:"/client.h5.setting/update"};function p(e){return Object(c["b"])({url:u.detail,method:"get",params:{key:e}})}function m(e,t){return Object(c["b"])({url:u.update,method:"post",data:Object(o["a"])({key:e},t)})}var d={data:function(){return{labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),key:"basic",record:{}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,p(this.key).then((function(t){e.record=t.data.detail,e.setFieldsValue()})).finally((function(t){e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,t=this.$nextTick,a=this.form;!Object(n["f"])(a.getFieldsValue())&&t((function(){a.setFieldsValue(s()(e,["enabled","baseUrl","one","tow","three","zero","number"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,m(this.key,{form:e}).then((function(e){t.$message.success(e.message,1.5)})).finally((function(e){t.isLoading=!1}))}}},f=d,b=(a("ec80"),a("2877")),v=Object(b["a"])(f,r,i,!1,null,"79187f8a",null);t["default"]=v.exports},"88bc":function(e,t,a){(function(t){var a=1/0,r=9007199254740991,i="[object Arguments]",l="[object Function]",s="[object GeneratorFunction]",n="[object Symbol]",o="object"==typeof t&&t&&t.Object===Object&&t,c="object"==typeof self&&self&&self.Object===Object&&self,u=o||c||Function("return this")();function p(e,t,a){switch(a.length){case 0:return e.call(t);case 1:return e.call(t,a[0]);case 2:return e.call(t,a[0],a[1]);case 3:return e.call(t,a[0],a[1],a[2])}return e.apply(t,a)}function m(e,t){var a=-1,r=e?e.length:0,i=Array(r);while(++a<r)i[a]=t(e[a],a,e);return i}function d(e,t){var a=-1,r=t.length,i=e.length;while(++a<r)e[i+a]=t[a];return e}var f=Object.prototype,b=f.hasOwnProperty,v=f.toString,h=u.Symbol,C=f.propertyIsEnumerable,g=h?h.isConcatSpreadable:void 0,w=Math.max;function y(e,t,a,r,i){var l=-1,s=e.length;a||(a=I),i||(i=[]);while(++l<s){var n=e[l];t>0&&a(n)?t>1?y(n,t-1,a,r,i):d(i,n):r||(i[i.length]=n)}return i}function _(e,t){return e=Object(e),j(e,t,(function(t,a){return a in e}))}function j(e,t,a){var r=-1,i=t.length,l={};while(++r<i){var s=t[r],n=e[s];a(n,s)&&(l[s]=n)}return l}function x(e,t){return t=w(void 0===t?e.length-1:t,0),function(){var a=arguments,r=-1,i=w(a.length-t,0),l=Array(i);while(++r<i)l[r]=a[t+r];r=-1;var s=Array(t+1);while(++r<t)s[r]=a[r];return s[t]=l,p(e,this,s)}}function I(e){return F(e)||q(e)||!!(g&&e&&e[g])}function S(e){if("string"==typeof e||$(e))return e;var t=e+"";return"0"==t&&1/e==-a?"-0":t}function q(e){return L(e)&&b.call(e,"callee")&&(!C.call(e,"callee")||v.call(e)==i)}var F=Array.isArray;function k(e){return null!=e&&A(e.length)&&!O(e)}function L(e){return V(e)&&k(e)}function O(e){var t=D(e)?v.call(e):"";return t==l||t==s}function A(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=r}function D(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function V(e){return!!e&&"object"==typeof e}function $(e){return"symbol"==typeof e||V(e)&&v.call(e)==n}var N=x((function(e,t){return null==e?{}:_(e,m(y(t,1),S))}));e.exports=N}).call(this,a("c8ba"))},9866:function(e,t,a){},afa0:function(e,t,a){},ec80:function(e,t,a){"use strict";a("9866")},f09e:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{staticClass:"mt-30",attrs:{label:"公众号名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,message:"请输入公众号名称"}]}],expression:"['name', { rules: [{ required: true, message: '请输入公众号名称' }] }]"}]})],1),t("a-form-item",{attrs:{label:"原始ID",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["originalId",{rules:[{required:!0,message:"请输入公众号原始ID"}]}],expression:"['originalId', { rules: [{ required: true, message: '请输入公众号原始ID' }] }]"}]})],1),t("a-form-item",{attrs:{label:"二维码图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["qrcodeImageId"],expression:"['qrcodeImageId']"}],attrs:{defaultList:e.record.qrcodeImage?[e.record.qrcodeImage]:[]}}),t("p",{staticClass:"form-item-help"},[t("small",[e._v("建议尺寸: 400px * 400px")])])],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("开发者信息")]),t("a-form-item",{staticClass:"mt-30",attrs:{label:"开发者 AppID",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["appId",{rules:[{required:!0,message:"请输入开发者AppID"}]}],expression:"['appId', { rules: [{ required: true, message: '请输入开发者AppID' }] }]"}]}),t("p",{staticClass:"form-item-help"},[t("small",[e._v("登录微信公众平台，设置与开发 - 基本配置 - 公众号开发信息，记录开发者ID(AppID)")])])],1),t("a-form-item",{attrs:{label:"开发者 AppSecret",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["appSecret",{rules:[{required:!0,message:"请输入开发者AppSecret"}]}],expression:"['appSecret', { rules: [{ required: true, message: '请输入开发者AppSecret' }] }]"}],attrs:{type:"password"}}),t("p",{staticClass:"form-item-help"},[t("small",[e._v("登录微信公众平台，设置与开发 - 基本配置 - 公众号开发信息，设置开发者密码(AppSecret)")])])],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("授权域名设置")]),t("a-form-item",{staticClass:"mt-30",attrs:{label:"业务域名",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("span",{staticClass:"f-14"},[e._v(e._s(e.domain))]),t("a",{staticClass:"ml-15 f-12",attrs:{href:"javascript:void(0);"},on:{click:function(t){return e.handleCopyLink(e.domain)}}},[e._v("点击复制")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("登录微信公众平台，设置与开发 - 公众号设置 - 功能设置，填写业务域名")])])]),t("a-form-item",{attrs:{label:"JS接口安全域名",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("span",{staticClass:"f-14"},[e._v(e._s(e.domain))]),t("a",{staticClass:"ml-15 f-12",attrs:{href:"javascript:void(0);"},on:{click:function(t){return e.handleCopyLink(e.domain)}}},[e._v("点击复制")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("登录微信公众平台，设置与开发 - 公众号设置 - 功能设置，填写JS接口安全域名")])])]),t("a-form-item",{attrs:{label:"网页授权域名",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("span",{staticClass:"f-14"},[e._v(e._s(e.domain))]),t("a",{staticClass:"ml-15 f-12",attrs:{href:"javascript:void(0);"},on:{click:function(t){return e.handleCopyLink(e.domain)}}},[e._v("点击复制")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("登录微信公众平台，设置与开发 - 公众号设置 - 功能设置，填写网页授权域名")])])]),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},i=[],l=(a("d3b7"),a("88bc")),s=a.n(l),n=a("ca00"),o=a("5530"),c=a("b775"),u={basic:"/client.wxofficial.setting/basic",detail:"/client.wxofficial.setting/detail",update:"/client.wxofficial.setting/update"};function p(e){return Object(c["b"])({url:u.basic,method:"get"})}function m(e,t){return Object(c["b"])({url:u.update,method:"post",data:Object(o["a"])({key:e},t)})}var d=a("2af9"),f={components:{SelectImage:d["c"]},data:function(){return{labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),record:{},domain:"",serverUrl:"",radioStyle:{display:"block",marginBottom:"16px"}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,p().then((function(t){e.record=t.data.detail,e.domain=t.data.domain,e.serverUrl=t.data.serverUrl,e.setFieldsValue()})).finally((function(t){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,t=this.$nextTick,a=this.form;!Object(n["f"])(a.getFieldsValue())&&t((function(){a.setFieldsValue(s()(e,["name","originalId","qrcodeImageId","appId","appSecret"]))}))},handleCopyLink:function(e){var t=this;this.$copyText(e).then((function(e){t.$message.success("复制成功",.8)}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,m("basic",{form:e}).then((function(e){t.$message.success(e.message,1.5)})).finally((function(e){return t.isLoading=!1}))}}},b=f,v=(a("1008"),a("2877")),h=Object(v["a"])(b,r,i,!1,null,"54d5b7f9",null);t["default"]=h.exports}}]);