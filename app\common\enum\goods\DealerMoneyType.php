<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\goods;

use app\common\enum\EnumBasics;

/**
 * 枚举类：商品记录 - 分销佣金类型
 * Class DealerMoneyType
 * @package app\common\enum\goods
 */
class DealerMoneyType extends EnumBasics
{
    // 百分比
    const PRORATE = 10;

    // 固定金额
    const FIXED = 20;

    /**
     * 获取枚举类型值
     * @return array
     */
    public static function data(): array
    {
        return [
            self::PRORATE => [
                'name' => '百分比',
                'value' => self::PRORATE,
            ],
            self::FIXED => [
                'name' => '固定金额',
                'value' => self::FIXED,
            ]
        ];
    }
}
