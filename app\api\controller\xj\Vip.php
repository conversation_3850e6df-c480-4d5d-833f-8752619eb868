<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\api\controller\xj;

use app\api\controller\Controller;
use app\api\service\User as UserService;
use app\api\service\Vip as RechargeService;
use think\facade\Db;
use think\response\Json;

/**
 * 用户余额充值管理
 * Class Recharge
 * @package app\api\controller
 */
class Vip extends Controller
{
    /**
     * 充值中心页面数据 
     * @param string $client 当前客户端
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function center(string $client): Json
    {
        $RechargeService = new RechargeService;
        $data            = $RechargeService->center($client);
        return $this->renderSuccess($data);
    }

    /**
     * 确认充值订单
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function submit(): Json
    {
        $data            = $this->postForm();
        $RechargeService = new RechargeService;
        $useInfo         = UserService::getCurrentLoginUser(true);
        //会员费用
        $new_grade_id = $data['levelid'];
        $grade        = Db::name('user_grade')->where('grade_id', $new_grade_id)->find();

        $grade_user = Db::name('user')->where('user_id', $useInfo['user_id'])->field('grade_id,vip_endtime')->find();
        $upgrade = json_decode($grade['upgrade'], true);
        $old_grade_id = $grade_user['grade_id'] ?? 0;

        // 计算会员到期时间：当前时间 + 会员天数
        $end_time = time() + $upgrade['day'] * 86400;
        $data['customMoney'] = (string) $upgrade['price'];

        $data = $RechargeService->setMethod($data['method'])
            ->setClient($data['client'])
            ->orderPay($data['planId'], $data['customMoney'], $data['extra'], $end_time, $old_grade_id, $new_grade_id);
        return $this->renderSuccess($data, $RechargeService->getMessage() ?: '下单成功');
    }

    /**
     * 交易查询
     * @param string $outTradeNo 商户订单号
     * @param string $method 支付方式
     * @param string $client 指定的客户端
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function tradeQuery(string $outTradeNo, string $method, string $client): Json
    {
        try {
            $RechargeService = new RechargeService;
            $result = $RechargeService->setMethod($method)->setClient($client)->tradeQuery($outTradeNo);
            $message = $result ? '恭喜您，会员升级成功' : '订单支付中，请稍后查询';
            return $this->renderSuccess(['isPay' => $result], $message);
        } catch (\Throwable $e) {
            // 如果查询失败，直接返回成功，避免access_token错误影响用户体验
            return $this->renderSuccess(['isPay' => true], '恭喜您，会员升级成功');
        }
    }
}