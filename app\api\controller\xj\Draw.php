<?php

declare (strict_types = 1);

namespace app\api\controller\xj;

use app\api\controller\Controller;
use app\api\model\xj\Draw as DrawModel;
use think\response\Json;

/**
 * 优惠券兑换控制器
 * Class Draw
 * @package app\api\controller\xj
 */
class Draw extends Controller
{
    /**
     * 获取兑换页面数据
     * @return Json
     */
    public function index(): Json
    {
        $model = new DrawModel;
        $data  = $model->getDrawData();
        return $this->renderSuccess($data);
    }

    /**
     * 抽奖
     * @return Json
     */
    public function add(): <PERSON><PERSON>
    {
        $model = new DrawModel;

        $detail = $model->add();
        if ($detail) {
            return $this->renderSuccess(['detail' => $detail], '抽奖成功！');
        } else {
            return $this->renderError($model->getError() ?: '抽奖失败');
        }

    }

    public function getCount(): Json
    {

        $model = new DrawModel;

        $count = $model->getCount();
        return $this->renderSuccess(['count' => $count], '');
    }
}
