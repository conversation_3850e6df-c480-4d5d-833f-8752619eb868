<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\bargain;

use app\common\model\bargain\Task as TaskModel;
use app\store\service\Goods as GoodsService;

/**
 * 砍价任务模型
 * Class Task
 * @package app\store\model\bargain
 */
class Task extends TaskModel
{
    /**
     * 获取列表数据
     * @param array $param
     * @return mixed|\think\Paginator
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getList(array $param = [])
    {
        // 设置基础查询条件
        $query = $this->setBaseQuery($this->alias, [
            ['goods', 'goods_id'],
            ['user', 'user_id'],
        ]);
        // 检索查询条件
        $filter = $this->getFilter($param);
        // 获取活动列表
        $list = $query->with(['user.avatar'])
            ->where($filter)
            ->where("{$this->alias}.is_delete", '=', 0)
            ->order(["{$this->alias}.create_time" => 'desc'])
            ->paginate(15);
        // 设置商品数据
        return !$list->isEmpty() ? GoodsService::setGoodsData($list) : $list;
    }

    /**
     * 检索查询条件
     * @param array $param
     * @return array
     */
    private function getFilter(array $param): array
    {
        // 设置默认查询参数
        $params = $this->setQueryDefaultValue($param, [
            'search' => '', // 会员昵称/商品名称
            'isBuy' => -1, // 是否购买
            'status' => -1, // 砍价状态
        ]);
        // 检索查询条件
        $filter = [];
        !empty($params['search']) && $filter[] = ['goods.goods_name|user.nick_name', 'like', "%{$params['search']}%"];
        $params['isBuy'] > -1 && $filter[] = ["{$this->alias}.is_buy", '=', (int)$params['isBuy']];
        $params['status'] > -1 && $filter[] = ["{$this->alias}.status", '=', (int)$params['status']];
        return $filter;
    }

    /**
     * 软删除
     * @return bool|false
     */
    public function setDelete(): bool
    {
        return $this->save(['is_delete' => 1]);
    }
}