(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["user"],{"10e3":function(e,a,t){},"2cd3":function(e,a,t){},"310f":function(e,a,t){"use strict";t("466b")},"466b":function(e,a,t){},"644d":function(e,a,t){"use strict";t("2cd3")},7524:function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-row",{staticClass:"row-item-search"},[a("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[a("a-form-item",{attrs:{label:"视频标题"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["title"],expression:"['title']"}],attrs:{placeholder:"请输入视频标题"}})],1),a("a-form-item",{attrs:{label:"状态"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:-1}],expression:"['status', { initialValue: -1 }]"}]},[a("a-select-option",{attrs:{value:-1}},[e._v("全部")]),a("a-select-option",{attrs:{value:1}},[e._v("显示")]),a("a-select-option",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{staticClass:"search-btn"},[a("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1),a("div",{staticClass:"row-item-tab clearfix"},[a("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1)],1),a("s-table",{ref:"table",attrs:{rowKey:"article_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15,scroll:{x:1400}},scopedSlots:e._u([{key:"image_url",fn:function(e){return a("span",{},[a("a",{attrs:{title:"点击查看原图",href:e,target:"_blank"}},[a("img",{attrs:{height:"50",src:e,alt:"封面图"}})])])}},{key:"stitle",fn:function(t){return a("span",{},[a("p",{staticClass:"twoline-hide",staticStyle:{width:"270px"}},[e._v(e._s(t))])])}},{key:"category",fn:function(t){return a("span",{},[e._v(e._s(t.name))])}},{key:"status",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:t?"green":""}},[e._v(e._s(t?"显示":"隐藏"))])],1)}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(a){return e.handleEdit(r)}}},[e._v("编辑")]),a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),a("AddForm",{ref:"AddForm",attrs:{categoryList:e.categoryList},on:{handleSubmit:e.handleRefresh}}),a("EditForm",{ref:"EditForm",attrs:{categoryList:e.categoryList},on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=t("5530"),n=(t("d3b7"),t("b775")),l={list:"/xj.video/list",detail:"/xj.video/detail",add:"/xj.video/add",edit:"/xj.video/edit",delete:"/xj.video/delete"};function s(e){return Object(n["b"])({url:l.list,method:"get",params:e})}function d(e){return Object(n["b"])({url:l.detail,method:"get",params:e})}function u(e){return Object(n["b"])({url:l.add,method:"post",data:e})}function c(e){return Object(n["b"])({url:l.edit,method:"post",data:e})}function m(e){return Object(n["b"])({url:l.delete,method:"post",data:e})}var p=t("89a2"),f=t("2af9"),v=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:780,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"视频标题",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["title",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['title', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),a("a-form-item",{attrs:{label:"积分",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"封面图",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["image_id",{rules:[{required:!0,message:"请选择1个封面图"}]}],expression:"['image_id', { rules: [{ required: true, message: '请选择1个封面图' }] }]"}],attrs:{defaultList:e.record.image?[e.record.image]:[]}})],1),a("a-form-item",{attrs:{label:"视频",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectVideo",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_id",{rules:[{required:!0,message:"请选择1个视频"}]}],expression:"['video_id', { rules: [{ required: true, message: '请选择1个视频' }] }]"}],attrs:{defaultList:e.record.videos?[e.record.videos]:[]}})],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("显示")]),a("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},h=[],b=t("88bc"),g=t.n(b),C={components:{SelectImage:f["h"],SelectVideo:f["m"],Ueditor:f["n"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{title:"编辑视频",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),articleId:null,record:{}}},methods:{edit:function(e){this.visible=!0,this.articleId=e,this.getDetail()},getDetail:function(){var e=this;this.confirmLoading=!0,d({articleId:this.articleId}).then((function(a){e.record=a.data.detail,e.setFieldsValue()})).finally((function(a){e.confirmLoading=!1}))},setFieldsValue:function(){var e=this,a=this.form.setFieldsValue;this.$nextTick((function(){a(g()(e.record,["title","show_type","category_id","video_id","image_id","content","sort","status","virtual_views","points"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.record={},this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,c({articleId:this.articleId,form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(e){a.confirmLoading=!1}))}}},_=C,w=t("2877"),y=Object(w["a"])(_,v,h,!1,null,null,null),x=y.exports,q=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:780,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"视频标题",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["title",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['title', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),a("a-form-item",{attrs:{label:"积分",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"封面图",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["image_id",{rules:[{required:!0,message:"请选择1个封面图"}]}],expression:"['image_id', { rules: [{ required: true, message: '请选择1个封面图' }] }]"}]})],1),a("a-form-item",{attrs:{label:"视频",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectVideo",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_id",{rules:[{required:!0,message:"请选择1个视频"}]}],expression:"['video_id', { rules: [{ required: true, message: '请选择1个视频' }] }]"}]})],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("显示")]),a("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},S=[],N={components:{SelectImage:f["h"],SelectVideo:f["m"],Ueditor:f["n"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{title:"新增视频",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{add:function(){this.visible=!0},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,u({form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(e){a.confirmLoading=!1}))}}},E=N,F=Object(w["a"])(E,q,S,!1,null,null,null),L=F.exports,I=[{title:"ID",dataIndex:"id"},{title:"封面图",dataIndex:"image_url",scopedSlots:{customRender:"image_url"}},{title:"视频标题",dataIndex:"title",width:"320px",scopedSlots:{customRender:"stitle"}},{title:"积分",dataIndex:"points"},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"创建时间",width:"180px",dataIndex:"create_time"},{title:"更新时间",width:"180px",dataIndex:"update_time"},{title:"操作",dataIndex:"action",width:"150px",fixed:"right",scopedSlots:{customRender:"action"}}],R={name:"Index",components:{ContentHeader:f["a"],STable:f["d"],AddForm:L,EditForm:x},data:function(){var e=this;return{expand:!1,searchForm:this.$form.createForm(this),categoryList:[],queryParam:{},isLoading:!1,columns:I,loadData:function(a){return s(Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){this.getCategoryList()},methods:{getCategoryList:function(){var e=this;this.isLoading=!0,p["d"]().then((function(a){e.categoryList=a.data.list})).finally((function(){return e.isLoading=!1}))},handleSearch:function(e){var a=this;e.preventDefault(),this.searchForm.validateFields((function(e,t){e||(a.queryParam=Object(o["a"])(Object(o["a"])({},a.queryParam),t),a.handleRefresh(!0))}))},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return m({articleId:e.id}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e.id)},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)}}},A=R,k=(t("e639"),Object(w["a"])(A,r,i,!1,null,"7a655bfc",null));a["default"]=k.exports},"7ad7":function(e,a,t){"use strict";t.r(a);for(var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("s-table",{ref:"table",attrs:{rowKey:"grade_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"upgrade",fn:function(t){return a("span",{},[e._v("消费满"+e._s(t.expend_money)+"元")])}},{key:"equity",fn:function(t){return a("span",{},[e._v(e._s(t.discount)+"折")])}},{key:"status",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:t?"green":""}},[e._v(e._s(t?"启用":"禁用"))])],1)}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(a){return e.handleEdit(r)}}},[e._v("编辑")]),a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),a("AddForm",{ref:"AddForm",on:{handleSubmit:e.handleRefresh}}),a("EditForm",{ref:"EditForm",on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=t("5530"),n=(t("d3b7"),t("2e1c")),l=t("2af9"),s=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"等级名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"例如：大众会员、黄金会员、铂金会员、钻石会员"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,message:"请输入等级名称"}]}],expression:"['name', { rules: [{ required: true, message: '请输入等级名称' }] }]"}]})],1),a("a-form-item",{attrs:{label:"等级权重",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"会员等级的权重，数字越大 等级越高"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["weight",{rules:[{required:!0,message:"请选择等级权重"}]}],expression:"['weight', { rules: [{ required: true, message: '请选择等级权重' }] }]"}]},e._l(e.weights,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t}},[e._v(e._s(t))])})),1)],1),a("a-form-item",{attrs:{label:"升级金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["upgrade.price",{rules:[{required:!0,message:"升级金额不能为空"}]}],expression:"['upgrade.price', { rules: [{ required: true, message: '升级金额不能为空' }] }]"}],attrs:{addonAfter:"元",inputProps:{min:.01}}})],1),a("a-form-item",{attrs:{label:"有效期",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["upgrade.day",{initialValue:365,rules:[{required:!0,message:"有效期不能为空"}]}],expression:"['upgrade.day', { initialValue: 365, rules: [{ required: true, message: '有效期不能为空' }] }]"}],attrs:{inputProps:{min:1}}})],1),a("a-form-item",{attrs:{label:"升级条件",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["upgrade.expend_money",{rules:[{required:!0,message:"升级条件不能为空"}]}],expression:"['upgrade.expend_money', { rules: [{ required: true, message: '升级条件不能为空' }] }]"}],attrs:{addonBefore:"实际消费金额满",addonAfter:"元",inputProps:{min:.01}}}),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("用户的实际消费金额满足后，自动升级")])])],1),a("a-form-item",{attrs:{label:"等级权益",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["equity.discount",{rules:[{required:!0,message:"等级权益不能为空"}]}],expression:"['equity.discount', { rules: [{ required: true, message: '等级权益不能为空' }] }]"}],attrs:{addonBefore:"折扣率",addonAfter:"折",inputProps:{min:0,max:9.9}}}),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("折扣率范围0.0-9.9，例如: 9.8代表98折，0代表不折扣")])])],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("启用")]),a("a-radio",{attrs:{value:0}},[e._v("禁用")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},d=[],u=[],c=1;c<=20;c++)u.push(c);for(var m={components:{InputNumberGroup:l["c"]},data:function(){return{title:"新增会员等级",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),weights:u}},created:function(){},methods:{add:function(){this.visible=!0},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,n["a"]({form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(){return a.confirmLoading=!1}))}}},p=m,f=t("2877"),v=Object(f["a"])(p,s,d,!1,null,null,null),h=v.exports,b=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"等级名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"例如：大众会员、黄金会员、铂金会员、钻石会员"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,message:"请输入等级名称"}]}],expression:"['name', { rules: [{ required: true, message: '请输入等级名称' }] }]"}]})],1),a("a-form-item",{attrs:{label:"等级权重",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"会员等级的权重，数字越大 等级越高"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["weight",{rules:[{required:!0,message:"请选择等级权重"}]}],expression:"['weight', { rules: [{ required: true, message: '请选择等级权重' }] }]"}]},e._l(e.weights,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t}},[e._v(e._s(t))])})),1)],1),a("a-form-item",{attrs:{label:"升级金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["upgrade.price",{rules:[{required:!0,message:"升级金额不能为空"}]}],expression:"['upgrade.price', { rules: [{ required: true, message: '升级金额不能为空' }] }]"}],attrs:{addonAfter:"元",inputProps:{min:.01}}})],1),a("a-form-item",{attrs:{label:"有效期",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["upgrade.day",{initialValue:365,rules:[{required:!0,message:"有效期不能为空"}]}],expression:"['upgrade.day', { initialValue: 365, rules: [{ required: true, message: '有效期不能为空' }] }]"}],attrs:{inputProps:{min:1}}})],1),a("a-form-item",{attrs:{label:"升级条件",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["upgrade.expend_money",{rules:[{required:!0,message:"升级条件不能为空"}]}],expression:"['upgrade.expend_money', { rules: [{ required: true, message: '升级条件不能为空' }] }]"}],attrs:{addonBefore:"实际消费金额满",addonAfter:"元",inputProps:{min:.01}}}),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("用户的实际消费金额满足后，自动升级")])])],1),a("a-form-item",{attrs:{label:"等级权益",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["equity.discount",{rules:[{required:!0,message:"等级权益不能为空"}]}],expression:"['equity.discount', { rules: [{ required: true, message: '等级权益不能为空' }] }]"}],attrs:{addonBefore:"折扣率",addonAfter:"折",inputProps:{min:0,max:9.9}}}),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("折扣率范围0.0-9.9，例如: 9.8代表98折，0代表不折扣")])])],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("启用")]),a("a-radio",{attrs:{value:0}},[e._v("禁用")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},g=[],C=t("88bc"),_=t.n(C),w=[],y=1;y<=20;y++)w.push(y);var x={components:{InputNumberGroup:l["c"]},data:function(){return{title:"编辑会员等级",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),weights:w,record:{}}},created:function(){},methods:{edit:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this.record,a=this.form.setFieldsValue;e.cascader=[e.province_id,e.city_id,e.region_id],this.$nextTick((function(){a(_()(e,["name","weight","upgrade","equity","status","sort","day","price"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,n["d"]({gradeId:this.record.grade_id,form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(){return a.confirmLoading=!1}))}}},q=x,S=Object(f["a"])(q,b,g,!1,null,null,null),N=S.exports,E={name:"Index",components:{STable:l["d"],AddForm:h,EditForm:N},data:function(){var e=this;return{queryParam:{},isLoading:!1,columns:[{title:"等级ID",dataIndex:"grade_id"},{title:"等级名称",dataIndex:"name"},{title:"等级权重",dataIndex:"weight"},{title:"升级金额（元）",dataIndex:"upgrade.price"},{title:"有效期（天）",dataIndex:"upgrade.day"},{title:"等级权益",dataIndex:"equity",scopedSlots:{customRender:"equity"}},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(a){return n["e"](Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return n["c"]({gradeId:e.grade_id}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)}}},F=E,L=Object(f["a"])(F,r,i,!1,null,null,null);a["default"]=L.exports},"88bc":function(e,a,t){(function(a){var t=1/0,r=9007199254740991,i="[object Arguments]",o="[object Function]",n="[object GeneratorFunction]",l="[object Symbol]",s="object"==typeof a&&a&&a.Object===Object&&a,d="object"==typeof self&&self&&self.Object===Object&&self,u=s||d||Function("return this")();function c(e,a,t){switch(t.length){case 0:return e.call(a);case 1:return e.call(a,t[0]);case 2:return e.call(a,t[0],t[1]);case 3:return e.call(a,t[0],t[1],t[2])}return e.apply(a,t)}function m(e,a){var t=-1,r=e?e.length:0,i=Array(r);while(++t<r)i[t]=a(e[t],t,e);return i}function p(e,a){var t=-1,r=a.length,i=e.length;while(++t<r)e[i+t]=a[t];return e}var f=Object.prototype,v=f.hasOwnProperty,h=f.toString,b=u.Symbol,g=f.propertyIsEnumerable,C=b?b.isConcatSpreadable:void 0,_=Math.max;function w(e,a,t,r,i){var o=-1,n=e.length;t||(t=S),i||(i=[]);while(++o<n){var l=e[o];a>0&&t(l)?a>1?w(l,a-1,t,r,i):p(i,l):r||(i[i.length]=l)}return i}function y(e,a){return e=Object(e),x(e,a,(function(a,t){return t in e}))}function x(e,a,t){var r=-1,i=a.length,o={};while(++r<i){var n=a[r],l=e[n];t(l,n)&&(o[n]=l)}return o}function q(e,a){return a=_(void 0===a?e.length-1:a,0),function(){var t=arguments,r=-1,i=_(t.length-a,0),o=Array(i);while(++r<i)o[r]=t[a+r];r=-1;var n=Array(a+1);while(++r<a)n[r]=t[r];return n[a]=o,c(e,this,n)}}function S(e){return F(e)||E(e)||!!(C&&e&&e[C])}function N(e){if("string"==typeof e||$(e))return e;var a=e+"";return"0"==a&&1/e==-t?"-0":a}function E(e){return I(e)&&v.call(e,"callee")&&(!g.call(e,"callee")||h.call(e)==i)}var F=Array.isArray;function L(e){return null!=e&&A(e.length)&&!R(e)}function I(e){return O(e)&&L(e)}function R(e){var a=k(e)?h.call(e):"";return a==o||a==n}function A(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=r}function k(e){var a=typeof e;return!!e&&("object"==a||"function"==a)}function O(e){return!!e&&"object"==typeof e}function $(e){return"symbol"==typeof e||O(e)&&h.call(e)==l}var j=q((function(e,a){return null==e?{}:y(e,m(w(a,1),N))}));e.exports=j}).call(this,t("c8ba"))},"89a2":function(e,a,t){"use strict";t.d(a,"d",(function(){return o})),t.d(a,"a",(function(){return n})),t.d(a,"c",(function(){return l})),t.d(a,"b",(function(){return s}));var r=t("b775"),i={list:"/content.article.category/list",add:"/content.article.category/add",edit:"/content.article.category/edit",delete:"/content.article.category/delete"};function o(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function n(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function l(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function s(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},"8eb9":function(e,a,t){"use strict";t("10e3")},c4e7:function(e,a,t){},dab6:function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-row",{staticClass:"row-item-search"},[a("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[a("a-form-item",{attrs:{label:"昵称/手机号"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["search"],expression:"['search']"}],attrs:{placeholder:"请输入昵称/手机号"}})],1),e.$module("user-grade")?a("a-form-item",{attrs:{label:"会员等级"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["gradeId",{initialValue:0}],expression:"['gradeId', { initialValue: 0 }]"}]},[a("a-select-option",{attrs:{value:0}},[e._v("全部")]),e._l(e.gradeList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.grade_id}},[e._v(e._s(t.name))])}))],2)],1):e._e(),a("a-form-item",{attrs:{label:"注册时间"}},[a("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime"],expression:"['betweenTime']"}],attrs:{format:"YYYY-MM-DD"}})],1),a("a-form-item",{staticClass:"search-btn"},[a("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1)],1),a("s-table",{ref:"table",attrs:{rowKey:"user_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"avatar_url",fn:function(e){return a("span",{},[a("div",{staticClass:"avatar"},[a("img",e?{attrs:{width:"45",height:"45",src:e,alt:"用户头像"}}:{attrs:{width:"45",height:"45",src:t("889b"),alt:"用户头像"}})])])}},{key:"main_info",fn:function(t){return a("span",{},[a("p",[e._v(e._s(t.nick_name))]),a("p",{staticClass:"c-p"},[e._v(e._s(t.mobile))])])}},{key:"grade",fn:function(t){return a("span",{},[t?a("a-tag",[e._v(e._s(t.name))]):a("span",[e._v("--")])],1)}},{key:"balance",fn:function(t,r){return a("span",{},[a("p",[a("span",[e._v("余额：")]),a("span",{staticClass:"c-p"},[e._v(e._s(t))])]),a("p",[a("span",[e._v("积分：")]),a("span",{staticClass:"c-p"},[e._v(e._s(r.points))])])])}},{key:"expend_money",fn:function(t){return a("span",{},[a("span",{staticClass:"c-p"},[e._v(e._s(t))])])}},{key:"platform",fn:function(e){return a("span",{staticClass:"platform"},[a("platform-icon",{attrs:{name:e,showTips:!0,iconSize:17}})],1)}},{key:"action",fn:function(t){return a("span",{staticClass:"actions"},[e.$module("market-recharge")?a("a",{directives:[{name:"action",rawName:"v-action:recharge",arg:"recharge"}],attrs:{title:"会员充值"},on:{click:function(a){return e.handleRecharge(t)}}},[e._v("充值")]):e._e(),e.$module("user-grade")?a("a",{directives:[{name:"action",rawName:"v-action:grade",arg:"grade"}],attrs:{title:"会员等级"},on:{click:function(a){return e.handleGrade(t)}}},[e._v("等级")]):e._e(),a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete(t)}}},[e._v("删除")])])}}])}),a("GradeForm",{ref:"GradeForm",attrs:{gradeList:e.gradeList},on:{handleSubmit:e.handleRefresh}}),a("RechargeForm",{ref:"RechargeForm",on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=t("5530"),n=(t("d3b7"),t("fa04")),l=t("fab29"),s=t("2e1c"),d=t("2af9"),u=t("8d5f"),c=function(){var e=this,a=e._self._c;return a("a-modal",{staticClass:"noborder",attrs:{title:e.title,width:520,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"会员ID",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("span",[e._v(e._s(e.record.user_id))])]),a("a-form-item",{attrs:{label:"会员等级",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["grade_id",{rules:[{required:!0}]}],expression:"['grade_id', { rules: [{ required: true }] }]"}],attrs:{placeholder:"请选择会员等级"}},[a("a-select-option",{attrs:{value:0}},[e._v("无等级")]),e._l(e.gradeList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.grade_id}},[e._v(e._s(t.name))])}))],2)],1),a("a-form-item",{attrs:{label:"管理员备注",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["remark",{rules:[{required:!0,message:"请输入管理员备注"}]}],expression:"['remark', { rules: [{ required: true, message: '请输入管理员备注' }] }]"}],attrs:{placeholder:"请输入管理员备注",rows:3}})],1)],1)],1)],1)},m=[],p=t("88bc"),f=t.n(p),v={components:{},props:{gradeList:{type:Array,required:!0}},data:function(){return{title:"设置会员等级",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{handle:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this.record,a=this.form.setFieldsValue;this.$nextTick((function(){a(f()(e,["grade_id"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,l["b"]({userId:this.record.user_id,form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(){return a.confirmLoading=!1}))}}},h=v,b=(t("8eb9"),t("2877")),g=Object(b["a"])(h,c,m,!1,null,null,null),C=g.exports,_=function(){var e=this,a=e._self._c;return a("a-modal",{staticClass:"noborder",attrs:{title:e.title,width:520,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-tabs",{attrs:{activeKey:e.activeKey},on:{change:e.onChangeTabs}},[a("a-tab-pane",{key:e.RECHARGE_TYPE_BALANCE,attrs:{tab:"充值余额"}},[e.activeKey===e.RECHARGE_TYPE_BALANCE?[a("a-form-item",{staticClass:"mb-5",attrs:{label:"会员ID",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("span",[e._v(e._s(e.record.user_id))])]),a("a-form-item",{staticClass:"mb-5",attrs:{label:"当前余额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("span",{staticClass:"c-p"},[e._v(e._s(e.record.balance))])]),a("a-form-item",{attrs:{label:"充值方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["".concat(e.RECHARGE_TYPE_BALANCE,".mode"),{initialValue:"inc",rules:[{required:!0}]}],expression:"[`${RECHARGE_TYPE_BALANCE}.mode`, { initialValue: 'inc', rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:"inc"}},[e._v("增加")]),a("a-radio",{attrs:{value:"dec"}},[e._v("减少")]),a("a-radio",{attrs:{value:"final"}},[e._v("最终金额")])],1)],1),a("a-form-item",{attrs:{label:"变更金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["".concat(e.RECHARGE_TYPE_BALANCE,".money"),{initialValue:"",rules:[{required:!0,message:"请输入变更的金额"}]}],expression:"[`${RECHARGE_TYPE_BALANCE}.money`, { initialValue: '', rules: [{ required: true, message: '请输入变更的金额' }] }]"}],attrs:{min:.01}})],1),a("a-form-item",{attrs:{label:"管理员备注",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["".concat(e.RECHARGE_TYPE_BALANCE,".remark"),{rules:[{required:!0,message:"请输入管理员备注"}]}],expression:"[`${RECHARGE_TYPE_BALANCE}.remark`, { rules: [{ required: true, message: '请输入管理员备注' }] }]"}],attrs:{placeholder:"请输入管理员备注",rows:3}})],1)]:e._e()],2),a("a-tab-pane",{key:e.RECHARGE_TYPE_POINTS,attrs:{tab:"充值积分"}},[e.activeKey===e.RECHARGE_TYPE_POINTS?[a("a-form-item",{staticClass:"mb-5",attrs:{label:"会员ID",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("span",[e._v(e._s(e.record.user_id))])]),a("a-form-item",{staticClass:"mb-5",attrs:{label:"当前积分",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("span",{staticClass:"c-p"},[e._v(e._s(e.record.points))])]),a("a-form-item",{attrs:{label:"充值方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["".concat(e.RECHARGE_TYPE_POINTS,".mode"),{initialValue:"inc",rules:[{required:!0}]}],expression:"[`${RECHARGE_TYPE_POINTS}.mode`, { initialValue: 'inc', rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:"inc"}},[e._v("增加")]),a("a-radio",{attrs:{value:"dec"}},[e._v("减少")]),a("a-radio",{attrs:{value:"final"}},[e._v("最终积分")])],1)],1),a("a-form-item",{attrs:{label:"变更数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["".concat(e.RECHARGE_TYPE_POINTS,".value"),{initialValue:"",rules:[{required:!0,message:"请输入变更的金数量"}]}],expression:"[`${RECHARGE_TYPE_POINTS}.value`, { initialValue: '', rules: [{ required: true, message: '请输入变更的金数量' }] }]"}],attrs:{min:.01}})],1),a("a-form-item",{attrs:{label:"管理员备注",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["".concat(e.RECHARGE_TYPE_POINTS,".remark"),{rules:[{required:!0,message:"请输入管理员备注"}]}],expression:"[`${RECHARGE_TYPE_POINTS}.remark`, { rules: [{ required: true, message: '请输入管理员备注' }] }]"}],attrs:{placeholder:"请输入管理员备注",rows:3}})],1)]:e._e()],2)],1)],1)],1)],1)},w=[],y="balance",x="points",q={components:{},data:function(){return{title:"会员充值",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),activeKey:y,RECHARGE_TYPE_BALANCE:y,RECHARGE_TYPE_POINTS:x,record:{}}},methods:{handle:function(e){this.visible=!0,this.record=e},onChangeTabs:function(e){this.activeKey=e},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this,t=this.record,r=this.activeKey;this.confirmLoading=!0,l["d"]({userId:t.user_id,target:r,form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(){return a.confirmLoading=!1}))}}},S=q,N=(t("644d"),Object(b["a"])(S,_,w,!1,null,"2bace808",null)),E=N.exports,F=Object(n["c"])([{title:"会员ID",dataIndex:"user_id"},{title:"会员头像",dataIndex:"avatar_url",scopedSlots:{customRender:"avatar_url"}},{title:"昵称/手机号",scopedSlots:{customRender:"main_info"}},{title:"会员等级",moduleKey:"user-grade",dataIndex:"grade",scopedSlots:{customRender:"grade"}},{title:"余额/积分",dataIndex:"balance",scopedSlots:{customRender:"balance"}},{title:"实际消费金额",dataIndex:"expend_money",scopedSlots:{customRender:"expend_money"}},{title:"注册来源",dataIndex:"platform",scopedSlots:{customRender:"platform"}},{title:"注册时间",dataIndex:"create_time"},{title:"操作",width:"180px",scopedSlots:{customRender:"action"}}]),L={name:"Index",components:{STable:d["d"],GradeForm:C,RechargeForm:E,PlatformIcon:u["a"]},data:function(){var e=this;return{searchForm:this.$form.createForm(this),queryParam:{},isLoading:!1,columns:F,loadData:function(a){return l["c"](Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))},gradeList:[]}},created:function(){this.getGradeList()},methods:{getGradeList:function(){var e=this;s["b"]().then((function(a){e.gradeList=a.data.list}))},handleGrade:function(e){this.$refs.GradeForm.handle(e)},handleRecharge:function(e){this.$refs.RechargeForm.handle(e)},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return l["a"]({userId:e.user_id}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleSearch:function(e){var a=this;e.preventDefault(),this.searchForm.validateFields((function(e,t){e||(a.queryParam=Object(o["a"])(Object(o["a"])({},a.queryParam),t),a.handleRefresh(!0))}))},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)}}},I=L,R=(t("310f"),Object(b["a"])(I,r,i,!1,null,"a82ebd74",null));a["default"]=R.exports},e639:function(e,a,t){"use strict";t("c4e7")}}]);