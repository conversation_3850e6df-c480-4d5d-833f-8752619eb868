<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\groupon;

use app\common\model\groupon\Task as TaskModel;
use app\common\service\Goods as GoodsService;

/**
 * 拼团拼单模型
 * Class Task
 * @package app\store\model\groupon
 */
class Task extends TaskModel
{
    /**
     * 获取列表
     * @param array $param
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getList(array $param): \think\Paginator
    {
        // 设置基础查询条件
        $query = $this->setBaseQuery($this->alias, [
            ['goods', 'goods_id'],
            ['user', 'user_id'],
        ]);
        // 检索查询条件
        $filter = $this->getFilter($param);
        // 获取活动列表
        $list = $query->where($filter)
            ->where("{$this->alias}.is_delete", '=', 0)
            ->order(["{$this->alias}.create_time" => 'desc', $this->getPk()])
            ->paginate(15);
        // 设置商品数据
        if (!$list->isEmpty()) {
            $list = $this->setGoodsListData($list, true);
            $list = static::preload($list, ['user.avatar', 'groupGoods']);
        }
        return $list;
    }

    /**
     * 检索查询条件
     * @param array $param
     * @return array
     */
    private function getFilter(array $param): array
    {
        // 设置默认查询参数
        $params = $this->setQueryDefaultValue($param, [
            'search' => '',     // 会员昵称/商品名称
            'activeType' => 0,  // 拼团类型
            'status' => 0,      // 拼单状态
        ]);
        // 检索查询条件
        $filter = [];
        !empty($params['search']) && $filter[] = ['goods.goods_name|user.nick_name', 'like', "%{$params['search']}%"];
        $params['activeType'] > 0 && $filter[] = ["{$this->alias}.active_type", '=', (int)$params['activeType']];
        $params['status'] > 0 && $filter[] = ["{$this->alias}.status", '=', (int)$params['status']];
        return $filter;
    }

    /**
     * 设置商品展示的数据
     * @param $data
     * @param bool $isMultiple
     * @param array $hidden 隐藏的属性
     * @param callable|null $callback
     * @return mixed
     */
    protected function setGoodsListData($data, bool $isMultiple = true, array $hidden = [], callable $callback = null)
    {
        // 设置主商品数据
        $data = GoodsService::setGoodsData($data, $isMultiple, $hidden);
        if (!$isMultiple) $dataSource = [&$data]; else $dataSource = &$data;
        // 整理商品数据
        foreach ($dataSource as &$item) {
            // 商品名称
            $item['goods_name'] = $item['goods']['goods_name'];
            // 商品图片
            $item['goods_image'] = $item['goods']['goods_image'];
            // 商品原价
            $item['original_price'] = $item['goods']['goods_price_min'];
            // 回调函数
            is_callable($callback) && call_user_func($callback, $item);
        }
        return $data;
    }
}