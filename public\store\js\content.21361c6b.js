(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["content"],{"0175":function(t,e,a){},"170a":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"card-title"},[t._v(t._s(t.$route.meta.title))]),e("div",{staticClass:"table-operator"}),e("s-table",{ref:"table",attrs:{rowKey:"article_id",loading:t.isLoading,columns:t.columns,data:t.loadData,pageSize:15,scroll:{x:1450}},scopedSlots:t._u([{key:"image_url",fn:function(t){return e("span",{},[e("a",{attrs:{title:"点击查看原图",href:t,target:"_blank"}},[e("img",{attrs:{height:"50",src:t,alt:"封面图"}})])])}},{key:"stitle",fn:function(a,r){return[e("a-popover",{scopedSlots:t._u([{key:"content",fn:function(){return[e("p",[t._v(t._s(r.article.title))])]},proxy:!0}],null,!0)},[t._v(" "+t._s(r.article.title)+" ")])]}},{key:"answer",fn:function(a,r){return e("span",{},[e("p",[t._v(t._s(r.article.answer_text))])])}},{key:"content",fn:function(a,r){return e("span",{},[e("p",[t._v(t._s(r.answer))])])}},{key:"status",fn:function(a,r){return e("span",{},[e("a-tag",{staticClass:"cur-p",attrs:{color:1==r.isright?"green":"red"}},[t._v(t._s(1==r.isright?"正确":"错误"))])],1)}}])})],1)},i=[],n=a("5530"),o=(a("d3b7"),a("b63a")),s=(a("89a2"),a("2af9")),l=a("8104"),c=a("8320"),u=[{title:"题目",align:"center",dataIndex:"title",width:500,ellipsis:!0,scopedSlots:{customRender:"stitle"}},{title:"正确答案",align:"answer",scopedSlots:{customRender:"answer"}},{title:"答题内容",align:"content",scopedSlots:{customRender:"content"}},{title:"正确与否",align:"status",scopedSlots:{customRender:"status"}}],d={name:"Index",components:{ContentHeader:s["a"],STable:s["b"],AddForm:c["a"],EditForm:l["a"]},data:function(){var t=this;return{logId:null,expand:!1,searchForm:this.$form.createForm(this),categoryList:[],queryParam:{},isLoading:!1,columns:u,loadData:function(e){return o["b"](Object(n["a"])(Object(n["a"])({},e),t.queryParam)).then((function(t){return t.data.list}))}}},created:function(){this.queryParam.logId=this.$route.query.logId},methods:{getDatiList:function(t){var e=this;this.isLoading=!0,o["b"]({logId:t}).then((function(t){e.loadData=t.data.list})).finally((function(t){e.isLoading=!1}))},handleDelete:function(t){var e=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return o["d"]({articleId:t.article_id}).then((function(t){e.$message.success(t.message,1.5),e.handleRefresh()})).finally((function(t){a.destroy()}))}})},handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(t){this.$refs.EditForm.edit(t.id)},handleRefresh:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(t)}}},m=d,f=(a("3d59"),a("2877")),p=Object(f["a"])(m,r,i,!1,null,"9c6db898",null);e["default"]=p.exports},2217:function(t,e,a){},"2b20":function(t,e,a){},32261:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"card-title"},[t._v(t._s(t.$route.meta.title))]),e("div",{staticClass:"table-operator"},[e("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:t.handleAdd}},[t._v("新增")])],1),t.isLoading?t._e():e("a-table",{attrs:{rowKey:"category_id",columns:t.columns,dataSource:t.categoryList,defaultExpandAllRows:!0,expandIconColumnIndex:1,pagination:!1,loading:t.isLoading},scopedSlots:t._u([{key:"status",fn:function(a){return e("span",{},[e("a-tag",{attrs:{color:a?"green":""}},[t._v(t._s(a?"显示":"隐藏"))])],1)}},{key:"action",fn:function(a,r){return e("span",{},[e("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(e){return t.handleEdit(r)}}},[t._v("编辑")]),e("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(e){return t.handleDelete(r)}}},[t._v("删除")])])}}],null,!1,1032135766)}),e("AddForm",{ref:"AddForm",on:{handleSubmit:t.handleRefresh}}),e("EditForm",{ref:"EditForm",on:{handleSubmit:t.handleRefresh}})],1)},i=[],n=(a("d3b7"),a("89a2")),o=a("2af9"),s=function(){var t=this,e=t._self._c;return e("a-modal",{attrs:{title:"编辑商品分类",width:720,visible:t.visible,confirmLoading:t.confirmLoading,maskClosable:!1},on:{ok:t.handleSubmit,cancel:t.handleCancel}},[e("a-spin",{attrs:{spinning:t.confirmLoading}},[e("a-form",{attrs:{form:t.form}},[e("a-form-item",{attrs:{label:"分类名称",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', {rules: [{required: true, min: 2, message: '请输入至少2个字符'}]}]"}]})],1),e("a-form-item",{attrs:{label:"状态",labelCol:t.labelCol,wrapperCol:t.wrapperCol,extra:"用户端是否展示"}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', {initialValue: 1, rules: [{required: true}]}]"}]},[e("a-radio",{attrs:{value:1}},[t._v("显示")]),e("a-radio",{attrs:{value:0}},[t._v("隐藏")])],1)],1),e("a-form-item",{attrs:{label:"排序",labelCol:t.labelCol,wrapperCol:t.wrapperCol,extra:"数字越小越靠前"}},[e("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', {initialValue: 100, rules: [{required: true, message: '请输入至少1个数字'}]}]"}],attrs:{min:0}})],1)],1)],1)],1)},l=[],c={data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{add:function(){this.visible=!0},handleSubmit:function(t){var e=this;t.preventDefault();var a=this.form.validateFields;a((function(t,a){t||e.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(t){var e=this;this.confirmLoading=!0,n["a"]({form:t}).then((function(a){e.$message.success(a.message,1.5),e.handleCancel(),e.$emit("handleSubmit",t)})).finally((function(t){e.confirmLoading=!1}))}}},u=c,d=a("2877"),m=Object(d["a"])(u,s,l,!1,null,null,null),f=m.exports,p=function(){var t=this,e=t._self._c;return e("a-modal",{attrs:{title:"编辑商品分类",width:720,visible:t.visible,confirmLoading:t.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:t.handleSubmit,cancel:t.handleCancel}},[e("a-spin",{attrs:{spinning:t.confirmLoading}},[e("a-form",{attrs:{form:t.form}},[e("a-form-item",{attrs:{label:"分类名称",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', {rules: [{required: true, min: 2, message: '请输入至少2个字符'}]}]"}]})],1),e("a-form-item",{attrs:{label:"状态",labelCol:t.labelCol,wrapperCol:t.wrapperCol,extra:"用户端是否展示"}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', {initialValue: 1, rules: [{required: true}]}]"}]},[e("a-radio",{attrs:{value:1}},[t._v("显示")]),e("a-radio",{attrs:{value:0}},[t._v("隐藏")])],1)],1),e("a-form-item",{attrs:{label:"排序",labelCol:t.labelCol,wrapperCol:t.wrapperCol,extra:"数字越小越靠前"}},[e("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', {initialValue: 100, rules: [{required: true, message: '请输入至少1个数字'}]}]"}],attrs:{min:0}})],1)],1)],1)],1)},h=[],v=(a("b1f8"),a("88bc")),b=a.n(v),g={data:function(){return{title:"",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{edit:function(t){this.visible=!0,this.record=t,this.setFieldsValue()},setFieldsValue:function(){var t=this,e=this.$nextTick,a=this.form.setFieldsValue;e((function(){a(b()(t.record,["name","status","sort"]))}))},handleSubmit:function(t){var e=this;t.preventDefault();var a=this.form.validateFields;a((function(t,a){t||e.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(t){var e=this;this.confirmLoading=!0,n["c"]({categoryId:this.record["category_id"],form:t}).then((function(a){e.$message.success(a.message,1.5),e.handleCancel(),e.$emit("handleSubmit",t)})).finally((function(t){e.confirmLoading=!1}))}}},y=g,_=Object(d["a"])(y,p,h,!1,null,null,null),C=_.exports,w={name:"Index",components:{STable:o["b"],AddForm:f,EditForm:C},data:function(){return{categoryList:[],queryParam:{},isLoading:!1,columns:[{title:"分类ID",dataIndex:"category_id"},{title:"分类名称",dataIndex:"name"},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}]}},created:function(){this.getCategoryList(!0)},methods:{getCategoryList:function(t){var e=this;t&&(this.isLoading=!0),n["d"]().then((function(t){e.categoryList=t.data.list})).finally((function(t){e.isLoading=!1}))},handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(t){this.$refs.EditForm.edit(t)},handleDelete:function(t){var e=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return n["b"]({categoryId:t["category_id"]}).then((function(t){e.$message.success(t.message,1.5),e.handleRefresh()})).finally((function(t){a.destroy()}))}})},handleRefresh:function(){this.getCategoryList()}}},x=w,L=Object(d["a"])(x,r,i,!1,null,null,null);e["default"]=L.exports},"3d59":function(t,e,a){"use strict";a("0175")},"4e7e":function(t,e,a){"use strict";a("2217")},6475:function(t,e,a){"use strict";a.r(e);a("b0c0");var r=function(){var t=this,e=t._self._c;return e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"card-title"},[t._v(t._s(t.$route.meta.title))]),e("div",{staticClass:"table-operator"},[e("a-row",{staticClass:"row-item-search"},[e("a-form",{staticClass:"search-form",attrs:{form:t.searchForm,layout:"inline"},on:{submit:t.handleSearch}},[e("a-form-item",{attrs:{label:"题目分类"}},[e("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["categoryId",{initialValue:-1}],expression:"['categoryId', { initialValue: -1 }]"}]},[e("a-select-option",{attrs:{value:-1}},[t._v("全部")]),t._l(t.categoryList,(function(a,r){return e("a-select-option",{key:r,attrs:{value:a.category_id}},[t._v(t._s(a.name))])}))],2)],1),e("a-form-item",{staticClass:"search-btn"},[e("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[t._v("搜索")])],1)],1)],1)],1),e("s-table",{ref:"table",attrs:{rowKey:"id",loading:t.isLoading,columns:t.columns,data:t.loadData,pageSize:15,scroll:{x:1450}},scopedSlots:t._u([{key:"image_url",fn:function(t){return e("span",{},[e("a",{attrs:{title:"点击查看原图",href:t,target:"_blank"}},[e("img",{attrs:{height:"50",src:t,alt:"封面图"}})])])}},{key:"stitle",fn:function(a,r){return[e("a-popover",{scopedSlots:t._u([{key:"content",fn:function(){return[e("p",[t._v(t._s(a))])]},proxy:!0}],null,!0)},[t._v(" "+t._s(a)+" ")])]}},{key:"category",fn:function(a){return e("span",{},[t._v(t._s(a.name))])}},{key:"user",fn:function(a){return e("span",{},[e("p",[t._v(t._s(a.real_name))]),e("p",{staticClass:"c-p"},[t._v(t._s(a.mobile))]),e("p",{staticClass:"c-p"},[t._v(t._s(a.address))])])}},{key:"status",fn:function(a,r){return e("span",{},[e("a-tag",{staticClass:"cur-p",attrs:{color:1==a?"green":""},on:{click:function(e){return t.handleUpdateStatus([r.article_id],1!=a)}}},[t._v(t._s(1==a?"显示":"隐藏"))])],1)}},{key:"action",fn:function(a,r){return e("span",{},[e("router-link",{attrs:{to:{path:"/paper/log",query:{logId:r.id}}}},[t._v("答题详情")])],1)}}])})],1)},i=[],n=a("5530"),o=(a("d3b7"),a("b63a")),s=a("89a2"),l=a("2af9"),c=a("8104"),u=a("8320"),d=[{title:"得分",dataIndex:"score"},{title:"用时",dataIndex:"time"},{title:"用户信息",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"答题时间",width:"180px",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"150px",fixed:"right",scopedSlots:{customRender:"action"}}],m={name:"Index",components:{ContentHeader:l["a"],STable:l["b"],AddForm:u["a"],EditForm:c["a"]},data:function(){var t=this;return{expand:!1,searchForm:this.$form.createForm(this),categoryList:[],queryParam:{},isLoading:!1,columns:d,loadData:function(e){return o["c"](Object(n["a"])(Object(n["a"])({},e),t.queryParam)).then((function(t){return t.data.list}))}}},created:function(){this.getCategoryList()},methods:{handleUpdateStatus:function(t){var e=this,a=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(!this.$auth("/article/index.status"))return!1;this.isLoading=!0,o["h"]({articleIds:t,state:a}).then((function(t){e.$message.success(t.message,1.5),e.handleRefresh()})).finally((function(t){e.isLoading=!1}))},getCategoryList:function(){var t=this;this.isLoading=!0,s["d"]().then((function(e){t.categoryList=e.data.list})).finally((function(e){t.isLoading=!1}))},handleSearch:function(t){var e=this;t.preventDefault(),this.searchForm.validateFields((function(t,a){t||(e.queryParam=Object(n["a"])(Object(n["a"])({},e.queryParam),a),e.handleRefresh(!0))}))},handleDelete:function(t){var e=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return o["d"]({articleId:t.article_id}).then((function(t){e.$message.success(t.message,1.5),e.handleRefresh()})).finally((function(t){a.destroy()}))}})},handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(t){this.$refs.EditForm.edit(t.id)},handleRefresh:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(t)}}},f=m,p=(a("f463"),a("2877")),h=Object(p["a"])(f,r,i,!1,null,"4e1fae7b",null);e["default"]=h.exports},8104:function(t,e,a){"use strict";var r=function(){var t=this,e=t._self._c;return e("a-modal",{attrs:{title:t.title,width:1180,visible:t.visible,confirmLoading:t.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:t.handleSubmit,cancel:t.handleCancel}},[e("a-spin",{attrs:{spinning:t.confirmLoading}},[e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"goods-list"},[e("a-table",{attrs:{rowKey:"order_goods_id",columns:t.columns,dataSource:t.record.list,pagination:!1},scopedSlots:t._u([{key:"title",fn:function(a,r){return[e("a-popover",{scopedSlots:t._u([{key:"content",fn:function(){return[e("p",[t._v(t._s(r.article.title))])]},proxy:!0}],null,!0)},[t._v(" "+t._s(r.article.title)+" ")])]}},{key:"goods_no",fn:function(a){return e("span",{},[t._v(t._s(a||"--"))])}},{key:"total_num",fn:function(a){return e("span",{},[t._v("x"+t._s(a))])}},{key:"total_price",fn:function(a){return e("span",{},[t._v("￥"+t._s(a))])}}])})],1)])],1)],1)},i=[],n=(a("a434"),a("d3b7"),a("88bc"),a("b63a")),o=a("2af9"),s=[{title:"题目",align:"center",dataIndex:"title",width:500,ellipsis:!0,scopedSlots:{customRender:"title"}},{title:"正确答案",align:"center",scopedSlots:{customRender:"unit_price"}},{title:"答题内容",align:"center",scopedSlots:{customRender:"unit_price"}},{title:"正确与否",align:"center",scopedSlots:{customRender:"unit_price"}}],l={components:{SelectImage:o["c"],Ueditor:o["d"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{optionsarr:[],option:[],isright:[],title:"答题详情",labelCol:{span:7},wrapperCol:{span:13},formItemLayoutWithOutLabel:{wrapperCol:{xs:{span:15,offset:7},sm:{span:15,offset:7}}},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),articleId:null,record:{},columns:s}},methods:{remove:function(t){1!==this.optionsarr.length&&this.optionsarr.splice(t,1)},addformitem:function(){this.optionsarr.push({})},edit:function(t){this.visible=!0,this.logId=t,this.getDetail()},getDetail:function(){var t=this;this.confirmLoading=!0,n["b"]({logId:this.logId}).then((function(e){t.record=e.data})).finally((function(e){t.confirmLoading=!1}))},handleSubmit:function(t){var e=this;t.preventDefault();var a=this.form.validateFields;a((function(t,a){!t&&e.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.record={},this.form.resetFields()},onFormSubmit:function(t){var e=this;this.confirmLoading=!0,t.option=this.option,t.isright=this.isright,n["f"]({articleId:this.articleId,form:t}).then((function(a){e.$message.success(a.message,1.5),e.handleCancel(),e.$emit("handleSubmit",t)})).finally((function(t){e.confirmLoading=!1}))}}},c=l,u=a("2877"),d=Object(u["a"])(c,r,i,!1,null,null,null);e["a"]=d.exports},8320:function(t,e,a){"use strict";a("b0c0");var r=function(){var t=this,e=t._self._c;return e("a-modal",{attrs:{title:t.title,width:780,visible:t.visible,confirmLoading:t.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:t.handleSubmit,cancel:t.handleCancel}},[e("a-spin",{attrs:{spinning:t.confirmLoading}},[e("a-form",{attrs:{form:t.form}},[e("a-form-item",{attrs:{label:"题目标题",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["title",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['title', {rules: [{required: true, min: 2, message: '请输入至少2个字符'}]}]"}],attrs:{autoSize:{minRows:4,maxRows:6}}})],1),e("a-form-item",{attrs:{label:"题目分类",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["category_id",{rules:[{required:!0,message:"请选择1个题目分类"}]}],expression:"['category_id', {rules: [{required: true, message: '请选择1个题目分类'}]}]"}]},t._l(t.categoryList,(function(a,r){return e("a-select-option",{key:r,attrs:{value:a.category_id}},[t._v(t._s(a.name))])})),1)],1),e("a-form-item",{attrs:{label:"题目类型",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["show_type",{initialValue:10,rules:[{required:!0}]}],expression:"['show_type', {initialValue: 10, rules: [{required: true}]}]"}]},[e("a-radio",{attrs:{value:10}},[t._v("单选题")]),e("a-radio",{attrs:{value:20}},[t._v("多选题")])],1)],1),e("a-form-item",{attrs:{label:"状态",labelCol:t.labelCol,wrapperCol:t.wrapperCol,extra:"用户端是否展示"}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', {initialValue: 1, rules: [{required: true}]}]"}]},[e("a-radio",{attrs:{value:1}},[t._v("显示")]),e("a-radio",{attrs:{value:0}},[t._v("隐藏")])],1)],1),t._l(t.optionsarr,(function(a,r){return e("a-form-item",{key:r+1,attrs:{label:0===r?"选项1":"选项"+(r+1),required:!1,labelCol:t.labelCol,wrapperCol:t.wrapperCol,extra:""}},[e("a-input",{attrs:{min:0},model:{value:t.option[r],callback:function(e){t.$set(t.option,r,e)},expression:"option[ind]"}}),e("a-checkbox",{attrs:{value:1},model:{value:t.isright[r],callback:function(e){t.$set(t.isright,r,e)},expression:"isright[ind]"}},[t._v("是正确答案 ")]),e("a-icon",{staticClass:"dynamic-delete-button",attrs:{type:"minus-circle-o"},on:{click:function(e){return t.remove(r)}}})],1)})),e("a-form-item",t._b({},"a-form-item",t.formItemLayoutWithOutLabel,!1),[e("a-button",{staticStyle:{width:"60%"},attrs:{type:"dashed"},on:{click:function(e){return t.addformitem()}}},[e("a-icon",{attrs:{type:"plus"}}),t._v(" 添加一个选项 ")],1)],1)],2)],1)],1)},i=[],n=(a("a434"),a("d3b7"),a("88bc"),a("b63a")),o=a("2af9"),s={components:{SelectImage:o["c"],Ueditor:o["d"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{optionsarr:[],option:[],isright:[],title:"新增题目",labelCol:{span:7},wrapperCol:{span:13},formItemLayoutWithOutLabel:{wrapperCol:{xs:{span:15,offset:7},sm:{span:15,offset:7}}},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{remove:function(t){1!==this.optionsarr.length&&this.optionsarr.splice(t,1)},addformitem:function(){this.optionsarr.push({})},add:function(){this.visible=!0},handleSubmit:function(t){var e=this;t.preventDefault();var a=this.form.validateFields;a((function(t,a){!t&&e.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(t){var e=this;this.confirmLoading=!0,t.option=this.option,t.isright=this.isright,n["a"]({form:t}).then((function(a){e.$message.success(a.message,1.5),e.handleCancel(),e.$emit("handleSubmit",t)})).finally((function(t){e.confirmLoading=!1}))}}},l=s,c=a("2877"),u=Object(c["a"])(l,r,i,!1,null,null,null);e["a"]=u.exports},"88bc":function(t,e,a){(function(e){var a=1/0,r=9007199254740991,i="[object Arguments]",n="[object Function]",o="[object GeneratorFunction]",s="[object Symbol]",l="object"==typeof e&&e&&e.Object===Object&&e,c="object"==typeof self&&self&&self.Object===Object&&self,u=l||c||Function("return this")();function d(t,e,a){switch(a.length){case 0:return t.call(e);case 1:return t.call(e,a[0]);case 2:return t.call(e,a[0],a[1]);case 3:return t.call(e,a[0],a[1],a[2])}return t.apply(e,a)}function m(t,e){var a=-1,r=t?t.length:0,i=Array(r);while(++a<r)i[a]=e(t[a],a,t);return i}function f(t,e){var a=-1,r=e.length,i=t.length;while(++a<r)t[i+a]=e[a];return t}var p=Object.prototype,h=p.hasOwnProperty,v=p.toString,b=u.Symbol,g=p.propertyIsEnumerable,y=b?b.isConcatSpreadable:void 0,_=Math.max;function C(t,e,a,r,i){var n=-1,o=t.length;a||(a=S),i||(i=[]);while(++n<o){var s=t[n];e>0&&a(s)?e>1?C(s,e-1,a,r,i):f(i,s):r||(i[i.length]=s)}return i}function w(t,e){return t=Object(t),x(t,e,(function(e,a){return a in t}))}function x(t,e,a){var r=-1,i=e.length,n={};while(++r<i){var o=e[r],s=t[o];a(s,o)&&(n[o]=s)}return n}function L(t,e){return e=_(void 0===e?t.length-1:e,0),function(){var a=arguments,r=-1,i=_(a.length-e,0),n=Array(i);while(++r<i)n[r]=a[e+r];r=-1;var o=Array(e+1);while(++r<e)o[r]=a[r];return o[e]=n,d(t,this,o)}}function S(t){return I(t)||F(t)||!!(y&&t&&t[y])}function k(t){if("string"==typeof t||V(t))return t;var e=t+"";return"0"==e&&1/t==-a?"-0":e}function F(t){return q(t)&&h.call(t,"callee")&&(!g.call(t,"callee")||v.call(t)==i)}var I=Array.isArray;function $(t){return null!=t&&j(t.length)&&!O(t)}function q(t){return D(t)&&$(t)}function O(t){var e=R(t)?v.call(t):"";return e==n||e==o}function j(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=r}function R(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function D(t){return!!t&&"object"==typeof t}function V(t){return"symbol"==typeof t||D(t)&&v.call(t)==s}var A=L((function(t,e){return null==t?{}:w(t,m(C(e,1),k))}));t.exports=A}).call(this,a("c8ba"))},"89a2":function(t,e,a){"use strict";a.d(e,"d",(function(){return n})),a.d(e,"a",(function(){return o})),a.d(e,"c",(function(){return s})),a.d(e,"b",(function(){return l}));var r=a("b775"),i={list:"/content.article.category/list",add:"/content.article.category/add",edit:"/content.article.category/edit",delete:"/content.article.category/delete"};function n(t){return Object(r["b"])({url:i.list,method:"get",params:t})}function o(t){return Object(r["b"])({url:i.add,method:"post",data:t})}function s(t){return Object(r["b"])({url:i.edit,method:"post",data:t})}function l(t){return Object(r["b"])({url:i.delete,method:"post",data:t})}},"98bf":function(t,e,a){"use strict";a.r(e);a("b0c0");var r=function(){var t=this,e=t._self._c;return e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"card-title"},[t._v(t._s(t.$route.meta.title))]),e("div",{staticClass:"table-operator"},[e("a-row",{staticClass:"row-item-search"},[e("a-form",{staticClass:"search-form",attrs:{form:t.searchForm,layout:"inline"},on:{submit:t.handleSearch}},[e("a-form-item",{attrs:{label:"题目标题"}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["title"],expression:"['title']"}],attrs:{placeholder:"请输入题目标题"}})],1),e("a-form-item",{attrs:{label:"题目分类"}},[e("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["categoryId",{initialValue:-1}],expression:"['categoryId', { initialValue: -1 }]"}]},[e("a-select-option",{attrs:{value:-1}},[t._v("全部")]),t._l(t.categoryList,(function(a,r){return e("a-select-option",{key:r,attrs:{value:a.category_id}},[t._v(t._s(a.name))])}))],2)],1),e("a-form-item",{attrs:{label:"状态"}},[e("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:-1}],expression:"['status', { initialValue: -1 }]"}]},[e("a-select-option",{attrs:{value:-1}},[t._v("全部")]),e("a-select-option",{attrs:{value:1}},[t._v("显示")]),e("a-select-option",{attrs:{value:0}},[t._v("隐藏")])],1)],1),e("a-form-item",{staticClass:"search-btn"},[e("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[t._v("搜索")])],1)],1)],1),e("div",{staticClass:"row-item-tab clearfix"},[e("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:t.handleAdd}},[t._v("新增")])],1)],1),e("s-table",{ref:"table",attrs:{rowKey:"article_id",loading:t.isLoading,columns:t.columns,data:t.loadData,pageSize:15,scroll:{x:1450}},scopedSlots:t._u([{key:"image_url",fn:function(t){return e("span",{},[e("a",{attrs:{title:"点击查看原图",href:t,target:"_blank"}},[e("img",{attrs:{height:"50",src:t,alt:"封面图"}})])])}},{key:"stitle",fn:function(a,r){return[e("a-popover",{scopedSlots:t._u([{key:"content",fn:function(){return[e("p",[t._v(t._s(a))])]},proxy:!0}],null,!0)},[t._v(" "+t._s(a)+" ")])]}},{key:"category",fn:function(a){return e("span",{},[t._v(t._s(a.name))])}},{key:"status",fn:function(a,r){return e("span",{},[e("a-tag",{staticClass:"cur-p",attrs:{color:1==a?"green":""},on:{click:function(e){return t.handleUpdateStatus([r.article_id],1!=a)}}},[t._v(t._s(1==a?"显示":"隐藏"))])],1)}},{key:"action",fn:function(a,r){return e("span",{},[e("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(e){return t.handleEdit(r)}}},[t._v("编辑")]),e("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(e){return t.handleDelete(r)}}},[t._v("删除")])])}}])}),e("AddForm",{ref:"AddForm",attrs:{categoryList:t.categoryList},on:{handleSubmit:t.handleRefresh}}),e("EditForm",{ref:"EditForm",attrs:{categoryList:t.categoryList},on:{handleSubmit:t.handleRefresh}})],1)},i=[],n=a("5530"),o=(a("d3b7"),a("b63a")),s=a("89a2"),l=a("2af9"),c=function(){var t=this,e=t._self._c;return e("a-modal",{attrs:{title:t.title,width:1180,visible:t.visible,confirmLoading:t.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:t.handleSubmit,cancel:t.handleCancel}},[e("a-spin",{attrs:{spinning:t.confirmLoading}},[e("a-form",{attrs:{form:t.form}},[e("a-form-item",{attrs:{label:"题目标题",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["title",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['title', {rules: [{required: true, min: 2, message: '请输入至少2个字符'}]}]"}],attrs:{autoSize:{minRows:4,maxRows:6}}})],1),e("a-form-item",{attrs:{label:"题目分类",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["category_id",{rules:[{required:!0,message:"请选择1个题目分类"}]}],expression:"['category_id', {rules: [{required: true, message: '请选择1个题目分类'}]}]"}]},t._l(t.categoryList,(function(a,r){return e("a-select-option",{key:r,attrs:{value:a.category_id}},[t._v(t._s(a.name))])})),1)],1),e("a-form-item",{attrs:{label:"题目类型",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["show_type",{initialValue:10,rules:[{required:!0}]}],expression:"['show_type', {initialValue: 10, rules: [{required: true}]}]"}]},[e("a-radio",{attrs:{value:10}},[t._v("单选题")]),e("a-radio",{attrs:{value:20}},[t._v("多选题")]),e("a-radio",{attrs:{value:30}},[t._v("判断题")])],1)],1),t._l(t.optionsarr,(function(a,r){return e("a-form-item",{key:r+1,attrs:{label:0===r?"选项1":"选项"+(r+1),required:!1,labelCol:t.labelCol,wrapperCol:t.wrapperCol,extra:""}},[e("a-input",{attrs:{min:0},model:{value:t.option[r],callback:function(e){t.$set(t.option,r,e)},expression:"option[ind]"}}),e("a-checkbox",{attrs:{value:1},model:{value:t.isright[r],callback:function(e){t.$set(t.isright,r,e)},expression:"isright[ind]"}},[t._v("是正确答案 ")]),e("a-icon",{staticClass:"dynamic-delete-button",attrs:{type:"minus-circle-o"},on:{click:function(e){return t.remove(r)}}})],1)})),e("a-form-item",t._b({},"a-form-item",t.formItemLayoutWithOutLabel,!1),[e("a-button",{staticStyle:{width:"60%"},attrs:{type:"dashed"},on:{click:function(e){return t.addformitem()}}},[e("a-icon",{attrs:{type:"plus"}}),t._v(" 添加一个选项 ")],1)],1)],2)],1)],1)},u=[],d=(a("b1f8"),a("a434"),a("159b"),a("88bc")),m=a.n(d),f={components:{SelectImage:l["c"],Ueditor:l["d"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{optionsarr:[],option:[],isright:[],title:"编辑题目",labelCol:{span:7},wrapperCol:{span:13},formItemLayoutWithOutLabel:{wrapperCol:{xs:{span:15,offset:7},sm:{span:15,offset:7}}},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),articleId:null,record:{}}},methods:{remove:function(t){1!==this.optionsarr.length&&(this.optionsarr.splice(t,1),this.option.splice(t,1),this.isright.splice(t,1))},addformitem:function(){this.optionsarr.push({})},edit:function(t){this.visible=!0,this.articleId=t,this.getDetail()},getDetail:function(){var t=this;this.confirmLoading=!0,o["e"]({articleId:this.articleId}).then((function(e){t.record=e.data.detail,t.optionsarr=e.data.detail.option;var a=[];t.optionsarr.forEach((function(t){a.push(t.content)})),t.option=a,t.isright=e.data.detail.isright,t.setFieldsValue()})).finally((function(e){t.confirmLoading=!1}))},setFieldsValue:function(){var t=this,e=this.form.setFieldsValue;this.$nextTick((function(){e(m()(t.record,["title","option","answer","show_type","category_id","image_id","sort","status","virtual_views"]))}))},handleSubmit:function(t){var e=this;t.preventDefault();var a=this.form.validateFields;a((function(t,a){!t&&e.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.record={},this.form.resetFields()},onFormSubmit:function(t){var e=this;this.confirmLoading=!0,t.option=this.option,t.isright=this.isright,o["f"]({articleId:this.articleId,form:t}).then((function(t){e.$message.success(t.message,1.5),e.handleCancel()})).finally((function(t){e.confirmLoading=!1}))}}},p=f,h=a("2877"),v=Object(h["a"])(p,c,u,!1,null,null,null),b=v.exports,g=function(){var t=this,e=t._self._c;return e("a-modal",{attrs:{title:t.title,width:780,visible:t.visible,confirmLoading:t.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:t.handleSubmit,cancel:t.handleCancel}},[e("a-spin",{attrs:{spinning:t.confirmLoading}},[e("a-form",{attrs:{form:t.form}},[e("a-form-item",{attrs:{label:"题目标题",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["title",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['title', {rules: [{required: true, min: 2, message: '请输入至少2个字符'}]}]"}],attrs:{autoSize:{minRows:4,maxRows:6}}})],1),e("a-form-item",{attrs:{label:"题目分类",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["category_id",{rules:[{required:!0,message:"请选择1个题目分类"}]}],expression:"['category_id', {rules: [{required: true, message: '请选择1个题目分类'}]}]"}]},t._l(t.categoryList,(function(a,r){return e("a-select-option",{key:r,attrs:{value:a.category_id}},[t._v(t._s(a.name))])})),1)],1),e("a-form-item",{attrs:{label:"题目类型",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["show_type",{initialValue:10,rules:[{required:!0}]}],expression:"['show_type', {initialValue: 10, rules: [{required: true}]}]"}]},[e("a-radio",{attrs:{value:10}},[t._v("单选题")]),e("a-radio",{attrs:{value:20}},[t._v("多选题")]),e("a-radio",{attrs:{value:30}},[t._v("判断题")])],1)],1),e("a-form-item",{attrs:{label:"状态",labelCol:t.labelCol,wrapperCol:t.wrapperCol,extra:"用户端是否展示"}},[e("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', {initialValue: 1, rules: [{required: true}]}]"}]},[e("a-radio",{attrs:{value:1}},[t._v("显示")]),e("a-radio",{attrs:{value:0}},[t._v("隐藏")])],1)],1),t._l(t.optionsarr,(function(a,r){return e("a-form-item",{key:r+1,attrs:{label:0===r?"选项1":"选项"+(r+1),required:!1,labelCol:t.labelCol,wrapperCol:t.wrapperCol,extra:""}},[e("a-input",{attrs:{min:0},model:{value:t.option[r],callback:function(e){t.$set(t.option,r,e)},expression:"option[ind]"}}),e("a-checkbox",{attrs:{value:1},model:{value:t.isright[r],callback:function(e){t.$set(t.isright,r,e)},expression:"isright[ind]"}},[t._v("是正确答案 ")]),e("a-icon",{staticClass:"dynamic-delete-button",attrs:{type:"minus-circle-o"},on:{click:function(e){return t.remove(r)}}})],1)})),e("a-form-item",t._b({},"a-form-item",t.formItemLayoutWithOutLabel,!1),[e("a-button",{staticStyle:{width:"60%"},attrs:{type:"dashed"},on:{click:function(e){return t.addformitem()}}},[e("a-icon",{attrs:{type:"plus"}}),t._v(" 添加一个选项 ")],1)],1)],2)],1)],1)},y=[],_={components:{SelectImage:l["c"],Ueditor:l["d"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{optionsarr:[],option:[],isright:[],title:"新增题目",labelCol:{span:7},wrapperCol:{span:13},formItemLayoutWithOutLabel:{wrapperCol:{xs:{span:15,offset:7},sm:{span:15,offset:7}}},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{remove:function(t){1!==this.optionsarr.length&&(this.optionsarr.splice(t,1),this.option.splice(t,1),this.isright.splice(t,1))},addformitem:function(){this.optionsarr.push({})},add:function(){this.visible=!0,this.optionsarr=[],this.option=[],this.isright=[]},handleSubmit:function(t){var e=this;t.preventDefault();var a=this.form.validateFields;a((function(t,a){!t&&e.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(t){var e=this;this.confirmLoading=!0,t.option=this.option,t.isright=this.isright,o["a"]({form:t}).then((function(a){e.$message.success(a.message,1.5),e.handleCancel(),e.$emit("handleSubmit",t)})).finally((function(t){e.confirmLoading=!1}))}}},C=_,w=Object(h["a"])(C,g,y,!1,null,null,null),x=w.exports,L=[{title:"ID",dataIndex:"article_id"},{title:"题目",dataIndex:"title",width:500,ellipsis:!0,scopedSlots:{customRender:"stitle"}},{title:"所属分类",dataIndex:"category",scopedSlots:{customRender:"category"}},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"创建时间",width:"180px",dataIndex:"create_time"},{title:"更新时间",width:"180px",dataIndex:"update_time"},{title:"操作",dataIndex:"action",width:"150px",fixed:"right",scopedSlots:{customRender:"action"}}],S={name:"Index",components:{ContentHeader:l["a"],STable:l["b"],AddForm:x,EditForm:b},data:function(){var t=this;return{expand:!1,searchForm:this.$form.createForm(this),categoryList:[],queryParam:{},isLoading:!1,columns:L,loadData:function(e){return o["g"](Object(n["a"])(Object(n["a"])({},e),t.queryParam)).then((function(t){return t.data.list}))}}},created:function(){this.getCategoryList()},methods:{handleUpdateStatus:function(t){var e=this,a=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(!this.$auth("/article/index.status"))return!1;this.isLoading=!0,o["h"]({articleIds:t,state:a}).then((function(t){e.$message.success(t.message,1.5),e.handleRefresh()})).finally((function(t){e.isLoading=!1}))},getCategoryList:function(){var t=this;this.isLoading=!0,s["d"]().then((function(e){t.categoryList=e.data.list})).finally((function(e){t.isLoading=!1}))},handleSearch:function(t){var e=this;t.preventDefault(),this.searchForm.validateFields((function(t,a){t||(e.queryParam=Object(n["a"])(Object(n["a"])({},e.queryParam),a),e.handleRefresh(!0))}))},handleDelete:function(t){var e=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return o["d"]({articleId:t.article_id}).then((function(t){e.$message.success(t.message,1.5),e.handleRefresh()})).finally((function(t){a.destroy()}))}})},handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(t){this.$refs.EditForm.edit(t.article_id)},handleRefresh:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(t)}}},k=S,F=(a("4e7e"),Object(h["a"])(k,r,i,!1,null,"41d1ef84",null));e["default"]=F.exports},b63a:function(t,e,a){"use strict";a.d(e,"h",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"g",(function(){return s})),a.d(e,"b",(function(){return l})),a.d(e,"e",(function(){return c})),a.d(e,"a",(function(){return u})),a.d(e,"f",(function(){return d})),a.d(e,"d",(function(){return m}));var r=a("b775"),i={list:"/content.article/list",datiList:"/content.article/datiList",detail:"/content.article/detail",datiDetail:"/content.article/datiDetail",add:"/content.article/add",edit:"/content.article/edit",delete:"/content.article/delete",state:"/content.article/state"};function n(t){return Object(r["b"])({url:i.state,method:"post",data:t})}function o(t){return Object(r["b"])({url:i.datiList,method:"get",params:t})}function s(t){return Object(r["b"])({url:i.list,method:"get",params:t})}function l(t){return Object(r["b"])({url:i.datiDetail,method:"get",params:t})}function c(t){return Object(r["b"])({url:i.detail,method:"get",params:t})}function u(t){return Object(r["b"])({url:i.add,method:"post",data:t})}function d(t){return Object(r["b"])({url:i.edit,method:"post",data:t})}function m(t){return Object(r["b"])({url:i.delete,method:"post",data:t})}},f463:function(t,e,a){"use strict";a("2b20")}}]);