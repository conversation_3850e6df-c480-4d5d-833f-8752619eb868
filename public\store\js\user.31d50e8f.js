(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["user"],{"139d":function(e,a,t){},"1f44":function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-row",{staticClass:"row-item-search"},[a("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[a("a-form-item",{attrs:{label:"关键字"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["title"],expression:"['title']"}],attrs:{placeholder:"请输入姓名/手机号"}})],1),a("a-form-item",{staticClass:"search-btn"},[a("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1)],1),a("s-table",{ref:"table",attrs:{rowKey:"id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15,scroll:{x:1400}},scopedSlots:e._u([{key:"image_url",fn:function(e){return a("span",{},[a("a",{attrs:{title:"点击查看原图",href:e,target:"_blank"}},[a("img",{attrs:{height:"50",src:e,alt:"封面图"}})])])}},{key:"stitle",fn:function(t){return a("span",{},[a("p",{staticClass:"twoline-hide",staticStyle:{width:"270px"}},[e._v(e._s(t))])])}},{key:"category",fn:function(t){return a("span",{},[e._v(e._s(t.name))])}},{key:"status",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:t?"green":""}},[e._v(e._s(t?"显示":"隐藏"))])],1)}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),a("AddForm",{ref:"AddForm",attrs:{categoryList:e.categoryList},on:{handleSubmit:e.handleRefresh}}),a("EditForm",{ref:"EditForm",attrs:{categoryList:e.categoryList},on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=t("5530"),l=(t("d3b7"),t("b775")),s={list:"/xj.invite/list",detail:"/xj.invite/detail",add:"/xj.invite/add",edit:"/xj.invite/edit",delete:"/xj.invite/delete"};function n(e){return Object(l["b"])({url:s.list,method:"get",params:e})}function d(e){return Object(l["b"])({url:s.delete,method:"post",data:e})}var u=t("89a2"),c=t("2af9"),m=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:780,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"文章标题",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["title",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['title', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),a("a-form-item",{attrs:{label:"文章分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["category_id",{rules:[{required:!0,message:"请选择1个商品分类"}]}],expression:"['category_id', { rules: [{ required: true, message: '请选择1个商品分类' }] }]"}]},e._l(e.categoryList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.category_id}},[e._v(e._s(t.name))])})),1)],1),a("a-form-item",{attrs:{label:"列表显示方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["show_type",{initialValue:10,rules:[{required:!0}]}],expression:"['show_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:10}},[e._v("小图模式")]),a("a-radio",{attrs:{value:20}},[e._v("大图模式")])],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("小图模式建议封面图尺寸：300 * 188")]),a("p",{staticClass:"extra"},[e._v("大图模式建议封面图尺寸：750 * 455")])])],1),a("a-form-item",{attrs:{label:"封面图",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["image_id",{rules:[{required:!0,message:"请选择1个封面图"}]}],expression:"['image_id', { rules: [{ required: true, message: '请选择1个封面图' }] }]"}],attrs:{defaultList:e.record.image?[e.record.image]:[]}})],1),a("a-form-item",{attrs:{label:"文章内容",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("Ueditor",{directives:[{name:"decorator",rawName:"v-decorator",value:["content",{rules:[{required:!0,message:"文章内容不能为空"}]}],expression:"['content', { rules: [{ required: true, message: '文章内容不能为空' }] }]"}]})],1),a("a-form-item",{attrs:{label:"虚拟阅读量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户看到的阅读量 = 实际阅读量 + 虚拟阅读量"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["virtual_views",{initialValue:100}],expression:"['virtual_views', { initialValue: 100 }]"}],attrs:{min:0}})],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("显示")]),a("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},p=[],f=t("88bc"),v=t.n(f),b=t("b63a"),h={components:{SelectImage:c["h"],Ueditor:c["n"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{title:"编辑文章",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),articleId:null,record:{}}},methods:{edit:function(e){this.visible=!0,this.articleId=e,this.getDetail()},getDetail:function(){var e=this;this.confirmLoading=!0,b["c"]({articleId:this.articleId}).then((function(a){e.record=a.data.detail,e.setFieldsValue()})).finally((function(a){e.confirmLoading=!1}))},setFieldsValue:function(){var e=this,a=this.form.setFieldsValue;this.$nextTick((function(){a(v()(e.record,["title","show_type","category_id","image_id","content","sort","status","virtual_views"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.record={},this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,b["d"]({articleId:this.articleId,form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(e){a.confirmLoading=!1}))}}},g=h,C=t("2877"),w=Object(C["a"])(g,m,p,!1,null,null,null),_=w.exports,y=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:780,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"文章标题",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["title",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['title', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),a("a-form-item",{attrs:{label:"文章分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["category_id",{rules:[{required:!0,message:"请选择1个商品分类"}]}],expression:"['category_id', { rules: [{ required: true, message: '请选择1个商品分类' }] }]"}]},e._l(e.categoryList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.category_id}},[e._v(e._s(t.name))])})),1)],1),a("a-form-item",{attrs:{label:"列表显示方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["show_type",{initialValue:10,rules:[{required:!0}]}],expression:"['show_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:10}},[e._v("小图模式")]),a("a-radio",{attrs:{value:20}},[e._v("大图模式")])],1),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("小图模式建议封面图尺寸：300 * 188")]),a("p",{staticClass:"extra"},[e._v("大图模式建议封面图尺寸：750 * 455")])])],1),a("a-form-item",{attrs:{label:"封面图",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["image_id",{rules:[{required:!0,message:"请选择1个封面图"}]}],expression:"['image_id', { rules: [{ required: true, message: '请选择1个封面图' }] }]"}]})],1),a("a-form-item",{attrs:{label:"文章内容",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("Ueditor",{directives:[{name:"decorator",rawName:"v-decorator",value:["content",{rules:[{required:!0,message:"文章内容不能为空"}]}],expression:"['content', { rules: [{ required: true, message: '文章内容不能为空' }] }]"}]})],1),a("a-form-item",{attrs:{label:"虚拟阅读量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户看到的阅读量 = 实际阅读量 + 虚拟阅读量"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["virtual_views",{initialValue:100}],expression:"['virtual_views', { initialValue: 100 }]"}],attrs:{min:0}})],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("显示")]),a("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},x=[],q={components:{SelectImage:c["h"],Ueditor:c["n"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{title:"新增文章",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{add:function(){this.visible=!0},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,b["a"]({form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(e){a.confirmLoading=!1}))}}},S=q,L=Object(C["a"])(S,y,x,!1,null,null,null),N=L.exports,F=[{title:"姓名",dataIndex:"name"},{title:"手机号",dataIndex:"phone"},{title:"备注",dataIndex:"remark"},{title:"创建时间",width:"180px",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"150px",fixed:"right",scopedSlots:{customRender:"action"}}],I={name:"Index",components:{ContentHeader:c["a"],STable:c["d"],AddForm:N,EditForm:_},data:function(){var e=this;return{expand:!1,searchForm:this.$form.createForm(this),categoryList:[],queryParam:{},isLoading:!1,columns:F,loadData:function(a){return n(Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){this.getCategoryList()},methods:{getCategoryList:function(){var e=this;this.isLoading=!0,u["d"]().then((function(a){e.categoryList=a.data.list})).finally((function(){return e.isLoading=!1}))},handleSearch:function(e){var a=this;e.preventDefault(),this.searchForm.validateFields((function(e,t){e||(a.queryParam=Object(o["a"])(Object(o["a"])({},a.queryParam),t),a.handleRefresh(!0))}))},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return d({articleId:e.id}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e.article_id)},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)}}},k=I,E=(t("8f8e"),Object(C["a"])(k,r,i,!1,null,"5840815e",null));a["default"]=E.exports},"2cd3":function(e,a,t){},"317b":function(e,a,t){},"33ce":function(e,a,t){},"5da5":function(e,a,t){},"5e8d":function(e,a,t){"use strict";t("139d")},"644d":function(e,a,t){"use strict";t("2cd3")},"648d":function(e,a,t){"use strict";t("5da5")},"6a54":function(e,a,t){"use strict";t.d(a,"d",(function(){return o})),t.d(a,"a",(function(){return l})),t.d(a,"c",(function(){return s})),t.d(a,"b",(function(){return n}));var r=t("b775"),i={list:"/xj.category/list",add:"/xj.category/add",edit:"/xj.category/edit",delete:"/xj.category/delete"};function o(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function l(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function s(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function n(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},7524:function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-row",{staticClass:"row-item-search"},[a("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[a("a-form-item",{attrs:{label:"视频标题"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["title"],expression:"['title']"}],attrs:{placeholder:"请输入视频标题"}})],1),a("a-form-item",{attrs:{label:"视频分类"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["categoryId",{initialValue:-1}],expression:"['categoryId', { initialValue: -1 }]"}]},[a("a-select-option",{attrs:{value:-1}},[e._v("全部")]),e._l(e.categoryList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.category_id}},[e._v(e._s(t.name))])}))],2)],1),a("a-form-item",{attrs:{label:"状态"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:-1}],expression:"['status', { initialValue: -1 }]"}]},[a("a-select-option",{attrs:{value:-1}},[e._v("全部")]),a("a-select-option",{attrs:{value:1}},[e._v("显示")]),a("a-select-option",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{staticClass:"search-btn"},[a("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1),a("div",{staticClass:"row-item-tab clearfix"},[a("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1)],1),a("s-table",{ref:"table",attrs:{rowKey:"article_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15,scroll:{x:1400}},scopedSlots:e._u([{key:"image_url",fn:function(e){return a("span",{},[a("a",{attrs:{title:"点击查看原图",href:e,target:"_blank"}},[a("img",{attrs:{height:"50",src:e,alt:"封面图"}})])])}},{key:"stitle",fn:function(t){return a("span",{},[a("p",{staticClass:"twoline-hide",staticStyle:{width:"270px"}},[e._v(e._s(t))])])}},{key:"category",fn:function(t){return a("span",{},[e._v(e._s(t.name))])}},{key:"status",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:t?"green":""}},[e._v(e._s(t?"显示":"隐藏"))])],1)}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(a){return e.handleEdit(r)}}},[e._v("编辑")]),a("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(a){return e.handleCopy(r)}}},[e._v("复制")]),a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),a("AddForm",{ref:"AddForm",attrs:{categoryList:e.categoryList},on:{handleSubmit:e.handleRefresh}}),a("EditForm",{ref:"EditForm",attrs:{categoryList:e.categoryList},on:{handleSubmit:e.handleRefresh}}),a("CopyForm",{ref:"CopyForm",attrs:{categoryList:e.categoryList},on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=t("5530"),l=(t("d3b7"),t("b775")),s={list:"/xj.video/list",detail:"/xj.video/detail",add:"/xj.video/add",edit:"/xj.video/edit",delete:"/xj.video/delete"};function n(e){return Object(l["b"])({url:s.list,method:"get",params:e})}function d(e){return Object(l["b"])({url:s.detail,method:"get",params:e})}function u(e){return Object(l["b"])({url:s.add,method:"post",data:e})}function c(e){return Object(l["b"])({url:s.edit,method:"post",data:e})}function m(e){return Object(l["b"])({url:s.delete,method:"post",data:e})}var p=t("6a54"),f=t("2af9"),v=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:780,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"视频标题",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["title",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['title', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),a("a-form-item",{attrs:{label:"视频分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["category_id",{rules:[{required:!0,message:"请选择1个视频分类"}]}],expression:"['category_id', { rules: [{ required: true, message: '请选择1个视频分类' }] }]"}]},e._l(e.categoryList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.category_id}},[e._v(e._s(t.name))])})),1)],1),a("a-form-item",{attrs:{label:"积分(等级1)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points1",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points1', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"积分(等级2)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points2",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points2', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"积分(等级3)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points3",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points3', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"积分(等级4)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points4",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points4', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"积分(等级5)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points5",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points5', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"封面图",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["image_id",{rules:[{required:!0,message:"请选择1个封面图"}]}],expression:"['image_id', { rules: [{ required: true, message: '请选择1个封面图' }] }]"}],attrs:{defaultList:e.record.image?[e.record.image]:[]}})],1),a("a-form-item",{attrs:{label:"视频链接",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_url",{rules:[{required:!0,message:"请输入"}]}],expression:"['video_url', { rules: [{ required: true,  message: '请输入' }] }]"}]})],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("显示")]),a("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},b=[],h=t("88bc"),g=t.n(h),C={components:{SelectImage:f["h"],SelectVideo:f["m"],Ueditor:f["n"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{title:"编辑视频",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),articleId:null,record:{}}},methods:{edit:function(e){this.visible=!0,this.articleId=e,this.getDetail()},getDetail:function(){var e=this;this.confirmLoading=!0,d({articleId:this.articleId}).then((function(a){e.record=a.data.detail,e.setFieldsValue()})).finally((function(a){e.confirmLoading=!1}))},setFieldsValue:function(){var e=this,a=this.form.setFieldsValue;this.$nextTick((function(){a(g()(e.record,["title","show_type","category_id","video_id","video_url","image_id","content","sort","status","virtual_views","points1","points2","points3","points4","points5"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.record={},this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,c({articleId:this.articleId,form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(e){a.confirmLoading=!1}))}}},w=C,_=t("2877"),y=Object(_["a"])(w,v,b,!1,null,null,null),x=y.exports,q=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:780,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"视频标题",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["title",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['title', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),a("a-form-item",{attrs:{label:"视频分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["category_id",{rules:[{required:!0,message:"请选择1个视频分类"}]}],expression:"['category_id', { rules: [{ required: true, message: '请选择1个视频分类' }] }]"}]},e._l(e.categoryList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.category_id}},[e._v(e._s(t.name))])})),1)],1),a("a-form-item",{attrs:{label:"积分(等级1)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points1",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points1', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"积分(等级2)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points2",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points2', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"积分(等级3)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points3",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points3', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"积分(等级4)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points4",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points4', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"积分(等级5)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points5",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points5', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"封面图",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["image_id",{rules:[{required:!0,message:"请选择1个封面图"}]}],expression:"['image_id', { rules: [{ required: true, message: '请选择1个封面图' }] }]"}],attrs:{defaultList:e.record.image?[e.record.image]:[]}})],1),a("a-form-item",{attrs:{label:"视频链接",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_url",{rules:[{required:!0,message:"请输入"}]}],expression:"['video_url', { rules: [{ required: true,  message: '请输入' }] }]"}]})],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("显示")]),a("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},S=[],L={components:{SelectImage:f["h"],SelectVideo:f["m"],Ueditor:f["n"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{title:"复制视频",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),articleId:null,record:{}}},methods:{edit:function(e){this.visible=!0,this.articleId=e,this.getDetail()},getDetail:function(){var e=this;this.confirmLoading=!0,d({articleId:this.articleId}).then((function(a){e.record=a.data.detail,e.setFieldsValue()})).finally((function(a){e.confirmLoading=!1}))},setFieldsValue:function(){var e=this,a=this.form.setFieldsValue;this.$nextTick((function(){a(g()(e.record,["title","show_type","category_id","video_id","video_url","image_id","content","sort","status","virtual_views","points1","points2","points3","points4","points5"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.record={},this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,u({form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(e){a.confirmLoading=!1}))}}},N=L,F=Object(_["a"])(N,q,S,!1,null,null,null),I=F.exports,k=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:780,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"视频标题",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["title",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['title', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),a("a-form-item",{attrs:{label:"视频分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["category_id",{rules:[{required:!0,message:"请选择1个视频分类"}]}],expression:"['category_id', { rules: [{ required: true, message: '请选择1个视频分类' }] }]"}]},e._l(e.categoryList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.category_id}},[e._v(e._s(t.name))])})),1)],1),a("a-form-item",{attrs:{label:"积分(等级1)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points1",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points1', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"积分(等级2)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points2",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points2', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"积分(等级3)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points3",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points3', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"积分(等级4)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points4",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points4', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"积分(等级5)",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户观看获得积分"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points2",{rules:[{required:!0,message:"请输入积分"}]}],expression:"['points2', { rules: [{ required: true,message: '请输入积分' }] }]"}],attrs:{type:"number"}})],1),a("a-form-item",{attrs:{label:"封面图",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["image_id",{rules:[{required:!0,message:"请选择1个封面图"}]}],expression:"['image_id', { rules: [{ required: true, message: '请选择1个封面图' }] }]"}]})],1),a("a-form-item",{attrs:{label:"视频链接",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["video_url",{rules:[{required:!0,message:"请输入"}]}],expression:"['video_url', { rules: [{ required: true,  message: '请输入' }] }]"}]})],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户端是否展示"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("显示")]),a("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入至少1个数字"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入至少1个数字' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},E=[],R={components:{SelectImage:f["h"],SelectVideo:f["m"],Ueditor:f["n"]},props:{categoryList:{type:Array,required:!0}},data:function(){return{title:"新增视频",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{add:function(){this.visible=!0},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,u({form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(e){a.confirmLoading=!1}))}}},j=R,O=Object(_["a"])(j,k,E,!1,null,null,null),A=O.exports,V=[{title:"ID",dataIndex:"id",width:"80px"},{title:"封面图",dataIndex:"image_url",width:"180px",scopedSlots:{customRender:"image_url"}},{title:"视频标题",dataIndex:"title",width:"180px",scopedSlots:{customRender:"stitle"}},{title:"视频分类",dataIndex:"category",width:"120px",scopedSlots:{customRender:"category"}},{title:"积分（1）",width:"80px",dataIndex:"points1"},{title:"积分（2）",width:"80px",dataIndex:"points2"},{title:"积分（3）",width:"80px",dataIndex:"points3"},{title:"积分（4）",width:"80px",dataIndex:"points4"},{title:"积分（5）",width:"80px",dataIndex:"points5"},{title:"状态",dataIndex:"status",width:"80px",scopedSlots:{customRender:"status"}},{title:"排序",width:"180px",dataIndex:"sort"},{title:"创建时间",width:"180px",dataIndex:"create_time"},{title:"更新时间",width:"180px",dataIndex:"update_time"},{title:"操作",dataIndex:"action",width:"150px",fixed:"right",scopedSlots:{customRender:"action"}}],$={name:"Index",components:{ContentHeader:f["a"],STable:f["d"],AddForm:A,EditForm:x,CopyForm:I},data:function(){var e=this;return{expand:!1,searchForm:this.$form.createForm(this),categoryList:[],queryParam:{},isLoading:!1,columns:V,loadData:function(a){return n(Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){this.getCategoryList()},methods:{getCategoryList:function(){var e=this;this.isLoading=!0,p["d"]().then((function(a){e.categoryList=a.data.list})).finally((function(){return e.isLoading=!1}))},handleSearch:function(e){var a=this;e.preventDefault(),this.searchForm.validateFields((function(e,t){e||(a.queryParam=Object(o["a"])(Object(o["a"])({},a.queryParam),t),a.handleRefresh(!0))}))},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return m({articleId:e.id}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e.id)},handleCopy:function(e){this.$refs.CopyForm.edit(e.id)},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)}}},P=$,D=(t("da30b"),Object(_["a"])(P,r,i,!1,null,"55b97380",null));a["default"]=D.exports},"7ad7":function(e,a,t){"use strict";t.r(a);for(var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("s-table",{ref:"table",attrs:{rowKey:"grade_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"upgrade",fn:function(t){return a("span",{},[e._v("消费满"+e._s(t.expend_money)+"元")])}},{key:"equity",fn:function(t){return a("span",{},[e._v(e._s(t.discount)+"折")])}},{key:"status",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:t?"green":""}},[e._v(e._s(t?"启用":"禁用"))])],1)}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(a){return e.handleEdit(r)}}},[e._v("编辑")]),a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),a("AddForm",{ref:"AddForm",on:{handleSubmit:e.handleRefresh}}),a("EditForm",{ref:"EditForm",on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=t("5530"),l=(t("d3b7"),t("2e1c")),s=t("2af9"),n=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"等级名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"例如：大众会员、黄金会员、铂金会员、钻石会员"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,message:"请输入等级名称"}]}],expression:"['name', { rules: [{ required: true, message: '请输入等级名称' }] }]"}]})],1),a("a-form-item",{attrs:{label:"等级权重",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"会员等级的权重，数字越大 等级越高"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["weight",{rules:[{required:!0,message:"请选择等级权重"}]}],expression:"['weight', { rules: [{ required: true, message: '请选择等级权重' }] }]"}]},e._l(e.weights,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t}},[e._v(e._s(t))])})),1)],1),a("a-form-item",{attrs:{label:"升级金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["upgrade.price",{rules:[{required:!0,message:"升级金额不能为空"}]}],expression:"['upgrade.price', { rules: [{ required: true, message: '升级金额不能为空' }] }]"}],attrs:{addonAfter:"元",inputProps:{min:.01}}})],1),a("a-form-item",{attrs:{label:"有效期",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["upgrade.day",{initialValue:365,rules:[{required:!0,message:"有效期不能为空"}]}],expression:"['upgrade.day', { initialValue: 365, rules: [{ required: true, message: '有效期不能为空' }] }]"}],attrs:{inputProps:{min:1}}})],1),a("a-form-item",{attrs:{label:"升级条件",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["upgrade.expend_money",{rules:[{required:!0,message:"升级条件不能为空"}]}],expression:"['upgrade.expend_money', { rules: [{ required: true, message: '升级条件不能为空' }] }]"}],attrs:{addonBefore:"实际消费金额满",addonAfter:"元",inputProps:{min:.01}}}),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("用户的实际消费金额满足后，自动升级")])])],1),a("a-form-item",{attrs:{label:"等级权益",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["equity.discount",{rules:[{required:!0,message:"等级权益不能为空"}]}],expression:"['equity.discount', { rules: [{ required: true, message: '等级权益不能为空' }] }]"}],attrs:{addonBefore:"折扣率",addonAfter:"折",inputProps:{min:0,max:9.9}}}),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("折扣率范围0.0-9.9，例如: 9.8代表98折，0代表不折扣")])])],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("启用")]),a("a-radio",{attrs:{value:0}},[e._v("禁用")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},d=[],u=[],c=1;c<=5;c++)u.push(c);for(var m={components:{InputNumberGroup:s["c"]},data:function(){return{title:"新增会员等级",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),weights:u}},created:function(){},methods:{add:function(){this.visible=!0},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,l["a"]({form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(){return a.confirmLoading=!1}))}}},p=m,f=t("2877"),v=Object(f["a"])(p,n,d,!1,null,null,null),b=v.exports,h=function(){var e=this,a=e._self._c;return a("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"等级名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"例如：大众会员、黄金会员、铂金会员、钻石会员"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,message:"请输入等级名称"}]}],expression:"['name', { rules: [{ required: true, message: '请输入等级名称' }] }]"}]})],1),a("a-form-item",{attrs:{label:"等级权重",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"会员等级的权重，数字越大 等级越高"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["weight",{rules:[{required:!0,message:"请选择等级权重"}]}],expression:"['weight', { rules: [{ required: true, message: '请选择等级权重' }] }]"}]},e._l(e.weights,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t}},[e._v(e._s(t))])})),1)],1),a("a-form-item",{attrs:{label:"升级金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["upgrade.price",{rules:[{required:!0,message:"升级金额不能为空"}]}],expression:"['upgrade.price', { rules: [{ required: true, message: '升级金额不能为空' }] }]"}],attrs:{addonAfter:"元",inputProps:{min:.01}}})],1),a("a-form-item",{attrs:{label:"有效期",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["upgrade.day",{initialValue:365,rules:[{required:!0,message:"有效期不能为空"}]}],expression:"['upgrade.day', { initialValue: 365, rules: [{ required: true, message: '有效期不能为空' }] }]"}],attrs:{inputProps:{min:1}}})],1),a("a-form-item",{attrs:{label:"升级条件",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["upgrade.expend_money",{rules:[{required:!0,message:"升级条件不能为空"}]}],expression:"['upgrade.expend_money', { rules: [{ required: true, message: '升级条件不能为空' }] }]"}],attrs:{addonBefore:"实际消费金额满",addonAfter:"元",inputProps:{min:.01}}}),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("用户的实际消费金额满足后，自动升级")])])],1),a("a-form-item",{attrs:{label:"等级权益",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["equity.discount",{rules:[{required:!0,message:"等级权益不能为空"}]}],expression:"['equity.discount', { rules: [{ required: true, message: '等级权益不能为空' }] }]"}],attrs:{addonBefore:"折扣率",addonAfter:"折",inputProps:{min:0,max:9.9}}}),a("div",{staticClass:"form-item-help"},[a("p",{staticClass:"extra"},[e._v("折扣率范围0.0-9.9，例如: 9.8代表98折，0代表不折扣")])])],1),a("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:1}},[e._v("启用")]),a("a-radio",{attrs:{value:0}},[e._v("禁用")])],1)],1),a("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},g=[],C=t("88bc"),w=t.n(C),_=[],y=1;y<=5;y++)_.push(y);var x={components:{InputNumberGroup:s["c"]},data:function(){return{title:"编辑会员等级",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),weights:_,record:{}}},created:function(){},methods:{edit:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this.record,a=this.form.setFieldsValue;e.cascader=[e.province_id,e.city_id,e.region_id],this.$nextTick((function(){a(w()(e,["name","weight","upgrade","equity","status","sort","day","price"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,l["d"]({gradeId:this.record.grade_id,form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(){return a.confirmLoading=!1}))}}},q=x,S=Object(f["a"])(q,h,g,!1,null,null,null),L=S.exports,N={name:"Index",components:{STable:s["d"],AddForm:b,EditForm:L},data:function(){var e=this;return{queryParam:{},isLoading:!1,columns:[{title:"等级ID",dataIndex:"grade_id"},{title:"等级名称",dataIndex:"name"},{title:"等级权重",dataIndex:"weight"},{title:"升级金额（元）",dataIndex:"upgrade.price"},{title:"有效期（天）",dataIndex:"upgrade.day"},{title:"等级权益",dataIndex:"equity",scopedSlots:{customRender:"equity"}},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(a){return l["e"](Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return l["c"]({gradeId:e.grade_id}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)}}},F=N,I=Object(f["a"])(F,r,i,!1,null,null,null);a["default"]=I.exports},"88bc":function(e,a,t){(function(a){var t=1/0,r=9007199254740991,i="[object Arguments]",o="[object Function]",l="[object GeneratorFunction]",s="[object Symbol]",n="object"==typeof a&&a&&a.Object===Object&&a,d="object"==typeof self&&self&&self.Object===Object&&self,u=n||d||Function("return this")();function c(e,a,t){switch(t.length){case 0:return e.call(a);case 1:return e.call(a,t[0]);case 2:return e.call(a,t[0],t[1]);case 3:return e.call(a,t[0],t[1],t[2])}return e.apply(a,t)}function m(e,a){var t=-1,r=e?e.length:0,i=Array(r);while(++t<r)i[t]=a(e[t],t,e);return i}function p(e,a){var t=-1,r=a.length,i=e.length;while(++t<r)e[i+t]=a[t];return e}var f=Object.prototype,v=f.hasOwnProperty,b=f.toString,h=u.Symbol,g=f.propertyIsEnumerable,C=h?h.isConcatSpreadable:void 0,w=Math.max;function _(e,a,t,r,i){var o=-1,l=e.length;t||(t=S),i||(i=[]);while(++o<l){var s=e[o];a>0&&t(s)?a>1?_(s,a-1,t,r,i):p(i,s):r||(i[i.length]=s)}return i}function y(e,a){return e=Object(e),x(e,a,(function(a,t){return t in e}))}function x(e,a,t){var r=-1,i=a.length,o={};while(++r<i){var l=a[r],s=e[l];t(s,l)&&(o[l]=s)}return o}function q(e,a){return a=w(void 0===a?e.length-1:a,0),function(){var t=arguments,r=-1,i=w(t.length-a,0),o=Array(i);while(++r<i)o[r]=t[a+r];r=-1;var l=Array(a+1);while(++r<a)l[r]=t[r];return l[a]=o,c(e,this,l)}}function S(e){return F(e)||N(e)||!!(C&&e&&e[C])}function L(e){if("string"==typeof e||A(e))return e;var a=e+"";return"0"==a&&1/e==-t?"-0":a}function N(e){return k(e)&&v.call(e,"callee")&&(!g.call(e,"callee")||b.call(e)==i)}var F=Array.isArray;function I(e){return null!=e&&R(e.length)&&!E(e)}function k(e){return O(e)&&I(e)}function E(e){var a=j(e)?b.call(e):"";return a==o||a==l}function R(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=r}function j(e){var a=typeof e;return!!e&&("object"==a||"function"==a)}function O(e){return!!e&&"object"==typeof e}function A(e){return"symbol"==typeof e||O(e)&&b.call(e)==s}var V=q((function(e,a){return null==e?{}:y(e,m(_(a,1),L))}));e.exports=V}).call(this,t("c8ba"))},"89a2":function(e,a,t){"use strict";t.d(a,"d",(function(){return o})),t.d(a,"a",(function(){return l})),t.d(a,"c",(function(){return s})),t.d(a,"b",(function(){return n}));var r=t("b775"),i={list:"/content.article.category/list",add:"/content.article.category/add",edit:"/content.article.category/edit",delete:"/content.article.category/delete"};function o(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function l(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function s(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function n(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},"8f8e":function(e,a,t){"use strict";t("a17d")},"930b":function(e,a,t){"use strict";var r=t("5c06");a["a"]=new r["a"]([{key:"PENDING",name:"待支付",value:10},{key:"SUCCESS",name:"支付成功",value:20}])},9653:function(e,a,t){"use strict";t("317b")},a17d:function(e,a,t){},b63a:function(e,a,t){"use strict";t.d(a,"e",(function(){return o})),t.d(a,"c",(function(){return l})),t.d(a,"a",(function(){return s})),t.d(a,"d",(function(){return n})),t.d(a,"b",(function(){return d}));var r=t("b775"),i={list:"/content.article/list",detail:"/content.article/detail",add:"/content.article/add",edit:"/content.article/edit",delete:"/content.article/delete"};function o(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function l(e){return Object(r["b"])({url:i.detail,method:"get",params:e})}function s(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function n(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function d(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},d3ae:function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-row",{staticClass:"row-item-search"},[a("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[a("a-form-item",{attrs:{label:"会员昵称"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["search"],expression:"['search']"}],attrs:{placeholder:"请输入会员昵称"}})],1),a("a-form-item",{attrs:{label:"付款时间"}},[a("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime"],expression:"['betweenTime']"}],attrs:{format:"YYYY-MM-DD"}})],1),a("a-form-item",{staticClass:"search-btn"},[a("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1)],1),a("s-table",{ref:"table",attrs:{rowKey:"order_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"user",fn:function(e){return a("span",{},[a("UserItem",{attrs:{user:e}})],1)}},{key:"order_plan",fn:function(t){return a("span",{},[t?a("a-tag",[e._v(e._s(t.plan_name))]):a("span",[e._v("--")])],1)}},{key:"recharge_type",fn:function(t){return a("span",{},[a("a-tag",[e._v(e._s(e.RechargeTypeEnum[t].name))])],1)}},{key:"pay_status",fn:function(t){return a("span",{},[a("a-tag",{attrs:{color:20==t?"green":""}},[e._v(e._s(20==t?"已支付":"待支付"))])],1)}}])})],1)},i=[],o=t("5530"),l=t("b775"),s={order:"/user.vip/order"};function n(e){return Object(l["b"])({url:s.order,method:"get",params:e})}var d=t("ab09"),u=t("930b"),c=t("da0c"),m={name:"Index",components:{STable:d["b"],UserItem:d["c"]},data:function(){var e=this;return{PayStatusEnum:u["a"],RechargeTypeEnum:c["a"],searchForm:this.$form.createForm(this),queryParam:{},isLoading:!1,columns:[{title:"ID",dataIndex:"order_id"},{title:"会员信息",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"支付金额",dataIndex:"pay_price"},{title:"付款时间",dataIndex:"pay_time"}],loadData:function(a){return n(Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},handleSearch:function(e){var a=this;e.preventDefault(),this.searchForm.validateFields((function(e,t){e||(a.queryParam=Object(o["a"])(Object(o["a"])({},a.queryParam),t),a.handleRefresh(!0))}))}}},p=m,f=(t("9653"),t("2877")),v=Object(f["a"])(p,r,i,!1,null,"818b9ee4",null);a["default"]=v.exports},da0c:function(e,a,t){"use strict";var r=t("5c06");a["a"]=new r["a"]([{key:"CUSTOM",name:"自定义金额",value:10},{key:"PLAN",name:"套餐充值",value:20}])},da30b:function(e,a,t){"use strict";t("33ce")},dab6:function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),a("div",{staticClass:"table-operator"},[a("a-row",{staticClass:"row-item-search"},[a("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[a("a-form-item",{attrs:{label:"昵称/手机号"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["search"],expression:"['search']"}],attrs:{placeholder:"请输入昵称/手机号"}})],1),e.$module("user-grade")?a("a-form-item",{attrs:{label:"会员等级"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["gradeId",{initialValue:0}],expression:"['gradeId', { initialValue: 0 }]"}]},[a("a-select-option",{attrs:{value:0}},[e._v("全部")]),e._l(e.gradeList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.grade_id}},[e._v(e._s(t.name))])}))],2)],1):e._e(),a("a-form-item",{attrs:{label:"注册时间"}},[a("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime"],expression:"['betweenTime']"}],attrs:{format:"YYYY-MM-DD"}})],1),a("a-form-item",{staticClass:"search-btn"},[a("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1)],1),a("s-table",{ref:"table",attrs:{rowKey:"user_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"avatar_url",fn:function(e){return a("span",{},[a("div",{staticClass:"avatar"},[a("img",e?{attrs:{width:"45",height:"45",src:e,alt:"用户头像"}}:{attrs:{width:"45",height:"45",src:t("889b"),alt:"用户头像"}})])])}},{key:"main_info",fn:function(t){return a("span",{},[a("p",[e._v(e._s(t.nick_name))]),a("p",{staticClass:"c-p"},[e._v(e._s(t.mobile))])])}},{key:"grade",fn:function(t,r){return a("span",{},[t?a("a-tag",[e._v(e._s(t.name))]):a("span",[e._v("--")]),r.vip_endtime>0?a("p",[e._v(e._s(r.end_time_text))]):e._e()],1)}},{key:"balance",fn:function(t,r){return a("span",{},[a("p",[a("span",[e._v("余额：")]),a("span",{staticClass:"c-p"},[e._v(e._s(t))])]),a("p",[a("span",[e._v("积分：")]),a("span",{staticClass:"c-p"},[e._v(e._s(r.points))])])])}},{key:"expend_money",fn:function(t){return a("span",{},[a("span",{staticClass:"c-p"},[e._v(e._s(t))])])}},{key:"platform",fn:function(e){return a("span",{staticClass:"platform"},[a("platform-icon",{attrs:{name:e,showTips:!0,iconSize:17}})],1)}},{key:"action",fn:function(t){return a("span",{staticClass:"actions"},[e.$module("market-recharge")?a("a",{directives:[{name:"action",rawName:"v-action:recharge",arg:"recharge"}],attrs:{title:"会员充值"},on:{click:function(a){return e.handleRecharge(t)}}},[e._v("充值")]):e._e(),e.$module("user-grade")?a("a",{directives:[{name:"action",rawName:"v-action:grade",arg:"grade"}],attrs:{title:"会员等级"},on:{click:function(a){return e.handleGrade(t)}}},[e._v("等级")]):e._e(),a("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(a){return e.handleDelete(t)}}},[e._v("删除")])])}}])}),a("GradeForm",{ref:"GradeForm",attrs:{gradeList:e.gradeList},on:{handleSubmit:e.handleRefresh}}),a("RechargeForm",{ref:"RechargeForm",on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=t("5530"),l=(t("d3b7"),t("fa04")),s=t("fab29"),n=t("2e1c"),d=t("2af9"),u=t("8d5f"),c=function(){var e=this,a=e._self._c;return a("a-modal",{staticClass:"noborder",attrs:{title:e.title,width:520,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"会员ID",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("span",[e._v(e._s(e.record.user_id))])]),a("a-form-item",{attrs:{label:"会员等级",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["grade_id",{rules:[{required:!0}]}],expression:"['grade_id', { rules: [{ required: true }] }]"}],attrs:{placeholder:"请选择会员等级"}},[a("a-select-option",{attrs:{value:0}},[e._v("无等级")]),e._l(e.gradeList,(function(t,r){return a("a-select-option",{key:r,attrs:{value:t.grade_id}},[e._v(e._s(t.name))])}))],2)],1),a("a-form-item",{attrs:{label:"有效期",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-date-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["end_time_text",{rules:[{required:!0}]}],expression:"['end_time_text', { rules: [{ required: true }] }]"}],attrs:{format:"YYYY-MM-DD"}})],1),a("a-form-item",{attrs:{label:"管理员备注",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["remark",{rules:[{required:!0,message:"请输入管理员备注"}]}],expression:"['remark', { rules: [{ required: true, message: '请输入管理员备注' }] }]"}],attrs:{placeholder:"请输入管理员备注",rows:3}})],1)],1)],1)],1)},m=[],p=t("88bc"),f=t.n(p),v={components:{},props:{gradeList:{type:Array,required:!0}},data:function(){return{title:"设置会员等级",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{handle:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this.record,a=this.form.setFieldsValue;this.$nextTick((function(){a(f()(e,["grade_id","end_time_text"]))}))},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this;this.confirmLoading=!0,s["b"]({userId:this.record.user_id,form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(){return a.confirmLoading=!1}))}}},b=v,h=(t("5e8d"),t("2877")),g=Object(h["a"])(b,c,m,!1,null,null,null),C=g.exports,w=function(){var e=this,a=e._self._c;return a("a-modal",{staticClass:"noborder",attrs:{title:e.title,width:520,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-tabs",{attrs:{activeKey:e.activeKey},on:{change:e.onChangeTabs}},[a("a-tab-pane",{key:e.RECHARGE_TYPE_BALANCE,attrs:{tab:"充值余额"}},[e.activeKey===e.RECHARGE_TYPE_BALANCE?[a("a-form-item",{staticClass:"mb-5",attrs:{label:"会员ID",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("span",[e._v(e._s(e.record.user_id))])]),a("a-form-item",{staticClass:"mb-5",attrs:{label:"当前余额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("span",{staticClass:"c-p"},[e._v(e._s(e.record.balance))])]),a("a-form-item",{attrs:{label:"充值方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["".concat(e.RECHARGE_TYPE_BALANCE,".mode"),{initialValue:"inc",rules:[{required:!0}]}],expression:"[`${RECHARGE_TYPE_BALANCE}.mode`, { initialValue: 'inc', rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:"inc"}},[e._v("增加")]),a("a-radio",{attrs:{value:"dec"}},[e._v("减少")]),a("a-radio",{attrs:{value:"final"}},[e._v("最终金额")])],1)],1),a("a-form-item",{attrs:{label:"变更金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["".concat(e.RECHARGE_TYPE_BALANCE,".money"),{initialValue:"",rules:[{required:!0,message:"请输入变更的金额"}]}],expression:"[`${RECHARGE_TYPE_BALANCE}.money`, { initialValue: '', rules: [{ required: true, message: '请输入变更的金额' }] }]"}],attrs:{min:.01}})],1),a("a-form-item",{attrs:{label:"管理员备注",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["".concat(e.RECHARGE_TYPE_BALANCE,".remark"),{rules:[{required:!0,message:"请输入管理员备注"}]}],expression:"[`${RECHARGE_TYPE_BALANCE}.remark`, { rules: [{ required: true, message: '请输入管理员备注' }] }]"}],attrs:{placeholder:"请输入管理员备注",rows:3}})],1)]:e._e()],2),a("a-tab-pane",{key:e.RECHARGE_TYPE_POINTS,attrs:{tab:"充值积分"}},[e.activeKey===e.RECHARGE_TYPE_POINTS?[a("a-form-item",{staticClass:"mb-5",attrs:{label:"会员ID",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("span",[e._v(e._s(e.record.user_id))])]),a("a-form-item",{staticClass:"mb-5",attrs:{label:"当前积分",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("span",{staticClass:"c-p"},[e._v(e._s(e.record.points))])]),a("a-form-item",{attrs:{label:"充值方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["".concat(e.RECHARGE_TYPE_POINTS,".mode"),{initialValue:"inc",rules:[{required:!0}]}],expression:"[`${RECHARGE_TYPE_POINTS}.mode`, { initialValue: 'inc', rules: [{ required: true }] }]"}]},[a("a-radio",{attrs:{value:"inc"}},[e._v("增加")]),a("a-radio",{attrs:{value:"dec"}},[e._v("减少")]),a("a-radio",{attrs:{value:"final"}},[e._v("最终积分")])],1)],1),a("a-form-item",{attrs:{label:"变更数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["".concat(e.RECHARGE_TYPE_POINTS,".value"),{initialValue:"",rules:[{required:!0,message:"请输入变更的金数量"}]}],expression:"[`${RECHARGE_TYPE_POINTS}.value`, { initialValue: '', rules: [{ required: true, message: '请输入变更的金数量' }] }]"}],attrs:{min:.01}})],1),a("a-form-item",{attrs:{label:"管理员备注",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["".concat(e.RECHARGE_TYPE_POINTS,".remark"),{rules:[{required:!0,message:"请输入管理员备注"}]}],expression:"[`${RECHARGE_TYPE_POINTS}.remark`, { rules: [{ required: true, message: '请输入管理员备注' }] }]"}],attrs:{placeholder:"请输入管理员备注",rows:3}})],1)]:e._e()],2)],1)],1)],1)],1)},_=[],y="balance",x="points",q={components:{},data:function(){return{title:"会员充值",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),activeKey:y,RECHARGE_TYPE_BALANCE:y,RECHARGE_TYPE_POINTS:x,record:{}}},methods:{handle:function(e){this.visible=!0,this.record=e},onChangeTabs:function(e){this.activeKey=e},handleSubmit:function(e){var a=this;e.preventDefault();var t=this.form.validateFields;t((function(e,t){!e&&a.onFormSubmit(t)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var a=this,t=this.record,r=this.activeKey;this.confirmLoading=!0,s["d"]({userId:t.user_id,target:r,form:e}).then((function(t){a.$message.success(t.message,1.5),a.handleCancel(),a.$emit("handleSubmit",e)})).finally((function(){return a.confirmLoading=!1}))}}},S=q,L=(t("644d"),Object(h["a"])(S,w,_,!1,null,"2bace808",null)),N=L.exports,F=Object(l["c"])([{title:"会员ID",dataIndex:"user_id"},{title:"会员头像",dataIndex:"avatar_url",scopedSlots:{customRender:"avatar_url"}},{title:"昵称/手机号",scopedSlots:{customRender:"main_info"}},{title:"会员等级",moduleKey:"user-grade",dataIndex:"grade",scopedSlots:{customRender:"grade"}},{title:"余额/积分",dataIndex:"balance",scopedSlots:{customRender:"balance"}},{title:"实际消费金额",dataIndex:"expend_money",scopedSlots:{customRender:"expend_money"}},{title:"注册来源",dataIndex:"platform",scopedSlots:{customRender:"platform"}},{title:"注册时间",dataIndex:"create_time"},{title:"操作",width:"180px",scopedSlots:{customRender:"action"}}]),I={name:"Index",components:{STable:d["d"],GradeForm:C,RechargeForm:N,PlatformIcon:u["a"]},data:function(){var e=this;return{searchForm:this.$form.createForm(this),queryParam:{},isLoading:!1,columns:F,loadData:function(a){return s["c"](Object(o["a"])(Object(o["a"])({},a),e.queryParam)).then((function(e){return e.data.list}))},gradeList:[]}},created:function(){this.getGradeList()},methods:{getGradeList:function(){var e=this;n["b"]().then((function(a){e.gradeList=a.data.list}))},handleGrade:function(e){this.$refs.GradeForm.handle(e)},handleRecharge:function(e){this.$refs.RechargeForm.handle(e)},handleDelete:function(e){var a=this,t=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return s["a"]({userId:e.user_id}).then((function(e){a.$message.success(e.message,1.5),a.handleRefresh()})).finally((function(e){return t.destroy()}))}})},handleSearch:function(e){var a=this;e.preventDefault(),this.searchForm.validateFields((function(e,t){e||(a.queryParam=Object(o["a"])(Object(o["a"])({},a.queryParam),t),a.handleRefresh(!0))}))},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)}}},k=I,E=(t("648d"),Object(h["a"])(k,r,i,!1,null,"52af8c9a",null));a["default"]=E.exports}}]);