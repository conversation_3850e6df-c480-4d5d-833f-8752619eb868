<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\dealer;

use think\response\Json;
use app\api\controller\Controller;
use app\api\model\dealer\Order as OrderModel;

/**
 * 分销商订单
 * Class Order
 * @package app\api\controller\user\dealer
 */
class Order extends Controller
{
    /**
     * 分销商订单列表
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DbException
     */
    public function list(): Json
    {
        // 分销商订单列表
        $model = new OrderModel;
        $list = $model->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }
}