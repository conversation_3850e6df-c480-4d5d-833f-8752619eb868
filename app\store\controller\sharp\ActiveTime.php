<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\sharp;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\sharp\ActiveTime as ActiveTimeModel;

/**
 * 秒杀活动会场-场次管理
 * Class ActiveTime
 * @package app\store\controller\apps\sharp
 */
class ActiveTime extends Controller
{
    /**
     * 活动会场场次列表
     * @param int $activeId 活动会场ID
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function list(int $activeId): Json
    {
        $model = new ActiveTimeModel;
        $list = $model->getList($activeId);
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 获取已存在的活动场次
     * @param int $activeId 活动会场ID
     * @return Json
     */
    public function existTimes(int $activeId): Json
    {
        $model = new ActiveTimeModel;
        $existTimes = $model->getActiveTimeData($activeId);
        return $this->renderSuccess(compact('existTimes'));
    }

    /**
     * 获取活动场次详情
     * @param int $activeTimeId 场次ID
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(int $activeTimeId): Json
    {
        // 场次详情
        $detail = ActiveTimeModel::detail($activeTimeId, ['active']);
        // 当前场次关联的商品
        $goodsList = ActiveTimeModel::getGoodsListByActiveTimeId($activeTimeId);
        return $this->renderSuccess(compact('detail', 'goodsList'));
    }

    /**
     * 修改场次状态
     * @param int $activeTimeId 场次ID
     * @param int $state 活动状态
     * @return Json
     */
    public function state(int $activeTimeId, int $state): Json
    {
        // 场次详情
        $model = ActiveTimeModel::detail($activeTimeId);
        if (!$model->setStatus($state)) {
            return $this->renderError('操作失败');
        }
        return $this->renderSuccess('操作成功');
    }

    /**
     * 新增活动会场场次
     * @param int $activeId 活动会场ID
     * @return Json
     */
    public function add(int $activeId): Json
    {
        // 新增记录
        $model = new ActiveTimeModel;
        if ($model->add($activeId, $this->postForm())) {
            return $this->renderSuccess('添加成功');
        }
        return $this->renderError($model->getError() ?: '添加失败');
    }

    /**
     * 编辑活动场次
     * @param int $activeTimeId 场次ID
     * @return Json
     */
    public function edit(int $activeTimeId): Json
    {
        // 更新活动场次
        $model = ActiveTimeModel::detail($activeTimeId);
        if ($model->edit($this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }

    /**
     * 删除活动场次
     * @param int $activeTimeId 场次ID
     * @return Json
     */
    public function delete(int $activeTimeId): Json
    {
        // 场次详情
        $model = ActiveTimeModel::detail($activeTimeId);
        if (!$model->onDelete()) {
            return $this->renderError($model->getError() ?: '删除失败');
        }
        return $this->renderSuccess('删除成功');
    }
}