(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["groupon"],{"0ef9":function(e,t,a){},"115f":function(e,t,a){"use strict";a("0ef9")},"1da1":function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));a("d3b7");function r(e,t,a,r,i,o,n){try{var s=e[o](n),l=s.value}catch(u){return void a(u)}s.done?t(l):Promise.resolve(l).then(r,i)}function i(e){return function(){var t=this,a=arguments;return new Promise((function(i,o){var n=e.apply(t,a);function s(e){r(n,i,o,s,l,"next",e)}function l(e){r(n,i,o,s,l,"throw",e)}s(void 0)}))}}},2485:function(e,t,a){},"27ae":function(e,t,a){"use strict";a("8b7c")},"448a":function(e,t,a){"use strict";a.r(t);a("b0c0"),a("d3b7"),a("25f0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"商品信息",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[e.goods?t("GoodsItem",{attrs:{data:{image:e.goods.goods_image,imageAlt:"商品图片",title:e.goods.goods_name,subtitle:"¥".concat(e.goods.goods_price_min)},subTitleColor:!0}}):e._e()],1),t("a-form-item",{attrs:{label:"活动时间",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime",{initialValue:e.defaultDate,rules:[{required:!0,message:"请选择有效期范围"}]}],expression:"['betweenTime', { initialValue: defaultDate, rules: [{ required: true, message: '请选择有效期范围' }] }]"}],attrs:{format:"YYYY-MM-DD HH:mm:ss","show-time":{defaultValue:e.moment("00:00:00","HH:mm:ss")}}})],1),t("a-form-item",{attrs:{label:"拼团类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-tooltip",[t("template",{slot:"title"},[e._v("拼团类型创建后不允许修改")]),t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["active_type",{initialValue:10,rules:[{required:!0}]}],expression:"['active_type', { initialValue: 10, rules: [{ required: true }] }]"}],attrs:{disabled:!0}},e._l(e.ActiveTypeEnum.data,(function(a,r){return t("a-radio",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1)],2),t("p",{staticClass:"form-item-help"},[e.record.active_type==e.ActiveTypeEnum.NORMAL.value?t("small",[e._v("用户下单开团后，邀请好友拼团，以优惠价格购买，提升店铺转化")]):e._e(),e.record.active_type==e.ActiveTypeEnum.PULL_NEW.value?t("small",[e._v("新老用户都可下单开团，但仅限新用户参团；新用户定义: 从未下单付款过的用户")]):e._e(),e.record.active_type==e.ActiveTypeEnum.STEPS.value?t("small",[e._v("人数越多价格越低，用低价刺激用户发起拉人更多的拼团，高效裂变")]):e._e()])],1),t("a-form-item",{attrs:{label:"拼单有效期",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["group_time",{initialValue:24,rules:[{required:!0,message:"请输入拼团有效期"}]}],expression:"['group_time', { initialValue: 24, rules: [{ required: true, message: '请输入拼团有效期' }] }]"}],attrs:{min:1,precision:0}}),t("span",{staticClass:"ml-8"},[e._v("小时")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("自用户成功发起拼团到拼单截止的周期")])])],1),t("a-form-item",{attrs:{label:"参团人数",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[e.record.active_type==e.ActiveTypeEnum.STEPS.value?t("div",[e._l(e.record.steps_config,(function(a,r){return t("a-form-item",{key:r},[t("span",{staticClass:"mr-10"},[e._v("第"+e._s(r+1)+"阶梯人数")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["steps_config[".concat(r,"]"),{initialValue:a?a.toString():"",validateTrigger:["blur"],rules:[{required:!0,message:"参团人数不能为空"},{validator:e.validatorStepsConfig}]}],expression:"[`steps_config[${index}]`, {\n                initialValue: value ? value.toString() : '',\n                validateTrigger: ['blur'],\n                rules: [\n                  { required: true, message: '参团人数不能为空' },\n                  { validator: validatorStepsConfig }\n                ],\n              },\n            ]"}],attrs:{min:2,max:100,precision:0}}),t("span",{staticClass:"ml-8"},[e._v("人")]),r>1?t("a",{staticClass:"ml-8",attrs:{href:"javascript:;"},on:{click:function(t){return e.handleDeleteStepsItem(r)}}},[e._v("删除")]):e._e()],1)})),e.record.steps_config.length<5?t("div",{staticClass:"action"},[t("a",{attrs:{href:"javascript:;"},on:{click:function(t){return e.handleAddStepsItem()}}},[e._v("添加阶梯人数")])]):e._e()],2):e._e(),e.record.active_type!=e.ActiveTypeEnum.STEPS.value?t("div",[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["people",{initialValue:2,rules:[{required:!0,message:"请输入2-100之间的整数"}]}],expression:"['people', { initialValue: 2, rules: [{ required: true, message: '请输入2-100之间的整数' }] }]"}],attrs:{min:2,max:100,precision:0}}),t("span",{staticClass:"ml-8"},[e._v("人")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("如果参团人数不满足，则视为拼团失败，需取消订单并退款")])])],1):e._e()]),20==e.record.spec_type&&e.goods&&e.record?t("div",[t("MultiSpec",{ref:"MultiSpec",attrs:{activeType:e.record.active_type,stepsConfig:e.form.getFieldValue("steps_config"),defaultSpecList:e.record.specList,defaultSkuList:e.record.skuList}})],1):e._e(),10==e.record.spec_type&&e.goods&&e.record?t("div",[t("a-form-item",{attrs:{label:"拼团价格",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[e.record.active_type==e.ActiveTypeEnum.STEPS.value?t("div",e._l(e.form.getFieldsValue()["steps_config"],(function(a,r){return t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:a,expression:"value"}],key:r},[t("span",{staticClass:"mr-10"},[e._v(e._s(a)+"人拼团价格")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["steps_price_config[".concat(r,"]"),{validateTrigger:["blur"],rules:[{required:!0,message:"拼团价格不能为空"},{validator:e.validatorStepsPrice}]}],expression:"[`steps_price_config[${index}]`, {\n                    validateTrigger: ['blur'],\n                    rules: [\n                      { required: true, message: '拼团价格不能为空' },\n                      { validator: validatorStepsPrice }\n                    ],\n                  },\n                ]"}],attrs:{min:.01,precision:2}}),t("span",{staticClass:"ml-8"},[e._v("元")])],1)})),1):e._e(),e.record.active_type!=e.ActiveTypeEnum.STEPS.value?t("div",[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["groupon_price",{rules:[{required:!0,message:"请输入拼团价格"}]}],expression:"['groupon_price', { rules: [{ required: true, message: '请输入拼团价格' }] }]"}],attrs:{min:.01,precision:2}}),t("span",{staticClass:"ml-8"},[e._v("元")])],1):e._e()])],1):e._e(),t("a-form-item",{attrs:{label:"活动状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:10,rules:[{required:!0}]}],expression:"['status', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("进行中")]),t("a-radio",{attrs:{value:20}},[e._v("已结束")])],1)],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0,precision:0}})],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("其他设置")]),t("a-form-item",{attrs:{label:"初始销量",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["initial_sales",{initialValue:100,rules:[{required:!0,message:"请输入初始销量"}]}],expression:"['initial_sales', { initialValue: 100, rules: [{ required: true, message: '请输入初始销量' }] }]"}],attrs:{min:0,precision:0}}),t("p",{staticClass:"form-item-help"},[t("small",[e._v("用户端显示的销量 = 初始销量 + 实际销量")])])],1),t("a-form-item",{attrs:{label:"单独购买",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_alone_buy",{initialValue:1,rules:[{required:!0}]}],expression:"['is_alone_buy', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("p",{staticClass:"form-item-help"},[t("small",[e._v("开启后买家进入拼团商品详情页，无需发起拼团可直接选择原价购买")])])],1),t("a-form-item",{attrs:{label:"活动限购",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_restrict",{initialValue:0,rules:[{required:!0}]}],expression:"['is_restrict', { initialValue: 0, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),e.form.getFieldValue("is_restrict")?t("div",{staticClass:"mt-10"},[t("a-form-item",[t("span",{staticClass:"mr-10"},[e._v("总限购")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_total",{rules:[{required:!0,message:"请输入总限购数量"}]}],expression:"['restrict_total', { rules: [{ required: true, message: '请输入总限购数量' }] }]"}],attrs:{min:1,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("件/人")])],1),t("a-form-item",[t("span",{staticClass:"mr-10"},[e._v("每单限购")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_single",{rules:[{required:!0,message:"请输入每单限购数量"}]}],expression:"['restrict_single', { rules: [{ required: true, message: '请输入每单限购数量' }] }]"}],attrs:{min:1,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("件/人")])],1)],1):e._e()],1),t("a-form-item",{attrs:{label:"显示凑团",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_quick_join",{initialValue:1,rules:[{required:!0}]}],expression:"['is_quick_join', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("p",{staticClass:"form-item-help"},[t("small",[e._v("活动商品详情页展示未成团的团列表，买家可以任选一个团参团，提升成团率")])])],1),t("a-form-item",{attrs:{label:"模拟成团",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_mock_task",{initialValue:0,rules:[{required:!0}]}],expression:"['is_mock_task', { initialValue: 0, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("p",{staticClass:"form-item-help"},[t("small",[e._v("开启后，满足条件的团（拼单结束前12分钟内），系统将会模拟 “虚拟买家” 凑满该团，请确保拼团机器人的数量足够")])]),e.form.getFieldValue("is_mock_task")?t("div",{staticClass:"mt-10"},[t("a-form-item",[t("span",{staticClass:"mr-10"},[e._v("最低参团人数满足")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["mock_min_people",{initialValue:1,rules:[{validator:e.validatorMockMinPeople}]}],expression:"['mock_min_people', { initialValue: 1, rules: [{ validator: validatorMockMinPeople }] }]"}],attrs:{min:1,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("人的团")])],1)],1):e._e()],1),t("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1)],1)],1)},i=[],o=a("c7eb"),n=a("1da1"),s=(a("ddb0"),a("ac1f"),a("466d"),a("a434"),a("c1df")),l=a.n(s),u=a("2ef0"),c=a.n(u),d=a("2702"),p=a("d084"),m=a("acad"),v=a("ca00"),f=a("ab09"),h=function(){var e=this,t=e._self._c;return t("div",[t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.multiSpecData.skuList.length,expression:"multiSpecData.skuList.length"}],attrs:{label:"商品SKU",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[e.multiSpecData.skuList.length>1?t("div",{staticClass:"sku-batch"},[t("span",{staticClass:"title"},[e._v("批量设置:")]),e.activeType!=e.ActiveTypeEnum.STEPS.value?t("a-input-number",{attrs:{placeholder:"拼团价格",min:.01,precision:2},model:{value:e.multiSpecData.skuBatchForm.groupon_price,callback:function(t){e.$set(e.multiSpecData.skuBatchForm,"groupon_price",t)},expression:"multiSpecData.skuBatchForm.groupon_price"}}):e._e(),e._l(e.stepsConfig,(function(a,r){return t("span",{key:r},[t("a-input-number",{attrs:{placeholder:"".concat(a,"人拼团价格"),min:.01,precision:2},model:{value:e.multiSpecData.skuBatchForm["groupon_price_".concat(r)],callback:function(t){e.$set(e.multiSpecData.skuBatchForm,"groupon_price_".concat(r),t)},expression:"multiSpecData.skuBatchForm[`groupon_price_${idx}`]"}})],1)})),t("a-button",{on:{click:e.handleSkuBatch}},[e._v("立即设置")])],2):e._e(),t("a-table",{staticClass:"sku-list",attrs:{columns:e.multiSpecData.skuColumns,dataSource:e.multiSpecData.skuList,scroll:{x:!0},pagination:!1,bordered:""},scopedSlots:e._u([{key:"goods_sku_no",fn:function(a){return[t("span",[e._v(e._s(a||"--"))])]}},{key:"groupon_price",fn:function(a,r){return[t("a-input-number",{attrs:{size:"small",min:.01,precision:2},model:{value:r.groupon_price,callback:function(t){e.$set(r,"groupon_price",t)},expression:"item.groupon_price"}})]}}])})],1)],1)},g=[],b=(a("a15b"),a("4e82"),a("4d91")),y=a("5530"),C=a("d4ec"),w=a("bee2"),k=(a("159b"),a("d81d"),a("99af"),a("7db0"),a("b64b"),a("4de4"),a("4d63"),[{title:"SKU编码",dataIndex:"goods_sku_no",scopedSlots:{customRender:"goods_sku_no"}},{title:"商品原价（元）",dataIndex:"goods_price",scopedSlots:{customRender:"goods_price"}},{title:"商品库存",dataIndex:"stock_num",scopedSlots:{customRender:"stock_num"}}]),S={goods_sku_id:"",goods_sku_no:"",goods_price:"",stock_num:"",groupon_price:"",steps_price_config:[]},x=function(){function e(t){Object(C["a"])(this,e),this.app=void 0,this.activeType=void 0,this.stepsConfig=[],this.multiSpecData={},this.error="",this.app=t,this.multiSpecData={specList:[],skuList:[],skuColumns:c.a.cloneDeep(k),skuBatchForm:c.a.cloneDeep(S)}}return Object(w["a"])(e,[{key:"getData",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],a=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;e.length&&(this.multiSpecData.specList=c.a.cloneDeep(e),this.multiSpecData.skuList=c.a.cloneDeep(t),this.activeType=a,this.stepsConfig=r);var i=this.specGroupArr(),o=q(i),n=this.rowSpanArr(i,o);return this.buildSkuColumns(n),this.buildSkuList(o),this.multiSpecData}},{key:"isEmpty",value:function(){return 0===this.multiSpecData.specList.length}},{key:"getError",value:function(){return this.error}},{key:"specGroupArr",value:function(){var e=[];return this.multiSpecData.specList.forEach((function(t){var a=[];t.valueList.forEach((function(e){a.push(e)})),e.push(a)})),e}},{key:"rowSpanArr",value:function(e,t){for(var a=[],r=t.length,i=0;i<e.length;i++)a[i]=parseInt(r/e[i].length),r=a[i];return a}},{key:"buildSkuList",value:function(e){for(var t=this,a=[],r=function(){var r=Object(y["a"])(Object(y["a"])({},S),{},{key:i,skuKey:e[i].map((function(e){return e.key})).join("_"),skuKeys:e[i].map((function(e){return{groupKey:e.groupKey,valueKey:e.key}}))});e[i].forEach((function(e,t){r["spec_value_".concat(t)]=e.spec_value})),r.steps_price_config=[],t.stepsConfig.forEach((function(e,t){r["groupon_price_".concat(t)]=void 0,r.steps_price_config.push(void 0)})),a.push(r)},i=0;i<e.length;i++)r();this.multiSpecData.skuList=this.oldSkuList(a)}},{key:"oldSkuList",value:function(e){var t=this,a=this.multiSpecData.skuList.concat();if(!a.length||!e.length)return e;var r=function(r){var i={};i=a.length===e.length?c.a.cloneDeep(a[r]):a.find((function(t){return t.skuKey===e[r].skuKey})),i&&(e[r]=t.mergeOldSkuItem(e[r],i))};for(var i in e)r(i);return e}},{key:"mergeOldSkuItem",value:function(e,t){var a=this.stepsConfig,r=Object.keys(S);a.forEach((function(e,t){r.push("groupon_price_".concat(t)),r.push("steps_price_config")}));var i=c.a.pick(t,r);return i.steps_price_config&&i.steps_price_config.length&&i.steps_price_config.forEach((function(e,t){i["groupon_price_".concat(t)]||(i["groupon_price_".concat(t)]=e)})),i.steps_price_config=[],a.forEach((function(e,t){i.hasOwnProperty("groupon_price_".concat(t))&&i.steps_price_config.push(i["groupon_price_".concat(t)])})),Object(y["a"])(Object(y["a"])({},e),i)}},{key:"buildSkuColumns",value:function(e){for(var t=this.multiSpecData.specList,a=this.getDefaultColumns(),r=function(t,a,r,i){var o={children:a,attrs:{}},n=e[t-1];return o.attrs.rowSpan=i%n===0?n:0,o},i=function(e){var i=t[e-1];a.unshift({title:i.spec_name,dataIndex:"spec_value_".concat(e-1),customRender:function(t,a,i){return r(e,t,a,i)}})},o=t.length;o>0;o--)i(o);this.multiSpecData.skuColumns=a}},{key:"getDefaultColumns",value:function(){var e=this,t=c.a.cloneDeep(k);return e.activeType!=m["a"].STEPS.value&&t.push({title:"拼团价格（元）",dataIndex:"groupon_price",scopedSlots:{customRender:"groupon_price"}}),e.activeType==m["a"].STEPS.value&&e.stepsConfig.forEach((function(a,r){var i="groupon_price_".concat(r);t.push({title:"".concat(a,"人拼团价格（元）"),dataIndex:i,width:150,customRender:function(t,a){return e.app.$createElement("div",{class:{"has-error":Object(v["g"])(a[i])&&void 0!==a[i]}},[e.app.$createElement("a-input-number",{props:{value:a[i],min:.01,precision:2},on:{change:function(e){a[i]=e,a.steps_price_config[r]=e}}})])}})})),t}},{key:"handleAddSpecGroup",value:function(){var e=this.multiSpecData.specList;e.push({key:e.length||0,spec_name:"",valueList:[]});var t=e.length-1;this.handleAddSpecValue(t)}},{key:"handleAddSpecValue",value:function(e){var t=this.multiSpecData.specList[e],a=t.valueList;a.push({key:a.length||0,groupKey:t.key,spec_value:""})}},{key:"handleDeleteSpecGroup",value:function(e){this.multiSpecData.specList.splice(e,1),this.onUpdate(!1)}},{key:"handleDeleteSpecValue",value:function(e,t){this.multiSpecData.specList[e].valueList.splice(t,1),this.onUpdate(!1)}},{key:"handleSkuBatch",value:function(){var e=this.getFilterObject(this.multiSpecData.skuBatchForm),t=this.multiSpecData.skuList;for(var a in t)t[a]=Object(y["a"])(Object(y["a"])({},t[a]),e);this.onUpdate(!1)}},{key:"getFilterObject",value:function(e){var t={};for(var a in e){var r=e[a];Object(v["g"])(r)||(t[a]=r)}return t}},{key:"verifyForm",value:function(){return!!this.verifySpec()&&!!this.verifySkuList()}},{key:"verifySkuList",value:function(){var e=this,t=[{field:"groupon_price",name:"拼团价格"},{field:"groupon_price_",name:"拼团价格",dynamic:!0}];this.error="";var a=this.multiSpecData.skuList,r=function(){var r=a[i],o=function(){var a=t[n],i=[a.field];a.dynamic&&(i=c.a.keys(r).filter((function(e){return new RegExp("^".concat(a.field,"\\d+$")).test(e)}))),i.forEach((function(t){var i=r[t];""!==i&&null!==i&&void 0!==i||(r[t]="",e.error="".concat(a.name,"不能为空"))}))};for(var n in t)o()};for(var i in a)r();return!Object(v["g"])(this.error)&&this.onUpdate(!1),Object(v["g"])(this.error)}},{key:"verifySpec",value:function(){var e=this.multiSpecData.specList;if(!e.length)return this.error="亲，还没有添加规格组~",!1;for(var t in e){var a=e[t];if(Object(v["g"])(a.spec_name))return this.error="规格组名称不能为空~",!1;var r=a.valueList;if(!r.length)return this.error="还没有添加规格值~",!1;for(var i in r)if(Object(v["g"])(r[i].spec_value))return this.error="规格值不能为空~",!1}return!0}},{key:"getFromSpecData",value:function(){var e=this.multiSpecData.skuList,t={skuList:c.a.cloneDeep(e)};for(var a in t.skuList){var r=t.skuList[a];delete r.image,delete r.key}return t}},{key:"onUpdate",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e?Object(v["d"])(L,200)(this):L(this)}}]),e}(),L=function(e){return e.getData()},q=function(e){return e.length?Array.prototype.reduce.call(e,(function(e,t){var a=[];return e.forEach((function(e){t.forEach((function(t){a.push(e.concat([t]))}))})),a}),[[]]):[]},E=(a("04b3"),function(e,t){return e.sort().join("")===t.sort().join("")}),D={props:{defaultSpecList:b["a"].array.def([]),defaultSkuList:b["a"].array.def([]),activeType:b["a"].integer.def(m["a"].NORMAL.value),stepsConfig:b["a"].array.def([])},data:function(){return{labelCol:{span:3},wrapperCol:{span:21},ActiveTypeEnum:m["a"],MultiSpecModel:new x(this),multiSpecData:{specList:[],skuList:[]}}},watch:{defaultSpecList:function(e){e.length&&this.MultiSpecModel.isEmpty()&&this.getData()},stepsConfig:function(e,t){!E(e,t)&&this.getData()}},created:function(){this.getData()},methods:{getData:function(){var e=this.defaultSpecList,t=this.defaultSkuList,a=this.activeType,r=this.stepsConfig;this.multiSpecData=this.MultiSpecModel.getData(e,t,a,r)},getFromSpecData:function(){return this.MultiSpecModel.getFromSpecData()},handleSkuBatch:function(){this.MultiSpecModel.handleSkuBatch()},verifyForm:function(){return!!this.MultiSpecModel.verifyForm()||(this.$message.error(this.MultiSpecModel.getError(),2),!1)}}},O=D,T=(a("ff9b"),a("2877")),I=Object(T["a"])(O,h,g,!1,null,"38e47ebc",null),j=I.exports,V={components:{GoodsItem:f["a"],MultiSpec:j},data:function(){return{isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},ActiveTypeEnum:m["a"],form:this.$form.createForm(this),defaultDate:[l()(),l()().add(7,"days")],grouponGoodsId:null,record:{},goods:null}},beforeCreate:function(){this.moment=l.a},created:function(){var e=this;return Object(n["a"])(Object(o["a"])().mark((function t(){return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.grouponGoodsId=e.$route.query.grouponGoodsId,e.isLoading=!0,t.next=4,e.getDetail();case 4:return t.next=6,e.getGoodsInfo();case 6:e.isLoading=!1,e.setFieldsValue();case 8:case"end":return t.stop()}}),t)})))()},methods:{getDetail:function(){var e=this;return Object(n["a"])(Object(o["a"])().mark((function t(){var a;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=e.grouponGoodsId,t.next=3,d["c"](a).then((function(t){e.record=t.data.detail}));case 3:case"end":return t.stop()}}),t)})))()},getGoodsInfo:function(){var e=this;return Object(n["a"])(Object(o["a"])().mark((function t(){var a;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=e.record,t.next=3,p["b"](a.goods_id).then((function(t){return e.goods=t.data.detail}));case 3:case"end":return t.stop()}}),t)})))()},setFieldsValue:function(){var e=this,t=this.record,a=this.form,r=this.$nextTick,i=this.getExistFields;!Object(v["g"])(a.getFieldsValue())&&r((function(){var o=Object(u["pick"])(t,["active_type","group_time","is_quick_join","is_alone_buy","is_restrict","is_mock_task","initial_sales","sort","status"]);o.betweenTime=e.getBetweenTime(t),a.setFieldsValue(o),r((function(){var e=i(["mock_min_people","restrict_total","restrict_single","people","steps_config"]);a.setFieldsValue(Object(u["pick"])(t,e));var o=i(["groupon_price","steps_price_config"]);r((function(){a.setFieldsValue(Object(u["pick"])(t.skuList[0],o))}))}))}))},getExistFields:function(e){this.record;var t=this.form;return _.intersection(_.keys(t.getFieldsValue()),e)},getBetweenTime:function(e){return[l()(new Date(e.start_time)),l()(new Date(e.end_time))]},validatorMockMinPeople:function(e,t,a){var r=this.form,i=this.record,o=i.active_type,n=r.getFieldValue("steps_config");if(t){var s=o!=m["a"].STEPS.value?r.getFieldValue("people"):n[n.length-1];parseInt(t)>=parseInt(s)?a("模拟成团人数应小于最大参团人数"):a()}else a("请填写模拟成团最低参团人数")},validatorStepsConfig:function(e,t,a){var r=this.form.getFieldValue("steps_config"),i=e.field.match(/steps_config\[(\d+)\]/)[1];parseInt(t)<2?a("参团人数最少2人"):parseInt(t)>100?a("参团人数最多100人"):t&&i>0&&parseInt(r[i-1])>=parseInt(t)?a("参团人数需要大于上一级阶梯"):a()},validatorStepsPrice:function(e,t,a){var r=this.form.getFieldValue("steps_price_config"),i=e.field.match(/steps_price_config\[(\d+)\]/)[1];t&&i>0&&parseFloat(r[i-1])<=parseFloat(t)?a("价格需要小于上一级阶梯"):a()},handleAddStepsItem:function(){this.record.steps_config.push(void 0),this.forceUpdate()},handleDeleteStepsItem:function(e){this.record.steps_config.splice(e,1),this.forceUpdate()},forceUpdate:function(){var e=this;this.$nextTick((function(){return e.$forceUpdate()}),10)},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.record,r=this.form.validateFields,i=this.onFormSubmit;r((function(e,r){if(e)return!1;if(20===a.spec_type){var o=t.$refs.MultiSpec;if(!o.verifyForm())return!1;r.specData=o.getFromSpecData()}i(r)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,this.isBtnLoading=!0,d["d"](this.grouponGoodsId,{form:e}).then((function(e){t.$message.success(e.message,1.5),setTimeout((function(){return t.$router.push("./index")}),1200)})).catch((function(){return t.isBtnLoading=!1})).finally((function(){return t.isLoading=!1}))}}},F=V,N=(a("27ae"),Object(T["a"])(F,r,i,!1,null,"72d818ec",null));t["default"]=N.exports},"4a95":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"d",(function(){return n})),a.d(t,"e",(function(){return s})),a.d(t,"g",(function(){return l})),a.d(t,"h",(function(){return u})),a.d(t,"f",(function(){return c})),a.d(t,"a",(function(){return d}));var r=a("5c06"),i=new r["a"]([{key:"NOT_DELIVERED",name:"未发货",value:10},{key:"DELIVERED",name:"已发货",value:20},{key:"PART_DELIVERED",name:"部分发货",value:30}]),o=new r["a"]([{key:"EXPRESS",name:"快递配送",value:10},{key:"EXTRACT",name:"上门自提",value:20},{key:"NOTHING",name:"无需配送",value:30}]),n=new r["a"]([{key:"MASTER",name:"普通订单",value:10},{key:"BARGAIN",name:"砍价订单",value:20},{key:"SHARP",name:"秒杀订单",value:30},{key:"GROUPON",name:"拼团订单",value:40}]),s=new r["a"]([{key:"NORMAL",name:"进行中",value:10},{key:"CANCELLED",name:"已取消",value:20},{key:"APPLY_CANCEL",name:"待取消",value:21},{key:"COMPLETED",name:"已完成",value:30}]),l=new r["a"]([{key:"PENDING",name:"待支付",value:10},{key:"SUCCESS",name:"已支付",value:20}]),u=new r["a"]([{key:"NOT_RECEIVED",name:"未收货",value:10},{key:"RECEIVED",name:"已收货",value:20}]),c=new r["a"]([{key:"PHYSICAL",name:"实物订单",value:10},{key:"VIRTUAL",name:"虚拟订单",value:20}]),d=new r["a"]([{key:"ALL",name:"全部",value:"all"},{key:"DELIVERY",name:"待发货",value:"delivery"},{key:"RECEIPT",name:"待收货",value:"receipt"},{key:"PAY",name:"待付款",value:"pay"},{key:"COMPLETE",name:"已完成",value:"complete"},{key:"APPLY_CANCEL",name:"待取消",value:"apply_cancel"},{key:"CANCEL",name:"已取消",value:"cancel"}])},"63e1":function(e,t,a){},"6b13":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-alert",{attrs:{message:"拼团活动开启 “模拟成团” 后，满足条件的拼单，系统将会使用随机的 “拼团机器人” 模拟 “虚拟买家” 凑满该团，仅需对真实拼团买家发货。",banner:""}}),t("div",{staticClass:"table-operator"},[t("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),t("s-table",{ref:"table",attrs:{rowKey:"robot_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"avatar",fn:function(e){return t("span",{},[t("div",{staticClass:"avatar"},[t("img",e?{attrs:{width:"45",height:"45",src:e.preview_url,alt:"用户头像"}}:{attrs:{width:"45",height:"45",src:a("889b"),alt:"用户头像"}})])])}},{key:"gender",fn:function(a){return t("span",{},[t("a-tag",[e._v(e._s(["未知","男","女"][a]))])],1)}},{key:"action",fn:function(a,r){return t("span",{staticClass:"actions"},[t("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),t("AddForm",{ref:"AddForm",on:{handleSubmit:e.handleRefresh}}),t("EditForm",{ref:"EditForm",on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=a("5530"),n=(a("d3b7"),a("2af9")),s=a("b775"),l={list:"/groupon.robot/list",add:"/groupon.robot/add",edit:"/groupon.robot/edit",delete:"/groupon.robot/delete"};function u(e){return Object(s["b"])({url:l.list,method:"get",params:e})}function c(e){return Object(s["b"])({url:l.add,method:"post",data:e})}function d(e){return Object(s["b"])({url:l.edit,method:"post",data:e})}function p(e){return Object(s["b"])({url:l.delete,method:"post",data:e})}var m=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"用户昵称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["nick_name",{rules:[{required:!0,message:"请输入用户昵称"}]}],expression:"['nick_name', { rules: [{ required: true, message: '请输入用户昵称' }] }]"}]})],1),t("a-form-item",{attrs:{label:"用户头像",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸: 300*300",required:""}},[t("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["avatar_id",{rules:[{required:!0,message:"请上传用户头像"}]}],expression:"['avatar_id', { rules: [{ required: true, message: '请上传用户头像' }] }]"}]})],1),t("a-form-item",{attrs:{label:"性别",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["gender",{initialValue:1,rules:[{required:!0}]}],expression:"['gender', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("男")]),t("a-radio",{attrs:{value:2}},[e._v("女")])],1)],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},v=[],f=a("88bc"),h=a.n(f),g={components:{SelectImage:n["h"]},data:function(){return{title:"编辑机器人",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{handle:function(){this.visible=!0},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,c({form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){return t.confirmLoading=!1}))}}},_=g,b=a("2877"),y=Object(b["a"])(_,m,v,!1,null,null,null),C=y.exports,w=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1,destroyOnClose:!0},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"用户昵称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["nick_name",{rules:[{required:!0,message:"请输入用户昵称"}]}],expression:"['nick_name', { rules: [{ required: true, message: '请输入用户昵称' }] }]"}]})],1),t("a-form-item",{attrs:{label:"用户头像",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"建议尺寸: 300*300",required:""}},[t("SelectImage",{directives:[{name:"decorator",rawName:"v-decorator",value:["avatar_id",{rules:[{required:!0,message:"请上传用户头像"}]}],expression:"['avatar_id', { rules: [{ required: true, message: '请上传用户头像' }] }]"}],attrs:{defaultList:e.record.avatar?[e.record.avatar]:[]}})],1),t("a-form-item",{attrs:{label:"性别",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["gender",{initialValue:1,rules:[{required:!0}]}],expression:"['gender', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("男")]),t("a-radio",{attrs:{value:2}},[e._v("女")])],1)],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},k=[],S={components:{SelectImage:n["h"]},data:function(){return{title:"编辑机器人",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{handle:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this.record,t=this.form.setFieldsValue;this.$nextTick((function(){t(h()(e,["nick_name","avatar_id","gender","sort"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,d({robotId:this.record.robot_id,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){return t.confirmLoading=!1}))}}},x=S,L=Object(b["a"])(x,w,k,!1,null,null,null),q=L.exports,E={name:"Index",components:{STable:n["d"],AddForm:C,EditForm:q},data:function(){var e=this;return{queryParam:{},isLoading:!1,columns:[{title:"机器人ID",dataIndex:"robot_id"},{title:"用户头像",dataIndex:"avatar",scopedSlots:{customRender:"avatar"}},{title:"用户昵称",dataIndex:"nick_name"},{title:"性别",dataIndex:"gender",scopedSlots:{customRender:"gender"}},{title:"排序",dataIndex:"sort"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(t){return u(Object(o["a"])(Object(o["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$refs.AddForm.handle()},handleEdit:function(e){this.$refs.EditForm.handle(e)},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return p({robotId:e.robot_id}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},D=E,O=Object(b["a"])(D,r,i,!1,null,null,null);t["default"]=O.exports},"7fae":function(e,t,a){"use strict";a.r(t);a("b0c0"),a("ac1f"),a("841c");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("div",{staticClass:"guide-create"},[t("a-row",{attrs:{gutter:40}},[t("a-col",{attrs:{span:8}},[t("div",{staticClass:"guide-item"},[t("div",{staticClass:"title"},[e._v("普通拼团")]),t("div",{staticClass:"describe"},[e._v("用户下单开团后，邀请好友拼团，以优惠价格购买，提升店铺转化")]),e.$auth("/apps/groupon/goods/create")?t("div",{staticClass:"action"},[t("router-link",{attrs:{to:{path:"/apps/groupon/goods/create",query:{type:e.ActiveTypeEnum.NORMAL.value}}}},[t("a-button",{attrs:{type:"primary"}},[e._v("立即创建")])],1)],1):e._e()])]),t("a-col",{attrs:{span:8}},[t("div",{staticClass:"guide-item"},[t("div",{staticClass:"title"},[e._v("老带新拼团")]),t("div",{staticClass:"describe"},[e._v("新老用户都可下单开团，但仅限新用户参团，快速裂变获取新客")]),e.$auth("/apps/groupon/goods/create")?t("div",{staticClass:"action"},[t("router-link",{attrs:{to:{path:"/apps/groupon/goods/create",query:{type:e.ActiveTypeEnum.PULL_NEW.value}}}},[t("a-button",{attrs:{type:"primary"}},[e._v("立即创建")])],1)],1):e._e()])]),t("a-col",{attrs:{span:8}},[t("div",{staticClass:"guide-item"},[t("div",{staticClass:"title"},[e._v("阶梯拼团")]),t("div",{staticClass:"describe"},[e._v("人数越多价格越低，用低价刺激用户发起拉人更多的拼团，高效裂变")]),e.$auth("/apps/groupon/goods/create")?t("div",{staticClass:"action"},[t("router-link",{attrs:{to:{path:"/apps/groupon/goods/create",query:{type:e.ActiveTypeEnum.STEPS.value}}}},[t("a-button",{attrs:{type:"primary"}},[e._v("立即创建")])],1)],1):e._e()])])],1)],1),t("a-row",[t("a-col",{attrs:{span:8}},[t("div",{staticClass:"tab-list"},[t("a-radio-group",{attrs:{defaultValue:e.queryParam.activeType},on:{change:e.handleTabs}},[t("a-radio-button",{attrs:{value:0}},[e._v("全部")]),e._l(e.ActiveTypeEnum.data,(function(a,r){return t("a-radio-button",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])}))],2)],1)]),t("a-col",{staticClass:"flex flex-x-end",attrs:{offset:8}},[t("a-select",{staticStyle:{width:"220px","margin-right":"20px"},attrs:{placeholder:"活动状态"},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}},e._l(e.GoodsStatusEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1),t("a-input-search",{staticStyle:{"max-width":"250px","min-width":"150px"},attrs:{placeholder:"请输入商品名称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"groupon_goods_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15,scroll:{x:1300}},scopedSlots:e._u([{key:"goods",fn:function(e){return[t("GoodsItem",{attrs:{data:{image:e.goods_image,imageAlt:"商品图片",title:e.goods_name,subtitle:"¥".concat(e.goods_price_min)},subTitleColor:!0}})]}},{key:"active_type",fn:function(a){return t("span",{},[t("a-tag",[e._v(e._s(e.ActiveTypeEnum[a].name))])],1)}},{key:"groupon_price_min",fn:function(a){return[t("p",{staticClass:"c-p"},[e._v("¥"+e._s(a))])]}},{key:"time",fn:function(a){return t("span",{},[t("p",[e._v("开始："+e._s(a.start_time))]),t("p",[e._v("结束："+e._s(a.end_time))])])}},{key:"status",fn:function(a){return t("span",{},[t("a-tag",{attrs:{color:a==e.ActiveTypeEnum.NORMAL.value?"green":"orange"}},[e._v(e._s(e.GoodsStatusEnum[a].name))])],1)}},{key:"action",fn:function(a,r){return t("span",{staticClass:"actions"},[e.$auth("/apps/groupon/goods/update")?t("router-link",{attrs:{to:{path:"/apps/groupon/goods/update",query:{grouponGoodsId:r.groupon_goods_id}}}},[e._v("编辑")]):e._e(),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")])],1)}}])})],1)},i=[],o=a("5530"),n=(a("d3b7"),a("ab09")),s=a("acad"),l=a("2702"),u={name:"Index",components:{STable:n["b"],GoodsItem:n["a"]},data:function(){var e=this;return{queryParam:{activeType:0,status:void 0,search:""},isLoading:!1,GoodsStatusEnum:s["b"],ActiveTypeEnum:s["a"],columns:[{title:"商品ID",dataIndex:"groupon_goods_id"},{title:"商品信息",dataIndex:"goods",width:"320px",scopedSlots:{customRender:"goods"}},{title:"拼团类型",dataIndex:"active_type",scopedSlots:{customRender:"active_type"}},{title:"拼团价格",dataIndex:"groupon_price_min",scopedSlots:{customRender:"groupon_price_min"}},{title:"活动时间",width:"200px",scopedSlots:{customRender:"time"}},{title:"实际销量",dataIndex:"actual_sales"},{title:"活动状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"操作",dataIndex:"action",width:"150px",fixed:"right",scopedSlots:{customRender:"action"}}],loadData:function(t){return l["e"](Object(o["a"])(Object(o["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleTabs:function(e){this.queryParam.activeType=e.target.value,this.handleRefresh(!0)},handleAdd:function(){this.$refs.AddForm.handle()},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return l["b"]({grouponGoodsId:e.groupon_goods_id}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},c=u,d=(a("b18d"),a("2877")),p=Object(d["a"])(c,r,i,!1,null,"30f9810d",null);t["default"]=p.exports},8591:function(e,t,a){"use strict";a("63e1")},"88bc":function(e,t,a){(function(t){var a=1/0,r=9007199254740991,i="[object Arguments]",o="[object Function]",n="[object GeneratorFunction]",s="[object Symbol]",l="object"==typeof t&&t&&t.Object===Object&&t,u="object"==typeof self&&self&&self.Object===Object&&self,c=l||u||Function("return this")();function d(e,t,a){switch(a.length){case 0:return e.call(t);case 1:return e.call(t,a[0]);case 2:return e.call(t,a[0],a[1]);case 3:return e.call(t,a[0],a[1],a[2])}return e.apply(t,a)}function p(e,t){var a=-1,r=e?e.length:0,i=Array(r);while(++a<r)i[a]=t(e[a],a,e);return i}function m(e,t){var a=-1,r=t.length,i=e.length;while(++a<r)e[i+a]=t[a];return e}var v=Object.prototype,f=v.hasOwnProperty,h=v.toString,g=c.Symbol,_=v.propertyIsEnumerable,b=g?g.isConcatSpreadable:void 0,y=Math.max;function C(e,t,a,r,i){var o=-1,n=e.length;a||(a=x),i||(i=[]);while(++o<n){var s=e[o];t>0&&a(s)?t>1?C(s,t-1,a,r,i):m(i,s):r||(i[i.length]=s)}return i}function w(e,t){return e=Object(e),k(e,t,(function(t,a){return a in e}))}function k(e,t,a){var r=-1,i=t.length,o={};while(++r<i){var n=t[r],s=e[n];a(s,n)&&(o[n]=s)}return o}function S(e,t){return t=y(void 0===t?e.length-1:t,0),function(){var a=arguments,r=-1,i=y(a.length-t,0),o=Array(i);while(++r<i)o[r]=a[t+r];r=-1;var n=Array(t+1);while(++r<t)n[r]=a[r];return n[t]=o,d(e,this,n)}}function x(e){return E(e)||q(e)||!!(b&&e&&e[b])}function L(e){if("string"==typeof e||F(e))return e;var t=e+"";return"0"==t&&1/e==-a?"-0":t}function q(e){return O(e)&&f.call(e,"callee")&&(!_.call(e,"callee")||h.call(e)==i)}var E=Array.isArray;function D(e){return null!=e&&I(e.length)&&!T(e)}function O(e){return V(e)&&D(e)}function T(e){var t=j(e)?h.call(e):"";return t==o||t==n}function I(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=r}function j(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function V(e){return!!e&&"object"==typeof e}function F(e){return"symbol"==typeof e||V(e)&&h.call(e)==s}var N=S((function(e,t){return null==e?{}:w(e,p(C(t,1),L))}));e.exports=N}).call(this,a("c8ba"))},"8b7c":function(e,t,a){},"8dc9":function(e,t,a){"use strict";a("d4a2")},b18d:function(e,t,a){"use strict";a("2485")},b21b:function(e,t,a){},b9bf:function(e,t,a){"use strict";a.r(t);a("d3b7"),a("25f0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"选择商品",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("SelectGoods",{directives:[{name:"decorator",rawName:"v-decorator",value:["goods_id",{rules:[{required:!0,message:"请选择指定的商品"}]}],expression:"['goods_id', { rules: [{ required: true, message: '请选择指定的商品' }] }]"}],attrs:{multiple:!1}}),t("p",{staticClass:"form-item-help"},[t("small",[e._v("添加拼团商品后，将不允许修改主商品的规格属性")])])],1),t("a-form-item",{attrs:{label:"活动时间",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime",{initialValue:e.defaultDate,rules:[{required:!0,message:"请选择有效期范围"}]}],expression:"['betweenTime', { initialValue: defaultDate, rules: [{ required: true, message: '请选择有效期范围' }] }]"}],attrs:{format:"YYYY-MM-DD HH:mm:ss","show-time":{defaultValue:e.moment("00:00:00","HH:mm:ss")}}})],1),t("a-form-item",{attrs:{label:"拼单有效期",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["group_time",{initialValue:24,rules:[{required:!0,message:"请输入拼团有效期"}]}],expression:"['group_time', { initialValue: 24, rules: [{ required: true, message: '请输入拼团有效期' }] }]"}],attrs:{min:1,precision:0}}),t("span",{staticClass:"ml-8"},[e._v("小时")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("自用户成功发起拼团到拼单截止的周期")])])],1),t("a-form-item",{attrs:{label:"参团人数",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[e.activeType==e.ActiveTypeEnum.STEPS.value?t("div",[e._l(e.stepsConfig,(function(a,r){return t("a-form-item",{key:r},[t("span",{staticClass:"mr-10"},[e._v("第"+e._s(r+1)+"阶梯人数")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["steps_config[".concat(r,"]"),{initialValue:a.value?a.value.toString():"",validateTrigger:["blur"],rules:[{required:!0,message:"参团人数不能为空"},{validator:e.validatorStepsConfig}]}],expression:"[`steps_config[${index}]`, {\n                initialValue: item.value ? item.value.toString() : '',\n                validateTrigger: ['blur'],\n                rules: [\n                  { required: true, message: '参团人数不能为空' },\n                  { validator: validatorStepsConfig }\n                ],\n              },\n            ]"}],attrs:{min:2,precision:0}}),t("span",{staticClass:"ml-8"},[e._v("人")]),r>1?t("a",{staticClass:"ml-8",attrs:{href:"javascript:;"},on:{click:function(t){return e.handleDeleteStepsItem(r)}}},[e._v("删除")]):e._e()],1)})),e.stepsConfig.length<5?t("div",{staticClass:"action"},[t("a",{attrs:{href:"javascript:;"},on:{click:function(t){return e.handleAddStepsItem()}}},[e._v("添加阶梯人数")])]):e._e()],2):e._e(),e.activeType!=e.ActiveTypeEnum.STEPS.value?t("div",[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["people",{initialValue:2,rules:[{required:!0,message:"请输入2-100之间的整数"}]}],expression:"['people', { initialValue: 2, rules: [{ required: true, message: '请输入2-100之间的整数' }] }]"}],attrs:{min:2,max:100,precision:0}}),t("span",{staticClass:"ml-8"},[e._v("人")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("如果参团人数不满足，则视为拼团失败，需取消订单并退款")])])],1):e._e()]),t("a-form-item",{attrs:{label:"活动状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:10,rules:[{required:!0}]}],expression:"['status', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("进行中")]),t("a-radio",{attrs:{value:20}},[e._v("已结束")])],1)],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0,precision:0}})],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("其他设置")]),t("a-form-item",{attrs:{label:"初始销量",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["initial_sales",{initialValue:100,rules:[{required:!0,message:"请输入初始销量"}]}],expression:"['initial_sales', { initialValue: 100, rules: [{ required: true, message: '请输入初始销量' }] }]"}],attrs:{min:0,precision:0}}),t("p",{staticClass:"form-item-help"},[t("small",[e._v("用户端显示的销量 = 初始销量 + 实际销量")])])],1),t("a-form-item",{attrs:{label:"单独购买",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_alone_buy",{initialValue:1,rules:[{required:!0}]}],expression:"['is_alone_buy', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("p",{staticClass:"form-item-help"},[t("small",[e._v("开启后买家进入拼团商品详情页，无需发起拼团可直接选择原价购买")])])],1),t("a-form-item",{attrs:{label:"活动限购",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_restrict",{initialValue:0,rules:[{required:!0}]}],expression:"['is_restrict', { initialValue: 0, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),e.form.getFieldValue("is_restrict")?t("div",{staticClass:"mt-10"},[t("a-form-item",[t("span",{staticClass:"mr-10"},[e._v("总限购")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_total",{initialValue:9999,rules:[{required:!0,message:"请输入总限购数量"}]}],expression:"['restrict_total', { initialValue: 9999, rules: [{ required: true, message: '请输入总限购数量' }] }]"}],attrs:{min:1,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("件/人")])],1),t("a-form-item",[t("span",{staticClass:"mr-10"},[e._v("每单限购")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["restrict_single",{initialValue:9999,rules:[{required:!0,message:"请输入每单限购数量"}]}],expression:"['restrict_single', { initialValue: 9999, rules: [{ required: true, message: '请输入每单限购数量' }] }]"}],attrs:{min:1,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("件/人")])],1)],1):e._e()],1),t("a-form-item",{attrs:{label:"显示凑团",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_quick_join",{initialValue:1,rules:[{required:!0}]}],expression:"['is_quick_join', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("p",{staticClass:"form-item-help"},[t("small",[e._v("活动商品详情页展示未成团的团列表，买家可以任选一个团参团，提升成团率")])])],1),t("a-form-item",{attrs:{label:"模拟成团",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_mock_task",{initialValue:0,rules:[{required:!0}]}],expression:"['is_mock_task', { initialValue: 0, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("p",{staticClass:"form-item-help"},[t("small",[e._v("开启后，满足条件的团（拼单结束前12分钟内），系统将会模拟 “虚拟买家” 凑满该团，请确保拼团机器人的数量足够")])]),e.form.getFieldValue("is_mock_task")?t("div",{staticClass:"mt-10"},[t("a-form-item",[t("span",{staticClass:"mr-10"},[e._v("最低参团人数满足")]),t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["mock_min_people",{initialValue:1,rules:[{validator:e.validatorMockMinPeople}]}],expression:"['mock_min_people', { initialValue: 1, rules: [{ validator: validatorMockMinPeople }] }]"}],attrs:{min:1,precision:0}}),t("span",{staticClass:"ml-10"},[e._v("人的团")])],1)],1):e._e()],1),t("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1)],1)],1)},i=[],o=a("5530"),n=(a("ac1f"),a("466d"),a("a434"),a("c1df")),s=a.n(n),l=a("2702"),u=a("2af9"),c=a("acad"),d=[{value:2},{value:3}],p={components:{SelectGoods:u["g"]},data:function(){return{isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},ActiveTypeEnum:c["a"],form:this.$form.createForm(this),defaultDate:[s()(),s()().add(7,"days")],stepsConfig:d,activeType:void 0}},beforeCreate:function(){this.moment=s.a},created:function(){this.activeType=parseInt(this.$route.query.type)},methods:{validatorMockMinPeople:function(e,t,a){var r=this.form,i=this.activeType,o=r.getFieldValue("steps_config");if(t){var n=i!=c["a"].STEPS.value?r.getFieldValue("people"):o[o.length-1];parseInt(t)>=parseInt(n)?a("模拟成团人数应小于最大参团人数"):a()}else a("请填写模拟成团最低参团人数")},validatorStepsConfig:function(e,t,a){var r=this.form.getFieldValue("steps_config"),i=e.field.match(/steps_config\[(\d+)\]/)[1];parseInt(t)<2?a("参团人数最少2人"):parseInt(t)>100?a("参团人数最多100人"):t&&i>0&&parseInt(r[i-1])>=parseInt(t)?a("参团人数需要大于上一级阶梯"):a()},handleAddStepsItem:function(){this.stepsConfig.push({value:void 0})},handleDeleteStepsItem:function(e){this.stepsConfig.splice(e,1)},handleSubmit:function(e){e.preventDefault();this.record;var t=this.form.validateFields,a=this.onFormSubmit;t((function(e,t){!e&&a(t)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,this.isBtnLoading=!0,l["a"]({form:Object(o["a"])(Object(o["a"])({},e),{},{active_type:this.activeType})}).then((function(e){t.$message.success(e.message,1.5),setTimeout((function(){return t.$router.push("./index")}),1200)})).catch((function(){return t.isBtnLoading=!1})).finally((function(){return t.isLoading=!1}))}}},m=p,v=(a("8591"),a("2877")),f=Object(v["a"])(m,r,i,!1,null,"078c8a1a",null);t["default"]=f.exports},c7eb:function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));a("a4d3"),a("e01a"),a("d3b7"),a("d28b"),a("3ca3"),a("ddb0"),a("b636"),a("944a"),a("0c47"),a("23dc"),a("3410"),a("159b"),a("b0c0"),a("131a"),a("fb6a");var r=a("53ca");function i(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
i=function(){return e};var e={},t=Object.prototype,a=t.hasOwnProperty,o=Object.defineProperty||function(e,t,a){e[t]=a.value},n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",l=n.asyncIterator||"@@asyncIterator",u=n.toStringTag||"@@toStringTag";function c(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(O){c=function(e,t,a){return e[t]=a}}function d(e,t,a,r){var i=t&&t.prototype instanceof v?t:v,n=Object.create(i.prototype),s=new q(r||[]);return o(n,"_invoke",{value:k(e,a,s)}),n}function p(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(O){return{type:"throw",arg:O}}}e.wrap=d;var m={};function v(){}function f(){}function h(){}var g={};c(g,s,(function(){return this}));var _=Object.getPrototypeOf,b=_&&_(_(E([])));b&&b!==t&&a.call(b,s)&&(g=b);var y=h.prototype=v.prototype=Object.create(g);function C(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function i(o,n,s,l){var u=p(e[o],e,n);if("throw"!==u.type){var c=u.arg,d=c.value;return d&&"object"==Object(r["a"])(d)&&a.call(d,"__await")?t.resolve(d.__await).then((function(e){i("next",e,s,l)}),(function(e){i("throw",e,s,l)})):t.resolve(d).then((function(e){c.value=e,s(c)}),(function(e){return i("throw",e,s,l)}))}l(u.arg)}var n;o(this,"_invoke",{value:function(e,a){function r(){return new t((function(t,r){i(e,a,t,r)}))}return n=n?n.then(r,r):r()}})}function k(e,t,a){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return D()}for(a.method=i,a.arg=o;;){var n=a.delegate;if(n){var s=S(n,a);if(s){if(s===m)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===r)throw r="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r="executing";var l=p(e,t,a);if("normal"===l.type){if(r=a.done?"completed":"suspendedYield",l.arg===m)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(r="completed",a.method="throw",a.arg=l.arg)}}}function S(e,t){var a=t.method,r=e.iterator[a];if(void 0===r)return t.delegate=null,"throw"===a&&e.iterator["return"]&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==a&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+a+"' method")),m;var i=p(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,m;var o=i.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,m):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,m)}function x(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function q(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(x,this),this.reset(!0)}function E(e){if(e){var t=e[s];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(a.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:D}}function D(){return{value:void 0,done:!0}}return f.prototype=h,o(y,"constructor",{value:h,configurable:!0}),o(h,"constructor",{value:f,configurable:!0}),f.displayName=c(h,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,c(e,u,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},C(w.prototype),c(w.prototype,l,(function(){return this})),e.AsyncIterator=w,e.async=function(t,a,r,i,o){void 0===o&&(o=Promise);var n=new w(d(t,a,r,i),o);return e.isGeneratorFunction(a)?n:n.next().then((function(e){return e.done?e.value:n.next()}))},C(y),c(y,u,"Generator"),c(y,s,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),a=[];for(var r in t)a.push(r);return a.reverse(),function e(){for(;a.length;){var r=a.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=E,q.prototype={constructor:q,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!e)for(var t in this)"t"===t.charAt(0)&&a.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(a,r){return n.type="throw",n.arg=e,t.next=a,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],n=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=a.call(o,"catchLoc"),l=a.call(o,"finallyLoc");if(s&&l){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&a.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var n=o?o.completion:{};return n.type=e,n.arg=t,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(n)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),L(a),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var r=a.completion;if("throw"===r.type){var i=r.arg;L(a)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,a){return this.delegate={iterator:E(e),resultName:t,nextLoc:a},"next"===this.method&&(this.arg=void 0),m}},e}},c9b2:function(e,t,a){"use strict";a.r(t);a("b0c0"),a("ac1f"),a("841c");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{staticClass:"flex flex-x-end"},[t("a-select",{staticStyle:{width:"220px","margin-right":"20px"},attrs:{placeholder:"拼团类型"},model:{value:e.queryParam.activeType,callback:function(t){e.$set(e.queryParam,"activeType",t)},expression:"queryParam.activeType"}},e._l(e.ActiveTypeEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1),t("a-select",{staticStyle:{width:"220px","margin-right":"20px"},attrs:{placeholder:"拼单状态"},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}},e._l(e.TaskStatusEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1),t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px"},attrs:{placeholder:"请输入发起人昵称/商品名称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"task_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15,scroll:{x:1450}},scopedSlots:e._u([{key:"goods",fn:function(e){return[t("GoodsItem",{attrs:{data:{image:e.goods.goods_image,imageAlt:"商品图片",title:e.goods.goods_name,subtitle:"¥".concat(e.groupGoods.groupon_price_min)},subTitleColor:!0}})]}},{key:"active_type",fn:function(a){return t("span",{},[t("a-tag",[e._v(e._s(e.ActiveTypeEnum[a].name))])],1)}},{key:"people",fn:function(a){return t("span",{},[t("p",[e._v(e._s(a)+"人")])])}},{key:"now_people",fn:function(a){return t("span",{},[t("a-tooltip",{attrs:{placement:"top"}},[t("template",{slot:"title"},[t("p",{staticClass:"f-12"},[e._v("真实成员："+e._s(a.actual_people)+"人")]),t("p",{staticClass:"f-12",staticStyle:{"padding-left":"12px"}},[e._v("机器人："+e._s(a.robot_people)+"人")])]),t("a",{attrs:{href:"javascript:void(0);"},on:{click:function(t){return e.handlePeoples(a)}}},[t("p",[e._v(e._s(a.actual_people)+"人")]),a.robot_people?t("p",[e._v(e._s(a.robot_people)+"人")]):e._e()])],2)],1)}},{key:"user",fn:function(e){return[t("UserItem",{attrs:{user:e}})]}},{key:"time",fn:function(a){return t("span",{},[t("p",[e._v(e._s(a.start_time))]),t("p",[e._v(e._s(a.end_time))])])}},{key:"status",fn:function(a){return t("span",{},[t("a-tag",{attrs:{color:e.TaskStatusEnumTagColor[a]}},[e._v(e._s(e.TaskStatusEnum[a].name))])],1)}},{key:"action",fn:function(a,r){return t("span",{staticClass:"actions"},[t("a",{on:{click:function(t){return e.handlePeoples(r)}}},[e._v("拼单成员")])])}}])}),t("PeopleModal",{ref:"PeopleModal",on:{handleSubmit:e.handleRefresh}})],1)},i=[],o=a("5530"),n=a("ade3"),s=a("ab09"),l=a("acad"),u=(a("4a95"),a("b775")),c={list:"/groupon.task/list",users:"/groupon.task/users"};function d(e){return Object(u["b"])({url:c.list,method:"get",params:e})}function p(e,t){return Object(u["b"])({url:c.users,method:"get",params:Object(o["a"])({taskId:e},t)})}var m=function(){var e=this,t=e._self._c;return t("a-modal",{staticClass:"noborder",attrs:{title:e.title,width:920,visible:e.visible,isLoading:e.isLoading,maskClosable:!1,cancelText:!1},on:{ok:e.handleCancel,cancel:e.handleCancel}},[e.taskId?t("s-table",{ref:"table",attrs:{scroll:{y:"420px",scrollToFirstRowOnChange:!0},rowKey:e.fieldName,loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"user",fn:function(a){return t("span",{},[a.is_robot?e._e():t("UserItem",{attrs:{user:a.user}}),a.is_robot?t("UserItem",{attrs:{user:a.robot}}):e._e()],1)}},{key:"identity",fn:function(a){return t("span",{},[a.is_robot?t("a-tag",{attrs:{color:""}},[e._v("机器人")]):t("a-tag",{attrs:{color:a.is_leader?"green":""}},[e._v(e._s(a.is_leader?"团长":"成员"))])],1)}},{key:"action",fn:function(a){return t("span",{staticClass:"actions"},[a.is_robot?e._e():t("router-link",{directives:[{name:"action",rawName:"v-action:order",arg:"order"}],attrs:{target:"_blank",to:{path:"/order/detail",query:{orderId:a.order_id}}}},[e._v("查看订单")])],1)}}],null,!1,3447835808)}):e._e()],1)},v=[],f=[{title:"ID",dataIndex:"id"},{title:"用户信息",scopedSlots:{customRender:"user"}},{title:"成员身份",scopedSlots:{customRender:"identity"}},{title:"参团时间",dataIndex:"create_time"},{title:"操作",scopedSlots:{customRender:"action"}}],h={name:"PeopleModal",components:{STable:s["b"],UserItem:s["c"]},data:function(){var e=this;return{title:"拼单成员列表",visible:!1,isLoading:!1,searchForm:this.$form.createForm(this),queryParam:{},columns:f,loadData:function(t){return p(e.taskId,Object(o["a"])(Object(o["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))},fieldName:"id",taskId:void 0}},methods:{handle:function(e){var t=this;this.visible=!0,this.taskId=e,this.$nextTick((function(){t.handleRefresh(!0)}))},handleRefresh:function(){this.$refs.table.refresh(!0)},handleCancel:function(){this.visible=!1}}},g=h,_=(a("115f"),a("2877")),b=Object(_["a"])(g,m,v,!1,null,"61ec953a",null),y=b.exports,C={name:"Index",components:{STable:s["b"],GoodsItem:s["a"],UserItem:s["c"],PeopleModal:y},data:function(){var e,t=this;return{queryParam:{activeType:void 0,status:void 0,search:""},isLoading:!1,TaskStatusEnum:l["c"],ActiveTypeEnum:l["a"],TaskStatusEnumTagColor:(e={},Object(n["a"])(e,l["c"].NORMAL.value,""),Object(n["a"])(e,l["c"].COMPLETED.value,"green"),Object(n["a"])(e,l["c"].FAIL.value,"red"),e),columns:[{title:"拼单ID",dataIndex:"task_id"},{title:"拼团商品",width:"320px",scopedSlots:{customRender:"goods"}},{title:"拼团类型",dataIndex:"active_type",scopedSlots:{customRender:"active_type"}},{title:"成团人数",dataIndex:"people",scopedSlots:{customRender:"people"}},{title:"已拼人数",scopedSlots:{customRender:"now_people"}},{title:"发起人 (团长)",dataIndex:"user",width:"200px",scopedSlots:{customRender:"user"}},{title:"发起/结束时间",width:"180px",scopedSlots:{customRender:"time"}},{title:"拼单状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"操作",dataIndex:"action",width:"150px",fixed:"right",scopedSlots:{customRender:"action"}}],loadData:function(e){return d(Object(o["a"])(Object(o["a"])({},e),t.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handlePeoples:function(e){this.$refs.PeopleModal.handle(e.task_id)},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},w=C,k=Object(_["a"])(w,r,i,!1,null,null,null);t["default"]=k.exports},d4a2:function(e,t,a){},eaab:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{staticClass:"mt-30",attrs:{label:"拼单失败自动退款",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["taskFailAutoRefund",{rules:[{required:!0}]}],expression:"['taskFailAutoRefund', { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:!0}},[e._v("开启")]),t("a-radio",{attrs:{value:!1}},[e._v("关闭")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("注：拼单如果超时还未拼成，则自动将订单取消并退款")])])],1),e.$module("apps-dealer")?t("a-form-item",{staticClass:"mt-30",attrs:{label:"是否参与分销",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["isDealer",{rules:[{required:!0}]}],expression:"['isDealer', { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:!0}},[e._v("开启")]),t("a-radio",{attrs:{value:!1}},[e._v("关闭")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("注：拼团活动订单是否参与分销（需开启分销功能）")])])],1):e._e(),t("a-form-item",{staticClass:"mt-30",attrs:{label:"是否叠加其他优惠方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["isOtherDiscount",{rules:[{required:!0}]}],expression:"['isOtherDiscount', { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:!0}},[e._v("开启")]),t("a-radio",{attrs:{value:!1}},[e._v("关闭")])],1)],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("isOtherDiscount"),expression:"form.getFieldValue('isOtherDiscount')"}],attrs:{label:"可用优惠方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-checkbox-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["otherDiscount"],expression:"['otherDiscount']"}]},[t("a-checkbox",{attrs:{value:"coupon"}},[e._v("优惠券")]),t("a-checkbox",{attrs:{value:"points"}},[e._v("积分抵扣")])],1)],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("活动说明")]),t("a-form-item",{attrs:{label:"拼团活动图",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("SelectImage2",{directives:[{name:"decorator",rawName:"v-decorator",value:["backdrop.src",{initialValue:"",rules:[{required:!0,message:"请上传拼团活动图"}]}],expression:"['backdrop.src', { initialValue: '', rules: [{ required: true, message: '请上传拼团活动图' }] }]"}],attrs:{width:375,height:184,tips:"建议尺寸：宽750 高度368"}}),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("尺寸：宽750像素 高度不限")])])],1),t("a-form-item",{attrs:{label:"拼团活动简述",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["ruleBrief",{rules:[{required:!0,message:"拼团活动简述"}]}],expression:"['ruleBrief', { rules: [{ required: true, message: '拼团活动简述' }] }]"}]})],1),t("a-form-item",{attrs:{label:"拼团活动规则",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["ruleDetail",{rules:[{required:!0,message:"拼团活动规则"}]}],expression:"['ruleDetail', { rules: [{ required: true, message: '拼团活动规则' }] }]"}],attrs:{"auto-size":{minRows:6,maxRows:12}}})],1),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},i=[],o=(a("d3b7"),a("88bc")),n=a.n(o),s=a("ca00"),l=a("5530"),u=a("b775"),c={detail:"/groupon.setting/detail",update:"/groupon.setting/update"};function d(e){return Object(u["b"])({url:c.detail,method:"get",params:{key:e}})}function p(e,t){return Object(u["b"])({url:c.update,method:"post",data:Object(l["a"])({key:e},t)})}var m=a("2af9"),v={components:{SelectImage2:m["i"]},data:function(){return{labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),key:"basic",record:{}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,d(this.key).then((function(t){e.record=t.data.detail,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,t=this.$nextTick,a=this.form;!Object(s["g"])(a.getFieldsValue())&&t((function(){a.setFieldsValue(n()(e,["taskFailAutoRefund","isDealer","isOtherDiscount","otherDiscount","backdrop","ruleBrief","ruleDetail"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,p(this.key,{form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},f=v,h=(a("8dc9"),a("2877")),g=Object(h["a"])(f,r,i,!1,null,"e659a0e2",null);t["default"]=g.exports},ff9b:function(e,t,a){"use strict";a("b21b")}}]);