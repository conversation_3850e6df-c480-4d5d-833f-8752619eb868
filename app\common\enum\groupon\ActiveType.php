<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\groupon;

use app\common\enum\EnumBasics;

/**
 * 枚举类：拼团类型
 * Class ActiveType
 * @package app\common\enum\groupon
 */
class ActiveType extends EnumBasics
{
    // 普通拼团
    const NORMAL = 10;

    // 老带新拼团
    const PULL_NEW = 20;

    // 阶梯拼团
    const STEPS = 30;

    /**
     * 获取枚举类型值
     * @return array
     */
    public static function data(): array
    {
        return [
            self::NORMAL => [
                'name' => '普通拼团',
                'value' => self::NORMAL,
            ],
            self::PULL_NEW => [
                'name' => '老带新拼团',
                'value' => self::PULL_NEW,
            ],
            self::STEPS => [
                'name' => '阶梯拼团',
                'value' => self::STEPS,
            ]
        ];
    }
}