<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\store\shop;

use cores\BaseModel;
use think\model\relation\BelongsTo;

/**
 * 商家门店店员模型
 * Class Clerk
 * @package app\common\model\store
 */
class Clerk extends BaseModel
{
    // 定义表名
    protected $name = 'store_shop_clerk';

    // 定义主键
    protected $pk = 'clerk_id';

    /**
     * 关联用户表
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        $module = static::getCalledModule();
        return $this->BelongsTo("app\\{$module}\\model\\User");
    }

    /**
     * 关联门店表
     * @return BelongsTo
     */
    public function shop(): BelongsTo
    {
        $module = static::getCalledModule();
        return $this->BelongsTo("app\\{$module}\\model\\store\\Shop", 'shop_id');
    }

    /**
     * 店员详情
     * @param $where
     * @return static|array|null
     */
    public static function detail($where)
    {
        return static::get($where);
    }
}
