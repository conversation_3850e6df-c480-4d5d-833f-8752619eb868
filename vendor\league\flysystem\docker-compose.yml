---
version: "3"
services:
  webdav:
    image: bytemark/webdav
    restart: always
    ports:
      - "80:80"
    environment:
      AUTH_TYPE: Digest
      USERNAME: alice
      PASSWORD: secret1234
  sftp:
    container_name: sftp
    restart: always
    image: atmoz/sftp
    volumes:
      - ./test_files/sftp/users.conf:/etc/sftp/users.conf
      - ./test_files/sftp/ssh_host_ed25519_key:/etc/ssh/ssh_host_ed25519_key
      - ./test_files/sftp/ssh_host_rsa_key:/etc/ssh/ssh_host_rsa_key
      - ./test_files/sftp/id_rsa.pub:/home/<USER>/.ssh/keys/id_rsa.pub
    ports:
      - "2222:22"
  ftp:
    container_name: ftp
    restart: always
    image: delfer/alpine-ftp-server
    environment:
      USERS: 'foo|pass|/home/<USER>/upload'
      ADDRESS: 'localhost'
    ports:
      - "2121:21"
      - "21000-21010:21000-21010"
  ftpd:
    container_name: ftpd
    restart: always
    environment:
      PUBLICHOST: localhost
      FTP_USER_NAME: foo
      FTP_USER_PASS: pass
      FTP_USER_HOME: /home/<USER>
    image: stilliard/pure-ftpd
    ports:
      - "2122:21"
      - "30000-30009:30000-30009"
    command: "/run.sh -l puredb:/etc/pure-ftpd/pureftpd.pdb -E -j -P localhost"
  toxiproxy:
    container_name: toxiproxy
    restart: unless-stopped
    image: ghcr.io/shopify/toxiproxy
    command: "-host 0.0.0.0 -config /opt/toxiproxy/config.json"
    volumes:
      - ./test_files/toxiproxy/toxiproxy.json:/opt/toxiproxy/config.json:ro
    ports:
      - "8474:8474" # HTTP API
      - "8222:8222" # SFTP
      - "8121:8121" # FTP
      - "8122:8122" # FTPD
