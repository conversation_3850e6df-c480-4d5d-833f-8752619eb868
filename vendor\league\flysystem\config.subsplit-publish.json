{"sub-splits": [{"name": "ftp", "directory": "src/Ftp", "target": "**************:thephpleague/flysystem-ftp.git"}, {"name": "sftp", "directory": "src/PhpseclibV2", "target": "**************:thephpleague/flysystem-sftp.git"}, {"name": "sftp-v3", "directory": "src/PhpseclibV3", "target": "**************:thephpleague/flysystem-sftp-v3.git"}, {"name": "memory", "directory": "src/InMemory", "target": "**************:thephpleague/flysystem-memory.git"}, {"name": "ziparchive", "directory": "src/ZipArchive", "target": "**************:thephpleague/flysystem-ziparchive.git"}, {"name": "aws-s3-v3", "directory": "src/AwsS3V3", "target": "**************:thephpleague/flysystem-aws-s3-v3.git"}, {"name": "async-aws-s3", "directory": "src/AsyncAwsS3", "target": "**************:thephpleague/flysystem-async-aws-s3.git"}, {"name": "google-cloud-storage", "directory": "src/GoogleCloudStorage", "target": "**************:thephpleague/flysystem-google-cloud-storage.git"}, {"name": "adapter-test-utilities", "directory": "src/AdapterTestUtilities", "target": "**************:thephpleague/flysystem-adapter-test-utilities.git"}]}