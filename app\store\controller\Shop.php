<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller;

use think\response\Json;
use app\store\model\store\Shop as ShopModel;

/**
 * 门店管理
 * Class Shop
 * @package app\store\controller\store
 */
class Shop extends Controller
{
    /**
     * 门店列表
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function list(): Json
    {
        $model = new ShopModel;
        $list = $model->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 全部记录
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function all(): Json
    {
        $model = new ShopModel;
        $list = $model->getAll($this->request->param());
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 获取详情记录
     * @param int $shopId
     * @return Json
     */
    public function detail(int $shopId): Json
    {
        // 获取门店详情
        $detail = ShopModel::detail($shopId);
        // 获取logoImage (这里不能用with因为编辑页需要logoImage对象)
        !empty($detail) && $detail['logoImage'];
        return $this->renderSuccess(compact('detail'));
    }

    /**
     * 添加门店
     * @return Json
     */
    public function add(): Json
    {
        // 新增记录
        $model = new ShopModel;
        if ($model->add($this->postForm())) {
            return $this->renderSuccess('添加成功');
        }
        return $this->renderError($model->getError() ?: '添加失败');
    }

    /**
     * 编辑门店
     * @param int $shopId
     * @return Json
     */
    public function edit(int $shopId): Json
    {
        // 门店详情
        $model = ShopModel::detail($shopId);
        // 新增记录
        if ($model->edit($this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }

    /**
     * 删除门店
     * @param int $shopId
     * @return Json
     */
    public function delete(int $shopId): Json
    {
        // 门店详情
        $model = ShopModel::detail($shopId);
        if (!$model->setDelete()) {
            return $this->renderError($model->getError() ?: '删除失败');
        }
        return $this->renderSuccess('删除成功');
    }
     /**
     * 用户充值
     * @param int $userId
     * @param string $target
     * @return Json
     */
    public function recharge(int $shopId, string $target): Json
    {
        // 用户详情
        $model = ShopModel::detail($shopId);
        if ($model->recharge($target, $this->postForm())) {
            return $this->renderSuccess('操作成功');
        }
        return $this->renderError($model->getError() ?: '操作失败');
    }
}
