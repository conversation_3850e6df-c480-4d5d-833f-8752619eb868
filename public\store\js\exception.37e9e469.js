(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["exception"],{cc89:function(t,e,o){"use strict";o.r(e);var n=function(){var t=this,e=t._self._c;return e("a-result",{attrs:{status:"404",title:"404","sub-title":"Sorry, the page you visited does not exist."},scopedSlots:t._u([{key:"extra",fn:function(){return[e("a-button",{attrs:{type:"primary"},on:{click:t.toHome}},[t._v("返回首页")])]},proxy:!0}])})},s=[],r={name:"Exception404",methods:{toHome:function(){this.$router.push({path:"/"})}}},u=r,i=o("2877"),a=Object(i["a"])(u,n,s,!1,null,null,null);e["default"]=a.exports}}]);