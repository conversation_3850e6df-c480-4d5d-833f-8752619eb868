<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\groupon;

use think\response\Json;
use app\api\controller\Controller;
use app\api\model\groupon\Setting as SettingModel;

/**
 * 拼团设置
 * Class Setting
 * @package app\store\controller\apps\groupon
 */
class Setting extends Controller
{
    /**
     * 获取拼团设置
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function data(): Json
    {
        // 获取拼团设置
        $setting = SettingModel::getBasic();
        return $this->renderSuccess(compact('setting'));
    }
}