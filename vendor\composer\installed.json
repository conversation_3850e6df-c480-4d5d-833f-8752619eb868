{"packages": [{"name": "adbario/php-dot-notation", "version": "2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/adbario/php-dot-notation.git", "reference": "eee4fc81296531e6aafba4c2bbccfc5adab1676e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/adbario/php-dot-notation/zipball/eee4fc81296531e6aafba4c2bbccfc5adab1676e", "reference": "eee4fc81296531e6aafba4c2bbccfc5adab1676e", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.0|^5.0|^6.0", "squizlabs/php_codesniffer": "^3.0"}, "time": "2019-01-01T23:59:15+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Adbar\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP dot notation access to arrays", "homepage": "https://github.com/adbario/php-dot-notation", "keywords": ["ArrayA<PERSON>ess", "dotnotation"], "support": {"issues": "https://github.com/adbario/php-dot-notation/issues", "source": "https://github.com/adbario/php-dot-notation/tree/2.x"}, "install-path": "../adbario/php-dot-notation"}, {"name": "aferrandini/phpqrcode", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/aferrandini/PHPQRCode.git", "reference": "3c1c0454d43710ab5bbe19a51ad4cb41c22e3d46"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aferrandini/PHPQRCode/zipball/3c1c0454d43710ab5bbe19a51ad4cb41c22e3d46", "reference": "3c1c0454d43710ab5bbe19a51ad4cb41c22e3d46", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2013-07-08T09:39:08+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"PHPQRCode": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.ferrandini.com/", "role": "Developer"}], "description": "PHPQRCode porting and changed for PHP 5.3 compatibility", "homepage": "https://github.com/aferrandini/PHPQRCode", "keywords": ["barcode", "php", "qrcode"], "support": {"issues": "https://github.com/aferrandini/PHPQRCode/issues", "source": "https://github.com/aferrandini/PHPQRCode/tree/master"}, "abandoned": "endroid/qr-code", "install-path": "../aferrandini/phpqrcode"}, {"name": "alibabacloud/tea", "version": "3.1.23", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/aliyun/tea-php.git", "reference": "61fce993274edf6e7131af07256ed7723d97a85f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/tea-php/zipball/61fce993274edf6e7131af07256ed7723d97a85f", "reference": "61fce993274edf6e7131af07256ed7723d97a85f", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.2", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "*", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "time": "2021-12-20T02:32:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Client of Tea for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibabacloud", "client", "cloud", "tea"], "support": {"issues": "https://github.com/aliyun/tea-php/issues", "source": "https://github.com/aliyun/tea-php"}, "install-path": "../alibabacloud/tea"}, {"name": "alibabacloud/tea-fileform", "version": "0.3.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/tea-fileform.git", "reference": "4bf0c75a045c8115aa8cb1a394bd08d8bb833181"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/tea-fileform/zipball/4bf0c75a045c8115aa8cb1a394bd08d8bb833181", "reference": "4bf0c75a045c8115aa8cb1a394bd08d8bb833181", "shasum": ""}, "require": {"alibabacloud/tea": "^3.0", "php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3"}, "time": "2020-12-01T07:24:35+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\FileForm\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Tea File Library for PHP", "support": {"issues": "https://github.com/alibabacloud-sdk-php/tea-fileform/issues", "source": "https://github.com/alibabacloud-sdk-php/tea-fileform/tree/0.3.4"}, "install-path": "../alibabacloud/tea-fileform"}, {"name": "alipaysdk/easysdk", "version": "2.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/alipay/alipay-easysdk.git", "reference": "066388d02c6f55fe0919d75b386456d80801fec2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alipay/alipay-easysdk/zipball/066388d02c6f55fe0919d75b386456d80801fec2", "reference": "066388d02c6f55fe0919d75b386456d80801fec2", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.2", "alibabacloud/tea": "^3.1", "alibabacloud/tea-fileform": "^0.3.2", "danielstjules/stringy": "^3.1", "ext-ctype": "*", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": ">=6.3", "mtdowling/jmespath.php": "^2.4", "php": ">=7.0", "pimple/pimple": "^3.0", "psr/log": "^1.1", "songshenzong/support": "^2.0", "xin/container": "^2.0.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "time": "2021-09-24T06:54:12+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Alipay\\EasySDK\\": "php/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "junying.wjy", "email": "<EMAIL>"}], "description": "支付宝官方 Alipay Easy SDK", "support": {"source": "https://github.com/alipay/alipay-easysdk/tree/v2.2.1"}, "install-path": "../alipaysdk/easysdk"}, {"name": "aliyuncs/oss-sdk-php", "version": "v2.4.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-oss-php-sdk.git", "reference": "4ccead614915ee6685bf30016afb01aabd347e46"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/aliyun-oss-php-sdk/zipball/4ccead614915ee6685bf30016afb01aabd347e46", "reference": "4ccead614915ee6685bf30016afb01aabd347e46", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "*", "satooshi/php-coveralls": "*"}, "time": "2021-08-25T13:03:58+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"OSS\\": "src/OSS"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Aliyuncs", "homepage": "http://www.aliyun.com"}], "description": "Aliyun OSS SDK for PHP", "homepage": "http://www.aliyun.com/product/oss/", "support": {"issues": "https://github.com/aliyun/aliyun-oss-php-sdk/issues", "source": "https://github.com/aliyun/aliyun-oss-php-sdk/tree/v2.4.3"}, "install-path": "../aliyuncs/oss-sdk-php"}, {"name": "danielst<PERSON>les/stringy", "version": "3.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/danielstjules/Stringy.git", "reference": "df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/danielstjules/Stringy/zipball/df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e", "reference": "df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e", "shasum": ""}, "require": {"php": ">=5.4.0", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "time": "2017-06-12T01:10:27+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Stringy\\": "src/"}, "files": ["src/Create.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.danielstjules.com"}], "description": "A string manipulation library with multibyte support", "homepage": "https://github.com/danielstjules/Stringy", "keywords": ["UTF", "helpers", "manipulation", "methods", "multibyte", "string", "utf-8", "utility", "utils"], "support": {"issues": "https://github.com/danielstjules/Stringy/issues", "source": "https://github.com/danielstjules/Stringy"}, "install-path": "../danielstjules/stringy"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-composer/easywechat-composer", "version": "1.4.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mingyoung/easywechat-composer.git", "reference": "3fc6a7ab6d3853c0f4e2922539b56cc37ef361cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mingyoung/easywechat-composer/zipball/3fc6a7ab6d3853c0f4e2922539b56cc37ef361cd", "reference": "3fc6a7ab6d3853c0f4e2922539b56cc37ef361cd", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=7.0"}, "require-dev": {"composer/composer": "^1.0 || ^2.0", "phpunit/phpunit": "^6.5 || ^7.0"}, "time": "2021-07-05T04:03:22+00:00", "type": "composer-plugin", "extra": {"class": "EasyWeChatComposer\\Plugin"}, "installation-source": "dist", "autoload": {"psr-4": {"EasyWeChatComposer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "张铭阳", "email": "mingyoung<PERSON><EMAIL>"}], "description": "The composer plugin for EasyWeChat", "support": {"issues": "https://github.com/mingyoung/easywechat-composer/issues", "source": "https://github.com/mingyoung/easywechat-composer/tree/1.4.1"}, "install-path": "../easywechat-composer/easywechat-composer"}, {"name": "ezyang/htmlpurifier", "version": "v4.14.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "12ab42bd6e742c70c0a52f7b82477fcd44e64b75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/12ab42bd6e742c70c0a52f7b82477fcd44e64b75", "reference": "12ab42bd6e742c70c0a52f7b82477fcd44e64b75", "shasum": ""}, "require": {"php": ">=5.2"}, "time": "2021-12-25T01:21:49+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.14.0"}, "install-path": "../ezyang/htmlpurifier"}, {"name": "guzzlehttp/command", "version": "1.2.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/command.git", "reference": "7883359e0ecab8a8f7c43aad2fc36360a35d21e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/command/zipball/7883359e0ecab8a8f7c43aad2fc36360a35d21e8", "reference": "7883359e0ecab8a8f7c43aad2fc36360a35d21e8", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.4.1", "guzzlehttp/promises": "^1.5.1", "guzzlehttp/psr7": "^1.8.3 || ^2.1", "php": "^7.2.5 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^8.5.19"}, "time": "2022-02-08T10:21:14+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Command\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "description": "Provides the foundation for building command-based web service clients", "support": {"issues": "https://github.com/guzzle/command/issues", "source": "https://github.com/guzzle/command/tree/1.2.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/command", "type": "tidelift"}], "install-path": "../guzzlehttp/command"}, {"name": "guzzlehttp/guzzle", "version": "7.4.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "ac1ec1cd9b5624694c3a40be801d94137afb12b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/ac1ec1cd9b5624694c3a40be801d94137afb12b4", "reference": "ac1ec1cd9b5624694c3a40be801d94137afb12b4", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.8.3 || ^2.1", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "ext-curl": "*", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.5 || ^9.3.5", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "time": "2022-03-20T14:16:28+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "7.4-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.4.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "install-path": "../guzzlehttp/guzzle"}, {"name": "guzzlehttp/guzzle-services", "version": "1.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle-services.git", "reference": "4989d902dd4e0411b320e851c46f3c94d652d891"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle-services/zipball/4989d902dd4e0411b320e851c46f3c94d652d891", "reference": "4989d902dd4e0411b320e851c46f3c94d652d891", "shasum": ""}, "require": {"guzzlehttp/command": "^1.2.2", "guzzlehttp/guzzle": "^7.4.1", "guzzlehttp/psr7": "^1.8.3 || ^2.1", "guzzlehttp/uri-template": "^1.0.1", "php": "^7.2.5 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^8.5.19 || ^9.5.8"}, "suggest": {"gimler/guzzle-description-loader": "^0.0.4"}, "time": "2022-03-03T11:21:34+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Command\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Konafets"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "description": "Provides an implementation of the Guzzle Command library that uses Guzzle service descriptions to describe web services, serialize requests, and parse responses into easy to use model structures.", "support": {"issues": "https://github.com/guzzle/guzzle-services/issues", "source": "https://github.com/guzzle/guzzle-services/tree/1.3.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle-services", "type": "tidelift"}], "install-path": "../guzzlehttp/guzzle-services"}, {"name": "guzzlehttp/promises", "version": "1.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "time": "2021-10-22T20:56:57+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "install-path": "../guzzlehttp/promises"}, {"name": "guzzlehttp/psr7", "version": "2.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c94a94f120803a18554c1805ef2e539f8285f9a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c94a94f120803a18554c1805ef2e539f8285f9a2", "reference": "c94a94f120803a18554c1805ef2e539f8285f9a2", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "time": "2022-03-20T21:55:58+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.2.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "install-path": "../guzzlehttp/psr7"}, {"name": "guzzlehttp/uri-template", "version": "v1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/uri-template.git", "reference": "b945d74a55a25a949158444f09ec0d3c120d69e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/uri-template/zipball/b945d74a55a25a949158444f09ec0d3c120d69e2", "reference": "b945d74a55a25a949158444f09ec0d3c120d69e2", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"phpunit/phpunit": "^8.5.19 || ^9.5.8", "uri-template/tests": "1.0.0"}, "time": "2021-10-07T12:57:01+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\UriTemplate\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "description": "A polyfill class for uri_template of PHP", "keywords": ["guzzlehttp", "uri-template"], "support": {"issues": "https://github.com/guzzle/uri-template/issues", "source": "https://github.com/guzzle/uri-template/tree/v1.0.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/uri-template", "type": "tidelift"}], "install-path": "../guzzlehttp/uri-template"}, {"name": "kosinix/grafika", "version": "dev-master", "version_normalized": "dev-master", "source": {"type": "git", "url": "https://github.com/kosinix/grafika.git", "reference": "211f61fc334b8b36616b23e8af7c5727971d96ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kosinix/grafika/zipball/211f61fc334b8b36616b23e8af7c5727971d96ee", "reference": "211f61fc334b8b36616b23e8af7c5727971d96ee", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3"}, "time": "2017-06-20T03:13:49+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Grafika\\": "src/<PERSON>ika"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT", "GPL-2.0+"], "authors": [{"name": "<PERSON>", "homepage": "https://www.kosinix.com"}], "description": "An image manipulation library for PHP.", "homepage": "http://kosinix.github.io/grafika", "keywords": ["grafika"], "install-path": "../kosinix/grafika"}, {"name": "league/flysystem", "version": "2.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "8aaffb653c5777781b0f7f69a5d937baf7ab6cdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/8aaffb653c5777781b0f7f69a5d937baf7ab6cdb", "reference": "8aaffb653c5777781b0f7f69a5d937baf7ab6cdb", "shasum": ""}, "require": {"ext-json": "*", "league/mime-type-detection": "^1.0.0", "php": "^7.2 || ^8.0"}, "conflict": {"guzzlehttp/ringphp": "<1.1.1"}, "require-dev": {"async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.0", "aws/aws-sdk-php": "^3.132.4", "composer/semver": "^3.0", "ext-fileinfo": "*", "ext-ftp": "*", "friendsofphp/php-cs-fixer": "^3.2", "google/cloud-storage": "^1.23", "phpseclib/phpseclib": "^2.0", "phpstan/phpstan": "^0.12.26", "phpunit/phpunit": "^8.5 || ^9.4", "sabre/dav": "^4.1"}, "time": "2022-09-17T21:02:32+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"League\\Flysystem\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "File storage abstraction for PHP", "keywords": ["WebDAV", "aws", "cloud", "file", "files", "filesystem", "filesystems", "ftp", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.5.0"}, "funding": [{"url": "https://ecologi.com/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "install-path": "../league/flysystem"}, {"name": "league/mime-type-detection", "version": "1.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "ff6248ea87a9f116e78edd6002e39e5128a0d4dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/ff6248ea87a9f116e78edd6002e39e5128a0d4dd", "reference": "ff6248ea87a9f116e78edd6002e39e5128a0d4dd", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3"}, "time": "2022-04-17T13:12:02+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.11.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "install-path": "../league/mime-type-detection"}, {"name": "lvht/geohash", "version": "v1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/lvht/geohash.git", "reference": "bbba3e1b487f0ec2e5e666c1bc9d1d4277990a29"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lvht/geohash/zipball/bbba3e1b487f0ec2e5e666c1bc9d1d4277990a29", "reference": "bbba3e1b487f0ec2e5e666c1bc9d1d4277990a29", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "time": "2017-08-24T11:05:30+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Lvht\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "吕海涛", "email": "************", "homepage": "https://github.com/lvht"}], "description": "geohash like python-geohash", "homepage": "http://github.com/lvht/geohash", "keywords": ["geo<PERSON>h"], "install-path": "../lvht/geohash"}, {"name": "maennchen/zipstream-php", "version": "2.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/c4c5803cc1f93df3d2448478ef79394a5981cc58", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58", "shasum": ""}, "require": {"myclabs/php-enum": "^1.5", "php": ">= 7.1", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "guzzlehttp/guzzle": ">= 6.3", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": ">= 7.5"}, "time": "2020-05-30T13:11:16+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/master"}, "funding": [{"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "install-path": "../maennchen/zipstream-php"}, {"name": "markbaker/complex", "version": "3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "ab8bc271e404909db09ff2d5ffa1e538085c0f22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/ab8bc271e404909db09ff2d5ffa1e538085c0f22", "reference": "ab8bc271e404909db09ff2d5ffa1e538085c0f22", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "squizlabs/php_codesniffer": "^3.4"}, "time": "2021-06-29T15:32:53+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.1"}, "install-path": "../markbaker/complex"}, {"name": "markbaker/matrix", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "c66aefcafb4f6c269510e9ac46b82619a904c576"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/c66aefcafb4f6c269510e9ac46b82619a904c576", "reference": "c66aefcafb4f6c269510e9ac46b82619a904c576", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.4"}, "time": "2021-07-01T19:01:15+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.0"}, "install-path": "../markbaker/matrix"}, {"name": "monolog/monolog", "version": "2.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "4192345e260f1d51b365536199744b987e160edc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/4192345e260f1d51b365536199744b987e160edc", "reference": "4192345e260f1d51b365536199744b987e160edc", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "graylog2/gelf-php": "^1.4.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpstan/phpstan": "^0.12.91", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": ">=0.90@dev", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "time": "2022-04-08T15:43:54+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.5.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "install-path": "../monolog/monolog"}, {"name": "mtdowling/jmespath.php", "version": "2.6.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "9b87907a81b87bc76d19a7fb2d61e61486ee9edb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/9b87907a81b87bc76d19a7fb2d61e61486ee9edb", "reference": "9b87907a81b87bc76d19a7fb2d61e61486ee9edb", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^7.5.15"}, "time": "2021-06-14T00:11:39+00:00", "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"JmesPath\\": "src/"}, "files": ["src/JmesPath.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.6.1"}, "install-path": "../mtdowling/jmespath.php"}, {"name": "myclabs/php-enum", "version": "1.6.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "32c4202886c51fbe5cc3a7c34ec5c9a4a790345e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/32c4202886c51fbe5cc3a7c34ec5c9a4a790345e", "reference": "32c4202886c51fbe5cc3a7c34ec5c9a4a790345e", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.7|^6.0", "squizlabs/php_codesniffer": "1.*"}, "time": "2019-02-04T21:18:49+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/master"}, "install-path": "../myclabs/php-enum"}, {"name": "nesbot/carbon", "version": "2.66.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "496712849902241f04902033b0441b269effe001"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/496712849902241f04902033b0441b269effe001", "reference": "496712849902241f04902033b0441b269effe001", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1.8 || ^8.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4", "doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "*", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "time": "2023-01-29T18:53:47+00:00", "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.x-dev", "dev-master": "2.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "installation-source": "dist", "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "install-path": "../nesbot/carbon"}, {"name": "overtrue/easy-sms", "version": "2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/overtrue/easy-sms.git", "reference": "fda1b6fcde861451ccf54e1071b4e1877455d89a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/easy-sms/zipball/fda1b6fcde861451ccf54e1071b4e1877455d89a", "reference": "fda1b6fcde861451ccf54e1071b4e1877455d89a", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^6.2 || ^7.0", "php": ">=5.6"}, "require-dev": {"brainmaestro/composer-git-hooks": "^2.8", "jetbrains/phpstorm-attributes": "^1.0", "mockery/mockery": "~1.3.3 || ^1.4.2", "phpunit/phpunit": "^5.7 || ^7.5 || ^8.5.19 || ^9.5.8"}, "time": "2022-03-20T15:13:45+00:00", "type": "library", "extra": {"hooks": {"pre-commit": ["composer check-style", "composer psalm", "composer test"], "pre-push": ["composer check-style"]}}, "installation-source": "dist", "autoload": {"psr-4": {"Overtrue\\EasySms\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "The easiest way to send short message.", "support": {"issues": "https://github.com/overtrue/easy-sms/issues", "source": "https://github.com/overtrue/easy-sms/tree/2.2.0"}, "funding": [{"url": "https://github.com/overtrue", "type": "github"}], "install-path": "../overtrue/easy-sms"}, {"name": "overtrue/socialite", "version": "2.0.24", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/overtrue/socialite.git", "reference": "ee7e7b000ec7d64f2b8aba1f6a2eec5cdf3f8bec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/socialite/zipball/ee7e7b000ec7d64f2b8aba1f6a2eec5cdf3f8bec", "reference": "ee7e7b000ec7d64f2b8aba1f6a2eec5cdf3f8bec", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^5.0|^6.0|^7.0", "php": ">=5.6", "symfony/http-foundation": "^2.7|^3.0|^4.0|^5.0"}, "require-dev": {"mockery/mockery": "~1.2", "phpunit/phpunit": "^6.0|^7.0|^8.0|^9.0"}, "time": "2021-05-13T16:04:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Overtrue\\Socialite\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "A collection of OAuth 2 packages that extracts from laravel/socialite.", "keywords": ["login", "o<PERSON>h", "qq", "social", "wechat", "weibo"], "support": {"issues": "https://github.com/overtrue/socialite/issues", "source": "https://github.com/overtrue/socialite/tree/2.0.24"}, "funding": [{"url": "https://www.patreon.com/overtrue", "type": "patreon"}], "install-path": "../overtrue/socialite"}, {"name": "overtrue/wechat", "version": "4.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/w7corp/easywechat.git", "reference": "04a940f97d6812a67bb8d5f2dbaebf9ad78ae776"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/w7corp/easywechat/zipball/04a940f97d6812a67bb8d5f2dbaebf9ad78ae776", "reference": "04a940f97d6812a67bb8d5f2dbaebf9ad78ae776", "shasum": ""}, "require": {"easywechat-composer/easywechat-composer": "^1.1", "ext-fileinfo": "*", "ext-openssl": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.2 || ^7.0", "monolog/monolog": "^1.22 || ^2.0", "overtrue/socialite": "~2.0", "php": ">=7.2", "pimple/pimple": "^3.0", "psr/simple-cache": "^1.0", "symfony/cache": "^3.3 || ^4.3 || ^5.0", "symfony/event-dispatcher": "^4.3 || ^5.0", "symfony/http-foundation": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/psr-http-message-bridge": "^0.3 || ^1.0 || ^2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.15", "mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2.3", "phpstan/phpstan": "^0.12.0", "phpunit/phpunit": "^7.5"}, "time": "2021-12-27T13:56:47+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/Kernel/Support/Helpers.php", "src/Kernel/Helpers.php"], "psr-4": {"EasyWeChat\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "微信SDK", "keywords": ["easywechat", "sdk", "wechat", "weixin", "weixin-sdk"], "support": {"issues": "https://github.com/w7corp/easywechat/issues", "source": "https://github.com/w7corp/easywechat/tree/4.5.0"}, "funding": [{"url": "https://github.com/overtrue", "type": "github"}], "abandoned": "w7corp/easywechat", "install-path": "../overtrue/wechat"}, {"name": "phpoffice/phpspreadsheet", "version": "1.22.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "3a9e29b4f386a08a151a33578e80ef1747037a48"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/3a9e29b4f386a08a151a33578e80ef1747037a48", "reference": "3a9e29b4f386a08a151a33578e80ef1747037a48", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.13", "maennchen/zipstream-php": "^2.1", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^7.3 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "dompdf/dompdf": "^1.0", "friendsofphp/php-cs-fixer": "^3.2", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "8.0.17", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5 || ^9.0", "squizlabs/php_codesniffer": "^3.6", "tecnickcom/tcpdf": "^6.4"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)"}, "time": "2022-02-18T12:57:07+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.22.0"}, "install-path": "../phpoffice/phpspreadsheet"}, {"name": "pimple/pimple", "version": "v3.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silexphp/Pimple.git", "reference": "a94b3a4db7fb774b3d78dad2315ddc07629e1bed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silexphp/Pimple/zipball/a94b3a4db7fb774b3d78dad2315ddc07629e1bed", "reference": "a94b3a4db7fb774b3d78dad2315ddc07629e1bed", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1 || ^2.0"}, "require-dev": {"symfony/phpunit-bridge": "^5.4@dev"}, "time": "2021-10-28T11:13:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.4.x-dev"}}, "installation-source": "dist", "autoload": {"psr-0": {"Pimple": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "<PERSON><PERSON>, a simple Dependency Injection Container", "homepage": "https://pimple.symfony.com", "keywords": ["container", "dependency injection"], "support": {"source": "https://github.com/silexphp/Pimple/tree/v3.5.0"}, "install-path": "../pimple/pimple"}, {"name": "psr/cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T20:24:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "install-path": "../psr/cache"}, {"name": "psr/container", "version": "1.1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "time": "2021-11-05T16:50:12+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "install-path": "../psr/container"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "time": "2019-01-08T18:20:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "install-path": "../psr/event-dispatcher"}, {"name": "psr/http-client", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "time": "2020-06-29T06:28:15+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "install-path": "../psr/http-client"}, {"name": "psr/http-factory", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "time": "2019-04-30T12:38:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/master"}, "install-path": "../psr/http-factory"}, {"name": "psr/http-message", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T14:39:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "install-path": "../psr/http-message"}, {"name": "psr/log", "version": "1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2021-05-03T11:20:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "source", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "install-path": "../psr/log"}, {"name": "psr/simple-cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "time": "2017-10-23T01:57:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "source", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "install-path": "../psr/simple-cache"}, {"name": "qcloud/cos-sdk-v5", "version": "v2.5.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/tencentyun/cos-php-sdk-v5.git", "reference": "3b2f32efd6c7bba7b982b118c5af18db74658fc5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tencentyun/cos-php-sdk-v5/zipball/3b2f32efd6c7bba7b982b118c5af18db74658fc5", "reference": "3b2f32efd6c7bba7b982b118c5af18db74658fc5", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.2.1 || ^7.0", "guzzlehttp/guzzle-services": "^1.1", "guzzlehttp/psr7": "^1.3.1 || ^2.0", "php": ">=5.6"}, "time": "2022-04-07T03:22:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/Common.php"], "psr-4": {"Qcloud\\Cos\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "yaozongyou", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tu<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP SDK for QCloud COS", "keywords": ["cos", "php", "qcloud"], "support": {"issues": "https://github.com/tencentyun/cos-php-sdk-v5/issues", "source": "https://github.com/tencentyun/cos-php-sdk-v5/tree/v2.5.2"}, "install-path": "../qcloud/cos-sdk-v5"}, {"name": "qiniu/php-sdk", "version": "v7.4.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/qiniu/php-sdk.git", "reference": "ee5344f319dd7babd86c9a38e6bd05aa58dc90ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qiniu/php-sdk/zipball/ee5344f319dd7babd86c9a38e6bd05aa58dc90ea", "reference": "ee5344f319dd7babd86c9a38e6bd05aa58dc90ea", "shasum": ""}, "require": {"myclabs/php-enum": "1.6.6", "php": ">=5.3.3"}, "require-dev": {"paragonie/random_compat": ">=2", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~3.6"}, "time": "2022-04-01T08:49:57+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/Qiniu/functions.php"], "psr-4": {"Qiniu\\": "src/<PERSON>iu"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.qiniu.com"}], "description": "Qiniu Resource (Cloud) Storage SDK for PHP", "homepage": "http://developer.qiniu.com/", "keywords": ["cloud", "qiniu", "sdk", "storage"], "support": {"issues": "https://github.com/qiniu/php-sdk/issues", "source": "https://github.com/qiniu/php-sdk/tree/v7.4.3"}, "install-path": "../qiniu/php-sdk"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "time": "2019-03-08T08:55:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "install-path": "../ralouphie/getallheaders"}, {"name": "songshenzong/support", "version": "2.0.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/aliguyong/support.git", "reference": "b334d8abc99e8a85538a556e10c670c18b71c230"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliguyong/support/zipball/b334d8abc99e8a85538a556e10c670c18b71c230", "reference": "b334d8abc99e8a85538a556e10c670c18b71c230", "shasum": ""}, "require": {"danielstjules/stringy": "^3.1", "ext-json": "*", "ext-simplexml": "*", "ext-xml": "*", "php": ">=5.5"}, "require-dev": {"laravel/framework": "^5.8", "phpunit/phpunit": "^4.8.35|^5.4.3"}, "time": "2021-12-29T06:36:20+00:00", "type": "library", "extra": {"laravel": {"providers": ["Songshenzong\\Support\\StringsServiceProvider"], "aliases": {"Strings": "Songshenzong\\Support\\StringsFacade"}}}, "installation-source": "dist", "autoload": {"files": ["src/StringsHelpers.php", "src/BashEchoHelpers.php"], "psr-4": {"Songshenzong\\Support\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Songshenzong Support package.", "homepage": "http://songshenzong.com", "keywords": ["laravel", "support", "tools", "web"], "support": {"issues": "https://github.com/songshenzong/support/issues", "source": "https://github.com/songshenzong/support"}, "install-path": "../songshenzong/support"}, {"name": "symfony/cache", "version": "v5.4.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "ba06841ed293fcaf79a592f59fdaba471f7c756c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/ba06841ed293fcaf79a592f59fdaba471f7c756c", "reference": "ba06841ed293fcaf79a592f59fdaba471f7c756c", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^1.1.7|^2", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<4.4", "symfony/http-kernel": "<4.4", "symfony/var-dumper": "<4.4"}, "provide": {"psr/cache-implementation": "1.0|2.0", "psr/simple-cache-implementation": "1.0|2.0", "symfony/cache-implementation": "1.0|2.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "^1.6|^2.0", "doctrine/dbal": "^2.13.1|^3.0", "predis/predis": "^1.1", "psr/simple-cache": "^1.0|^2.0", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "time": "2022-03-22T15:31:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an extended PSR-6, PSR-16 (and tags) implementation", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v5.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/cache"}, {"name": "symfony/cache-contracts", "version": "v2.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "64be4a7acb83b6f2bf6de9a02cee6dad41277ebc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/64be4a7acb83b6f2bf6de9a02cee6dad41277ebc", "reference": "64be4a7acb83b6f2bf6de9a02cee6dad41277ebc", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0|^3.0"}, "suggest": {"symfony/cache-implementation": ""}, "time": "2022-01-02T09:53:40+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v2.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/cache-contracts"}, {"name": "symfony/deprecation-contracts", "version": "v3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "26954b3d62a6c5fd0ea8a2a00c0353a14978d05c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/26954b3d62a6c5fd0ea8a2a00c0353a14978d05c", "reference": "26954b3d62a6c5fd0ea8a2a00c0353a14978d05c", "shasum": ""}, "require": {"php": ">=8.0.2"}, "time": "2022-01-02T09:55:41+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.0.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/event-dispatcher", "version": "v5.4.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "dec8a9f58d20df252b9cd89f1c6c1530f747685d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/dec8a9f58d20df252b9cd89f1c6c1530f747685d", "reference": "dec8a9f58d20df252b9cd89f1c6c1530f747685d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "time": "2022-01-02T09:53:40+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/event-dispatcher"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "7bc61cc2db649b4637d331240c5346dcc7708051"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7bc61cc2db649b4637d331240c5346dcc7708051", "reference": "7bc61cc2db649b4637d331240c5346dcc7708051", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "time": "2022-01-02T09:55:41+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.0.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/event-dispatcher-contracts"}, {"name": "symfony/http-foundation", "version": "v5.4.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "34e89bc147633c0f9dd6caaaf56da3b806a21465"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/34e89bc147633c0f9dd6caaaf56da3b806a21465", "reference": "34e89bc147633c0f9dd6caaaf56da3b806a21465", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"predis/predis": "~1.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/mime": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/mime": "To use the file extension guesser"}, "time": "2022-03-05T21:03:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v5.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/http-foundation"}, {"name": "symfony/polyfill-mbstring", "version": "v1.25.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "0abb51d2f102e00a4eefcf46ba7fec406d245825"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/0abb51d2f102e00a4eefcf46ba7fec406d245825", "reference": "0abb51d2f102e00a4eefcf46ba7fec406d245825", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2021-11-30T18:21:41+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/polyfill-php72", "version": "v1.25.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "9a142215a36a3888e30d0a9eeea9766764e96976"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/9a142215a36a3888e30d0a9eeea9766764e96976", "reference": "9a142215a36a3888e30d0a9eeea9766764e96976", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2021-05-27T09:17:38+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php72"}, {"name": "symfony/polyfill-php73", "version": "v1.25.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "cc5db0e22b3cb4111010e48785a97f670b350ca5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/cc5db0e22b3cb4111010e48785a97f670b350ca5", "reference": "cc5db0e22b3cb4111010e48785a97f670b350ca5", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2021-06-05T21:20:04+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php73"}, {"name": "symfony/polyfill-php80", "version": "v1.25.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "4407588e0d3f1f52efb65fbe92babe41f37fe50c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/4407588e0d3f1f52efb65fbe92babe41f37fe50c", "reference": "4407588e0d3f1f52efb65fbe92babe41f37fe50c", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2022-03-04T08:16:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php80"}, {"name": "symfony/process", "version": "v4.4.44", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "5cee9cdc4f7805e2699d9fd66991a0e6df8252a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/5cee9cdc4f7805e2699d9fd66991a0e6df8252a2", "reference": "5cee9cdc4f7805e2699d9fd66991a0e6df8252a2", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "time": "2022-06-27T13:16:42+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/process"}, {"name": "symfony/psr-http-message-bridge", "version": "v2.1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "22b37c8a3f6b5d94e9cdbd88e1270d96e2f97b34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/22b37c8a3f6b5d94e9cdbd88e1270d96e2f97b34", "reference": "22b37c8a3f6b5d94e9cdbd88e1270d96e2f97b34", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0", "symfony/http-foundation": "^4.4 || ^5.0 || ^6.0"}, "require-dev": {"nyholm/psr7": "^1.1", "psr/log": "^1.1 || ^2 || ^3", "symfony/browser-kit": "^4.4 || ^5.0 || ^6.0", "symfony/config": "^4.4 || ^5.0 || ^6.0", "symfony/event-dispatcher": "^4.4 || ^5.0 || ^6.0", "symfony/framework-bundle": "^4.4 || ^5.0 || ^6.0", "symfony/http-kernel": "^4.4 || ^5.0 || ^6.0", "symfony/phpunit-bridge": "^5.4@dev || ^6.0"}, "suggest": {"nyholm/psr7": "For a super lightweight PSR-7/17 implementation"}, "time": "2021-11-05T13:13:39+00:00", "type": "symfony-bridge", "extra": {"branch-alias": {"dev-main": "2.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "PSR HTTP message bridge", "homepage": "http://symfony.com", "keywords": ["http", "http-message", "psr-17", "psr-7"], "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.1.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/psr-http-message-bridge"}, {"name": "symfony/service-contracts", "version": "v2.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "24d9dc654b83e91aa59f9d167b131bc3b5bea24c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/24d9dc654b83e91aa59f9d167b131bc3b5bea24c", "reference": "24d9dc654b83e91aa59f9d167b131bc3b5bea24c", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "time": "2022-03-13T20:07:29+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/service-contracts"}, {"name": "symfony/translation", "version": "v6.2.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "60556925a703cfbc1581cde3b3f35b0bb0ea904c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/60556925a703cfbc1581cde3b3f35b0bb0ea904c", "reference": "60556925a703cfbc1581cde3b3f35b0bb0ea904c", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.3|^3.0"}, "conflict": {"symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.13", "psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/finder": "^5.4|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/intl": "^5.4|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^5.4|^6.0"}, "suggest": {"nikic/php-parser": "To use PhpAstExtractor", "psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "time": "2023-01-05T07:00:27+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v6.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/translation"}, {"name": "symfony/translation-contracts", "version": "v3.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "68cce71402305a015f8c1589bfada1280dc64fe7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/68cce71402305a015f8c1589bfada1280dc64fe7", "reference": "68cce71402305a015f8c1589bfada1280dc64fe7", "shasum": ""}, "require": {"php": ">=8.1"}, "suggest": {"symfony/translation-implementation": ""}, "time": "2022-11-25T10:21:52+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.3-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/translation-contracts"}, {"name": "symfony/var-dumper", "version": "v4.4.39", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "35237c5e5dcb6593a46a860ba5b29c1d4683d80e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/35237c5e5dcb6593a46a860ba5b29c1d4683d80e", "reference": "35237c5e5dcb6593a46a860ba5b29c1d4683d80e", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5", "symfony/polyfill-php80": "^1.16"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^3.4|^4.0|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^1.43|^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "time": "2022-02-25T10:38:15+00:00", "bin": ["Resources/bin/var-dump-server"], "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v4.4.39"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/var-dumper"}, {"name": "symfony/var-exporter", "version": "v6.0.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "5f1fddb1b3a8394dbfb234044e3ad620a26e1735"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/5f1fddb1b3a8394dbfb234044e3ad620a26e1735", "reference": "5f1fddb1b3a8394dbfb234044e3ad620a26e1735", "shasum": ""}, "require": {"php": ">=8.0.2"}, "require-dev": {"symfony/var-dumper": "^5.4|^6.0"}, "time": "2022-03-31T17:18:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v6.0.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/var-exporter"}, {"name": "topthink/framework", "version": "v6.1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/framework.git", "reference": "67235be5b919aaaf1de5aed9839f65d8e766aca3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/framework/zipball/67235be5b919aaaf1de5aed9839f65d8e766aca3", "reference": "67235be5b919aaaf1de5aed9839f65d8e766aca3", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": ">=7.2.5", "psr/container": "~1.0", "psr/http-message": "^1.0", "psr/log": "~1.0", "psr/simple-cache": "^1.0", "topthink/think-helper": "^3.1.1", "topthink/think-orm": "^2.0|^3.0"}, "require-dev": {"guzzlehttp/psr7": "^2.1.0", "mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^7.0"}, "time": "2023-02-08T02:24:01+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": [], "psr-4": {"think\\": "src/think/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP Framework.", "homepage": "http://thinkphp.cn/", "keywords": ["framework", "orm", "thinkphp"], "support": {"issues": "https://github.com/top-think/framework/issues", "source": "https://github.com/top-think/framework/tree/v6.1.2"}, "install-path": "../topthink/framework"}, {"name": "topthink/think-filesystem", "version": "v2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-filesystem.git", "reference": "50af34c4cfc9a5cbe8a5e3ac9f4e2aa0fd90693f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-filesystem/zipball/50af34c4cfc9a5cbe8a5e3ac9f4e2aa0fd90693f", "reference": "50af34c4cfc9a5cbe8a5e3ac9f4e2aa0fd90693f", "shasum": ""}, "require": {"league/flysystem": "^2.0", "topthink/framework": "^6.1"}, "require-dev": {"mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^8.0"}, "time": "2023-01-06T14:29:27+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6.1 Filesystem Package", "support": {"issues": "https://github.com/top-think/think-filesystem/issues", "source": "https://github.com/top-think/think-filesystem/tree/v2.0.1"}, "install-path": "../topthink/think-filesystem"}, {"name": "topthink/think-helper", "version": "v3.1.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-helper.git", "reference": "769acbe50a4274327162f9c68ec2e89a38eb2aff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-helper/zipball/769acbe50a4274327162f9c68ec2e89a38eb2aff", "reference": "769acbe50a4274327162f9c68ec2e89a38eb2aff", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "time": "2021-12-15T04:27:55+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Helper Package", "support": {"issues": "https://github.com/top-think/think-helper/issues", "source": "https://github.com/top-think/think-helper/tree/v3.1.6"}, "install-path": "../topthink/think-helper"}, {"name": "topthink/think-multi-app", "version": "v1.0.14", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/top-think/think-multi-app.git", "reference": "ccaad7c2d33f42cb1cc2a78d6610aaec02cea4c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-multi-app/zipball/ccaad7c2d33f42cb1cc2a78d6610aaec02cea4c3", "reference": "ccaad7c2d33f42cb1cc2a78d6610aaec02cea4c3", "shasum": ""}, "require": {"php": ">=7.1.0", "topthink/framework": "^6.0.0"}, "time": "2020-07-12T13:50:37+00:00", "type": "library", "extra": {"think": {"services": ["think\\app\\Service"]}}, "installation-source": "dist", "autoload": {"psr-4": {"think\\app\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp6 multi app support", "support": {"issues": "https://github.com/top-think/think-multi-app/issues", "source": "https://github.com/top-think/think-multi-app/tree/master"}, "install-path": "../topthink/think-multi-app"}, {"name": "topthink/think-orm", "version": "v2.0.53", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/top-think/think-orm.git", "reference": "06783eda65547a70ea686360a897759e1f873fff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-orm/zipball/06783eda65547a70ea686360a897759e1f873fff", "reference": "06783eda65547a70ea686360a897759e1f873fff", "shasum": ""}, "require": {"ext-json": "*", "ext-pdo": "*", "php": ">=7.1.0", "psr/log": "~1.0", "psr/simple-cache": "^1.0", "topthink/think-helper": "^3.1"}, "require-dev": {"phpunit/phpunit": "^7|^8|^9.5"}, "time": "2022-02-28T14:54:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["stubs/load_stubs.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "think orm", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/top-think/think-orm/issues", "source": "https://github.com/top-think/think-orm/tree/v2.0.53"}, "install-path": "../topthink/think-orm"}, {"name": "topthink/think-queue", "version": "v3.0.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-queue.git", "reference": "cded7616e313f9daa55c0ad0de5791f0d1fb3066"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-queue/zipball/cded7616e313f9daa55c0ad0de5791f0d1fb3066", "reference": "cded7616e313f9daa55c0ad0de5791f0d1fb3066", "shasum": ""}, "require": {"ext-json": "*", "nesbot/carbon": "^2.16", "symfony/process": "^4.2", "topthink/framework": "^6.0"}, "require-dev": {"mockery/mockery": "^1.2", "phpunit/phpunit": "^6.2", "topthink/think-migration": "^3.0.0"}, "time": "2021-12-20T08:04:36+00:00", "type": "library", "extra": {"think": {"services": ["think\\queue\\Service"], "config": {"queue": "src/config.php"}}}, "installation-source": "dist", "autoload": {"files": ["src/common.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Queue Package", "support": {"issues": "https://github.com/top-think/think-queue/issues", "source": "https://github.com/top-think/think-queue/tree/v3.0.7"}, "install-path": "../topthink/think-queue"}, {"name": "topthink/think-worker", "version": "v3.0.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-worker.git", "reference": "21dc442aaa50594466ed3ed767af0a68b8b75364"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-worker/zipball/21dc442aaa50594466ed3ed767af0a68b8b75364", "reference": "21dc442aaa50594466ed3ed767af0a68b8b75364", "shasum": ""}, "require": {"ext-fileinfo": "*", "topthink/framework": "^6.0.0", "workerman/gateway-worker": "^3.0.0", "workerman/workerman": "^3.5.23"}, "time": "2021-04-26T15:09:47+00:00", "type": "library", "extra": {"think": {"services": ["think\\worker\\Service"], "config": {"worker": "src/config/worker.php", "worker_server": "src/config/server.php", "gateway_worker": "src/config/gateway.php"}}}, "installation-source": "dist", "autoload": {"psr-4": {"think\\worker\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "workerman extend for thinkphp6.0", "support": {"issues": "https://github.com/top-think/think-worker/issues", "source": "https://github.com/top-think/think-worker/tree/v3.0.6"}, "install-path": "../topthink/think-worker"}, {"name": "wechatpay/wechatpay", "version": "1.4.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wechatpay-apiv3/wechatpay-php.git", "reference": "edbdb6bb19e0818b0576043b265ff1b1e188d668"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wechatpay-apiv3/wechatpay-php/zipball/edbdb6bb19e0818b0576043b265ff1b1e188d668", "reference": "edbdb6bb19e0818b0576043b265ff1b1e188d668", "shasum": ""}, "require": {"ext-curl": "*", "ext-libxml": "*", "ext-openssl": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.5 || ^7.0", "guzzlehttp/uri-template": "^0.2 || ^1.0", "php": ">=7.1.2"}, "require-dev": {"phpstan/phpstan": "^0.12.89 || ^1.0", "phpunit/phpunit": "^7.5 || ^8.5.16 || ^9.3.5"}, "time": "2022-08-19T09:14:39+00:00", "bin": ["bin/CertificateDownloader.php"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"WeChatPay\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/TheNorthMemory"}, {"name": "WeChatPay Community", "homepage": "https://developers.weixin.qq.com/community/pay"}], "description": "[A]Sync Chainable WeChatPay v2&v3's OpenAPI SDK for PHP", "homepage": "https://pay.weixin.qq.com/", "keywords": ["AES-GCM", "aes-ecb", "openapi-chainable", "rsa-oaep", "wechatpay", "xml-builder", "xml-parser"], "support": {"issues": "https://github.com/wechatpay-apiv3/wechatpay-php/issues", "source": "https://github.com/wechatpay-apiv3/wechatpay-php/tree/v1.4.6"}, "install-path": "../wechatpay/wechatpay"}, {"name": "workerman/gateway-worker", "version": "v3.0.22", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/walkor/GatewayWorker.git", "reference": "a615036c482d11f68b693998575e804752ef9068"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/GatewayWorker/zipball/a615036c482d11f68b693998575e804752ef9068", "reference": "a615036c482d11f68b693998575e804752ef9068", "shasum": ""}, "require": {"workerman/workerman": ">=3.5.0"}, "time": "2021-12-23T13:13:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"GatewayWorker\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "homepage": "http://www.workerman.net", "keywords": ["communication", "distributed"], "support": {"issues": "https://github.com/walkor/GatewayWorker/issues", "source": "https://github.com/walkor/GatewayWorker/tree/v3.0.22"}, "funding": [{"url": "https://opencollective.com/walkor", "type": "open_collective"}, {"url": "https://www.patreon.com/walkor", "type": "patreon"}], "install-path": "../workerman/gateway-worker"}, {"name": "workerman/workerman", "version": "v3.5.31", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/walkor/Workerman.git", "reference": "b73ddc45b3c7299f330923a2bde23ca6e974fd96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/Workerman/zipball/b73ddc45b3c7299f330923a2bde23ca6e974fd96", "reference": "b73ddc45b3c7299f330923a2bde23ca6e974fd96", "shasum": ""}, "require": {"php": ">=5.3"}, "suggest": {"ext-event": "For better performance. "}, "time": "2020-08-24T03:49:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Workerman\\": "./"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "description": "An asynchronous event driven PHP framework for easily building fast, scalable network applications.", "homepage": "http://www.workerman.net", "keywords": ["asynchronous", "event-loop"], "support": {"email": "<EMAIL>", "forum": "http://wenda.workerman.net/", "issues": "https://github.com/walkor/workerman/issues", "source": "https://github.com/walkor/workerman", "wiki": "http://doc.workerman.net/"}, "install-path": "../workerman/workerman"}, {"name": "xin/container", "version": "2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://gitee.com/liuxiaojinla/php-container", "reference": "97bb67f87dd851545938a1f2fe0ffbd379e3ff81"}, "require": {"ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "psr/container": "^1.0", "xin/helper": "^1.0"}, "time": "2019-10-21T03:51:25+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"xin\\container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "晋", "email": "<EMAIL>"}], "description": "严格基于PSR11规范实现基础的容器和依赖注入", "install-path": "../xin/container"}, {"name": "xin/helper", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://gitee.com/liuxiaojinla/php-helper", "reference": "02a58132dae2aea2d1c0b8e66f55125969224747"}, "require": {"ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*"}, "time": "2019-06-22T08:28:23+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"xin\\helper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "晋", "email": "<EMAIL>"}], "description": "PHP项目日常开发必备基础库，数组工具类、字符串工具类、数字工具类、函数工具类、服务器工具类、加密工具类", "install-path": "../xin/helper"}, {"name": "yiovo/tp6-cache", "version": "v1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/yiovo/tp6-cache.git", "reference": "31e0b5aaa1315ef85d7e68ed189bdb68406becfe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiovo/tp6-cache/zipball/31e0b5aaa1315ef85d7e68ed189bdb68406becfe", "reference": "31e0b5aaa1315ef85d7e68ed189bdb68406becfe", "shasum": ""}, "require": {"php": ">=7.1.0", "topthink/framework": "^6.0.0"}, "time": "2021-11-27T18:53:07+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"yiovo\\cache\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "yiovo", "email": "<EMAIL>"}], "description": "tp6-cache", "homepage": "https://github.com/yiovo/tp6-captcha", "support": {"issues": "https://github.com/yiovo/tp6-cache/issues", "source": "https://github.com/yiovo/tp6-cache/tree/v1.0.1"}, "install-path": "../yiovo/tp6-cache"}, {"name": "yiovo/tp6-captcha", "version": "v1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/yiovo/tp6-captcha.git", "reference": "ad04954c2c3de274f5bd7549788094ee96e4160e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiovo/tp6-captcha/zipball/ad04954c2c3de274f5bd7549788094ee96e4160e", "reference": "ad04954c2c3de274f5bd7549788094ee96e4160e", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "php": ">=7.1.0", "topthink/framework": "^6.0.0", "yiovo/tp6-cache": "^1.0"}, "time": "2022-04-12T14:26:54+00:00", "type": "library", "extra": {"think": {"config": {"captcha": "src/config.php"}}}, "installation-source": "dist", "autoload": {"psr-4": {"yiovo\\captcha\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "yiovo", "email": "<EMAIL>"}], "description": "tp6-cap<PERSON>a", "homepage": "https://github.com/yiovo/tp6-captcha", "support": {"source": "https://github.com/yiovo/tp6-captcha/tree/v1.1.4"}, "install-path": "../yiovo/tp6-captcha"}], "dev": true, "dev-package-names": ["symfony/polyfill-php72", "symfony/var-dumper"]}