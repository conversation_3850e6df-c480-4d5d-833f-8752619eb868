(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["setting"],{"027d":function(e,t,a){},"04ce":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v("设置支付方式")]),t("a-spin",{attrs:{spinning:e.isLoading}},[e._l(e.options,(function(a,r){return t("div",{key:r,staticClass:"payment-item"},[t("div",{staticClass:"item-client"},[t("div",{staticClass:"name"},[e._v(e._s(a.name))]),t("p",{staticClass:"desc"},[e._v(e._s(a.desc))])]),t("a-table",{staticClass:"item-method",attrs:{rowKey:function(t){return e.rowKey(t.key,a.client)},columns:e.columns,dataSource:a.methods,pagination:!1,bordered:""},scopedSlots:e._u([{key:"method",fn:function(a){return[t("div",{staticClass:"pay-method-item"},[t("span",{staticClass:"pay-icon",style:{color:e.PayMethodIcons[a].color}},[t("a-icon",{staticClass:"icon",class:[a],attrs:{component:e.PayMethodIcons[a].icon}})],1),t("span",[e._v(e._s(e.PaymentMethodEnum[a].name))])])]}},{key:"template",fn:function(a,r){return[r.method===e.PaymentMethodEnum.WECHAT.value?t("a-select",{staticClass:"select-template",attrs:{placeholder:"请选择支付模板"},model:{value:r.template_id,callback:function(t){e.$set(r,"template_id",t)},expression:"item.template_id"}},[t("a-select-option",{attrs:{value:0}},[e._v("无")]),e._l(e.wechatTemplateList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.template_id}},[e._v(e._s(a.name))])}))],2):e._e(),r.method===e.PaymentMethodEnum.ALIPAY.value?t("a-select",{staticClass:"select-template",attrs:{placeholder:"请选择支付模板"},model:{value:r.template_id,callback:function(t){e.$set(r,"template_id",t)},expression:"item.template_id"}},[t("a-select-option",{attrs:{value:0}},[e._v("无")]),e._l(e.alipayTemplateList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.template_id}},[e._v(e._s(a.name))])}))],2):e._e(),r.is_must_template&&e.$auth(e.createTemplatePath)?t("router-link",{staticClass:"ml-15 f-12",attrs:{to:{path:e.createTemplatePath},target:"_blank"}},[e._v("新增模板")]):e._e(),r.is_must_template?e._e():t("span",{staticClass:"f-12 c-muted-9"},[e._v("无需支付模板")])]}},{key:"enable",fn:function(a,r){return[t("a-switch",{attrs:{size:"small"},model:{value:r.is_enable,callback:function(t){e.$set(r,"is_enable",t)},expression:"item.is_enable"}})]}},{key:"default",fn:function(r,i,n){return[t("a-switch",{attrs:{size:"small",checked:i.is_default},on:{change:function(t){return e.onChangeDefault(t,n,a.methods)}}})]}}],null,!0)})],1)})),t("a-row",[t("a-col",{attrs:{offset:7}},[t("a-button",{attrs:{type:"primary",loading:e.isBtnLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],2)],1)},i=[],n=a("c7eb"),o=a("1da1"),l=a("ade3"),s=(a("4de4"),a("d3b7"),a("99af"),a("159b"),a("04b3")),c=a("b775"),d={options:"/setting.payment/options",update:"/setting.payment/update"};function u(e){return Object(c["b"])({url:d.options,method:"get",params:e})}function p(e){return Object(c["b"])({url:d.update,method:"post",data:e})}var m,f=a("759f"),h=a("3f9c"),v=(m={},Object(l["a"])(m,h["a"].ALIPAY.value,{icon:s["payAlipay"],color:"#009fe8"}),Object(l["a"])(m,h["a"].BALANCE.value,{icon:s["payBalance"],color:"#e8a807"}),Object(l["a"])(m,h["a"].WECHAT.value,{icon:s["payWechat"],color:"#59b64c"}),m),g=[{title:"支付方式",dataIndex:"method",width:"25%",scopedSlots:{customRender:"method"}},{title:"支付模板",dataIndex:"template_id",width:"35%",scopedSlots:{customRender:"template"}},{title:"是否启用",dataIndex:"is_enable",width:"20%",scopedSlots:{customRender:"enable"}},{title:"是否为默认支付",dataIndex:"is_default",width:"20%",scopedSlots:{customRender:"default"}}],b={components:{},data:function(){return{isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},PaymentMethodEnum:h["a"],PayMethodIcons:v,createTemplatePath:"/setting/payment/template/create",columns:g,options:{},templateList:[]}},computed:{wechatTemplateList:function(){return this.templateList.filter((function(e){return"wechat"===e.method}))},alipayTemplateList:function(){return this.templateList.filter((function(e){return"alipay"===e.method}))}},created:function(){var e=this;return Object(o["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getDetail();case 2:return t.next=4,e.getTemplateList();case 4:case"end":return t.stop()}}),t)})))()},methods:{getDetail:function(){var e=this;return Object(o["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.isLoading=!0,u(e.templateId).then((function(t){return e.options=t.data.options})).finally((function(){return e.isLoading=!1}));case 2:case"end":return t.stop()}}),t)})))()},getTemplateList:function(){var e=this;return Object(o["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.abrupt("return",f["b"]().then((function(t){return e.templateList=t.data.list})).finally((function(){return e.isLoading=!1})));case 2:case"end":return t.stop()}}),t)})))()},rowKey:function(e,t){return"".concat(e,"-").concat(t)},onChangeDefault:function(e,t,a){a.forEach((function(e){return e.is_default=!1})),a[t].is_default=e},handleSubmit:function(e){var t=this;e.preventDefault(),this.isLoading=!0,this.isBtnLoading=!0,p({form:this.options}).then((function(e){t.$message.success(e.message,1.5),t.getDetail()})).finally((function(){t.isLoading=!1,t.isBtnLoading=!1}))}}},y=b,C=(a("938c"),a("2877")),_=Object(C["a"])(y,r,i,!1,null,"1d463747",null);t["default"]=_.exports},"08e2":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form-model",{ref:"myForm",staticClass:"my-form",attrs:{model:e.record,labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-form-model-item",{attrs:{label:"开启商城客服",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-radio-group",{model:{value:e.record.enabled,callback:function(t){e.$set(e.record,"enabled",t)},expression:"record.enabled"}},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("p",{staticClass:"form-item-help"},[t("small",[e._v("开启后将在用户端商品详情页、个人中心页显示在线客服按钮")])])],1),1==e.record.enabled?t("div",[t("a-form-model-item",{attrs:{label:"在线客服方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-radio-group",{model:{value:e.record.provider,callback:function(t){e.$set(e.record,"provider",t)},expression:"record.provider"}},[t("a-radio",{attrs:{value:"mpwxkf"}},[e._v("微信小程序客服")]),t("a-radio",{attrs:{value:"wxqykf"}},[e._v("企业微信客服")])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"mpwxkf"===e.record.provider,expression:"record.provider === 'mpwxkf'"}],staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("仅支持微信小程序端，H5端、APP端等其他端不支持")]),t("p",{staticClass:"extra"},[t("span",[e._v("网页版客服工具：")]),t("a",{attrs:{href:"https://mpkf.weixin.qq.com",target:"_blank"}},[e._v("https://mpkf.weixin.qq.com")])])]),t("div",{directives:[{name:"show",rawName:"v-show",value:"wxqykf"===e.record.provider,expression:"record.provider === 'wxqykf'"}],staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("支持微信小程序端、H5端、APP端")]),t("p",{staticClass:"extra"},[t("span",[e._v("网页版客服工具：")]),t("a",{attrs:{href:"https://work.weixin.qq.com/kf/helpdesk",target:"_blank"}},[e._v("https://work.weixin.qq.com/kf/helpdesk")])])])],1),"wxqykf"===e.record.provider?t("div",[t("a-form-model-item",{attrs:{label:"客服链接",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"config.wxqykf.url",rules:{required:!0,message:"请填写客服链接"}}},[t("a-input",{model:{value:e.record.config.wxqykf.url,callback:function(t){e.$set(e.record.config.wxqykf,"url",t)},expression:"record.config.wxqykf.url"}})],1),t("a-form-model-item",{attrs:{label:"企业ID",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"config.wxqykf.corpId",rules:{required:!0,message:"请填写企业ID"}}},[t("a-input",{model:{value:e.record.config.wxqykf.corpId,callback:function(t){e.$set(e.record.config.wxqykf,"corpId",t)},expression:"record.config.wxqykf.corpId"}})],1)],1):e._e()],1):e._e(),t("a-form-model-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary",loading:e.confirmLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)],1)},i=[],n=(a("d3b7"),a("ddb0"),a("2ef0")),o=a("f585"),l=a("35c4"),s={enabled:1,provider:"mpwxkf",config:{mpwxkf:{},wxqykf:{url:"",corpId:""}}},c={data:function(){return{key:l["a"].CUSTOMER.value,labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,confirmLoading:!1,record:Object(n["cloneDeep"])(s)}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,o["a"](this.key).then((function(t){return e.record=t.data.values})).finally((function(){return e.isLoading=!1}))},handleSubmit:function(e){var t=this;this.$refs.myForm.validate((function(e){e&&(t.confirmLoading=!0,o["b"](t.key,{form:t.record}).then((function(e){t.$message.success(e.message,1.5)})).finally((function(e){return t.confirmLoading=!1})))}))}}},d=c,u=(a("19f3"),a("2877")),p=Object(u["a"])(d,r,i,!1,null,"16c6d0b0",null);t["default"]=p.exports},"0b43a":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"配送方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"用户下单时可以选择的配送方式"}},[t("a-checkbox-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["delivery_type",{rules:[{required:!0,message:"配送方式至少选择一个"}]}],expression:"['delivery_type', { rules: [{ required: true, message: '配送方式至少选择一个' }] }]"}]},[t("a-checkbox",{attrs:{value:10}},[e._v("快递配送")]),t("a-checkbox",{attrs:{value:20}},[e._v("上门自提")])],1)],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("物流轨迹API")]),t("a-form-item",{attrs:{label:"物流查询功能",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"开启后用户可在订单页查询物流轨迹信息"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["traces.enable",{rules:[{required:!0}]}],expression:"['traces.enable', { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("traces.enable"),expression:"form.getFieldValue('traces.enable')"}]},[t("a-form-item",{attrs:{label:"API 服务网关",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["traces.default",{rules:[{required:!0}]}],expression:"['traces.default', { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:"kd100"}},[e._v("快递100")]),t("a-radio",{attrs:{value:"aliyun"}},[e._v("阿里云")])],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"kd100"===e.form.getFieldValue("traces.default"),expression:"form.getFieldValue('traces.default') === 'kd100'"}]},[t("a-form-item",{attrs:{label:"快递100 Customer",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["traces.providerConfig.kd100.customer"],expression:"['traces.providerConfig.kd100.customer']"}]}),t("div",{staticClass:"form-item-help"},[t("small",{staticClass:"mr-5"},[e._v("需申请快递100企业版API")]),t("a",{attrs:{href:"https://api.kuaidi100.com/home",target:"_blank"}},[e._v("去申请")])])],1),t("a-form-item",{attrs:{label:"快递100 Key",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["traces.providerConfig.kd100.key"],expression:"['traces.providerConfig.kd100.key']"}]})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"aliyun"===e.form.getFieldValue("traces.default"),expression:"form.getFieldValue('traces.default') === 'aliyun'"}]},[t("a-form-item",{attrs:{label:"阿里云 AppCode",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["traces.providerConfig.aliyun.appCode"],expression:"['traces.providerConfig.aliyun.appCode']"}]}),t("div",{staticClass:"form-item-help"},[t("small",{staticClass:"mr-5"},[e._v("需申请阿里云物流查询API")]),t("a",{attrs:{href:"https://market.aliyun.com/products/57126001/cmapi023201.html",target:"_blank"}},[e._v("去申请")])])],1)],1)],1),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},i=[],n=(a("d3b7"),a("ddb0"),a("88bc")),o=a.n(n),l=a("f585"),s=a("ca00"),c={data:function(){return{key:"delivery",labelCol:{span:3},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),record:{}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,l["a"](this.key).then((function(t){e.record=t.data.values,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,t=this.$nextTick,a=this.form;!Object(s["g"])(a.getFieldsValue())&&t((function(){a.setFieldsValue(o()(e,["delivery_type","traces"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,l["b"](this.key,{form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},d=c,u=(a("5d09"),a("2877")),p=Object(u["a"])(d,r,i,!1,null,"4479d0fc",null);t["default"]=p.exports},"10dc":function(e,t,a){},"19f3":function(e,t,a){"use strict";a("10dc")},"1da1":function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));a("d3b7");function r(e,t,a,r,i,n,o){try{var l=e[n](o),s=l.value}catch(c){return void a(c)}l.done?t(s):Promise.resolve(s).then(r,i)}function i(e){return function(){var t=this,a=arguments;return new Promise((function(i,n){var o=e.apply(t,a);function l(e){r(o,i,n,l,s,"next",e)}function s(e){r(o,i,n,l,s,"throw",e)}l(void 0)}))}}},"25a8":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"是否开启小票打印",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_open",{initialValue:1,rules:[{required:!0}]}],expression:"['is_open', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),t("a-form-item",{attrs:{label:"订单打印机",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_id",{rules:[{required:!0,message:"订单打印机至少选择一个"}]}],expression:"['printer_id', { rules: [{ required: true, message: '订单打印机至少选择一个' }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请选择订单打印机"}},e._l(e.printerList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.printer_id}},[e._v(e._s(a.printer_name))])})),1)],1),t("a-form-item",{attrs:{label:"订单打印方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-checkbox-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["order_status",{rules:[{required:!0,message:"订单打印方式至少选择一个"}]}],expression:"['order_status', { rules: [{ required: true, message: '订单打印方式至少选择一个' }] }]"}]},[t("a-checkbox",{attrs:{value:20}},[e._v("订单付款时")])],1)],1),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},i=[],n=a("c7eb"),o=a("1da1"),l=(a("d3b7"),a("ddb0"),a("88bc")),s=a.n(l),c=a("f585"),d=a("f50c"),u=a("71b5"),p=a("ca00"),m={data:function(){return{key:"printer",labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),record:{},printerList:[],StorageEnum:u["a"]}},created:function(){var e=this;return Object(o["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getDetail();case 2:return t.next=4,e.getPrinterList();case 4:e.setFieldsValue();case 5:case"end":return t.stop()}}),t)})))()},methods:{getDetail:function(){var e=this;return Object(o["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.abrupt("return",c["a"](e.key).then((function(t){e.record=t.data.values})).finally((function(){return e.isLoading=!1})));case 2:case"end":return t.stop()}}),t)})))()},getPrinterList:function(){var e=this;return Object(o["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.abrupt("return",d["b"]().then((function(t){e.printerList=t.data.list})).finally((function(){return e.isLoading=!1})));case 2:case"end":return t.stop()}}),t)})))()},setFieldsValue:function(){var e=this.record,t=this.$nextTick,a=this.form;!Object(p["g"])(a.getFieldsValue())&&t((function(){var t=s()(e,["is_open","printer_id","order_status"]);t.printer_id=parseInt(t.printer_id),!t.printer_id&&delete t.printer_id,a.setFieldsValue(t)}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,c["b"](this.key,{form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},f=m,h=(a("551e"),a("2877")),v=Object(h["a"])(f,r,i,!1,null,"29ac8f6d",null);t["default"]=v.exports},"2a66":function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"b",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"d",(function(){return s})),a.d(t,"c",(function(){return c}));var r=a("b775"),i={list:"/setting.express/list",all:"/setting.express/all",add:"/setting.express/add",edit:"/setting.express/edit",delete:"/setting.express/delete"};function n(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function o(e){return Object(r["b"])({url:i.all,method:"get",params:e})}function l(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function s(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function c(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},3267:function(e,t,a){"use strict";var r=a("5c06");t["a"]=new r["a"]([{key:"CAPTCHA",name:"短信验证码",value:"captcha"},{key:"ORDER_PAY",name:"新付款订单",value:"order_pay"}])},"34e6":function(e,t,a){"use strict";a("5c49")},"35c4":function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));var r=a("5c06"),i=new r["a"]([{key:"DELIVERY",name:"配送设置",value:"delivery"},{key:"TRADE",name:"交易设置",value:"trade"},{key:"STORAGE",name:"上传设置",value:"storage"},{key:"PRINTER",name:"小票打印",value:"printer"},{key:"FULL_FREE",name:"满额包邮设置",value:"full_free"},{key:"RECHARGE",name:"充值设置",value:"recharge"},{key:"POINTS",name:"积分设置",value:"points"},{key:"SUBMSG",name:"订阅消息设置",value:"submsg"},{key:"APP_THEME",name:"店铺页面风格",value:"app_theme"},{key:"PAGE_CATEGORY_TEMPLATE",name:"分类页模板",value:"page_category_template"},{key:"RECOMMENDED",name:"商品推荐设置",value:"recommended"},{key:"CUSTOMER",name:"商城客服设置",value:"customer"}])},3833:function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"模版名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入模版名称"}})],1),t("a-form-item",{attrs:{label:"计费方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["method",{initialValue:10,rules:[{required:!0}]}],expression:"['method', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:e.onChangemMethod}},[t("a-radio",{attrs:{value:10}},[e._v("按件数")]),t("a-radio",{attrs:{value:20}},[e._v("按重量")])],1)],1),t("a-form-item",{attrs:{label:"配送区域及运费",labelCol:e.labelCol,wrapperCol:{span:15},required:""}},[t("a-table",{directives:[{name:"show",rawName:"v-show",value:e.ruleList.length,expression:"ruleList.length"}],staticClass:"table-rules",attrs:{columns:e.columns,dataSource:e.ruleList,pagination:!1,bordered:""},scopedSlots:e._u([{key:"region_text",fn:function(a,r,i){return[t("p",{staticClass:"content"},e._l(a,(function(a,r){return t("span",{key:r},[t("span",[e._v(e._s(a.name))]),a.citys.length?[t("span",[e._v("(")]),e._l(a.citys,(function(r,i){return t("span",{key:i,staticClass:"city-name"},[e._v(e._s(r.name)+e._s(a.citys.length>i+1?"、":""))])})),t("span",[e._v(")")])]:e._e(),t("span",[e._v(e._s(" "))])],2)})),0),t("p",{staticClass:"operation"},[t("a",{staticClass:"edit",attrs:{href:"javascript:void(0);"},on:{click:function(t){return e.handleEdit(i,r)}}},[e._v("编辑")]),t("a",{staticClass:"delete",attrs:{href:"javascript:void(0);"},on:{click:function(t){return e.handleDelete(i)}}},[e._v("删除")])])]}},{key:"first",fn:function(a,r){return[t("a-input-number",{attrs:{min:10==e.method?1:.01,precision:10==e.method?0:2},model:{value:r.first,callback:function(t){e.$set(r,"first",t)},expression:"item.first"}})]}},{key:"first_fee",fn:function(a,r){return[t("a-input-number",{attrs:{min:0,precision:2},model:{value:r.first_fee,callback:function(t){e.$set(r,"first_fee",t)},expression:"item.first_fee"}})]}},{key:"additional",fn:function(a,r){return[t("a-input-number",{attrs:{min:0,precision:10==e.method?0:2},model:{value:r.additional,callback:function(t){e.$set(r,"additional",t)},expression:"item.additional"}})]}},{key:"additional_fee",fn:function(a,r){return[t("a-input-number",{attrs:{min:0,precision:2},model:{value:r.additional_fee,callback:function(t){e.$set(r,"additional_fee",t)},expression:"item.additional_fee"}})]}}])}),t("a-button",{attrs:{icon:"environment"},on:{click:e.handleAdd}},[e._v("点击添加配送区域")])],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1),t("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1),t("AreasModal",{ref:"AreasModal",on:{handleSubmit:e.handleAreaSubmit}})],1)],1)},i=[],n=a("5530"),o=(a("d81d"),a("d3b7"),a("159b"),a("a434"),a("88bc")),l=a.n(o),s=a("ca00"),c=a("fd0d"),d=a("967a"),u=a("caec"),p={key:0,first:1,first_fee:0,additional:0,additional_fee:0,region:[],region_text:[]},m={components:{AreasModal:c["a"]},data:function(){return{method:10,columns:[{title:"可配送区域",dataIndex:"region_text",width:"400px",scopedSlots:{customRender:"region_text"}},{title:"首件 (个)",dataIndex:"first",scopedSlots:{customRender:"first"}},{title:"运费(元)",dataIndex:"first_fee",scopedSlots:{customRender:"first_fee"}},{title:"续件 (个)",dataIndex:"additional",scopedSlots:{customRender:"additional"}},{title:"续费(元)",dataIndex:"additional_fee",scopedSlots:{customRender:"additional_fee"}}],ruleList:[],isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},form:this.$form.createForm(this),citysCount:null,deliveryId:null,record:{}}},created:function(){var e=this;this.deliveryId=this.$route.query.deliveryId,this.getDetail(),u["a"].getCitysCount().then((function(t){e.citysCount=t}))},watch:{method:function(e){this.updateMethod()}},methods:{getDetail:function(){var e=this,t=this.deliveryId,a=this.form;this.isLoading=!0,d["d"]({deliveryId:t}).then((function(t){var r=t.data.detail;!Object(s["g"])(a.getFieldsValue())&&a.setFieldsValue(l()(r,["name","method","sort"])),e.ruleList=r.rule.map((function(e,t){return Object(n["a"])(Object(n["a"])({},e),{},{key:t})})),e.method=r.method,e.record=r,e.isLoading=!1}))},onChangemMethod:function(e){this.method=e.target.value},updateMethod:function(){var e={10:{first:"首件 (个)",additional:"续件 (个)"},20:{first:"首重 (Kg)",additional:"续重 (Kg)"}};this.columns[1].title=e[this.method].first,this.columns[3].title=e[this.method].additional},handleAdd:function(){var e=this.ruleList.length,t=Object(n["a"])(Object(n["a"])({},p),{},{key:e}),a=this.getExcludedCityIds();if(a.length===this.citysCount)return this.$message.error("已选择了所有的区域",.8),!1;this.handleAreasModal("add",e,t,a)},handleEdit:function(e,t){var a=this.getExcludedCityIds();this.handleAreasModal("edit",e,t,a)},handleAreasModal:function(e,t,a,r){this.$refs.AreasModal.handle({scene:e,index:t,item:a},a.region,r)},getExcludedCityIds:function(){var e=[];return this.ruleList.forEach((function(t){t.region.forEach((function(t){e.push(t)}))})),e},handleAreaSubmit:function(e){var t=e.custom,a=t.scene,r=t.item;r.region=e.selectedCityIds,r.region_text=e.selectedText,"add"===a&&this.ruleList.push(r)},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",onOk:function(){t.ruleList.splice(e,1),a.destroy()}})},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields,r=this.ruleList;a((function(e,a){return!e&&(0===r.length?(t.$message.error("您还没有添加配送区域及运费",.8),!1):(a.rules=r,void t.onFormSubmit(a)))}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,this.isBtnLoading=!0,d["e"]({deliveryId:this.deliveryId,form:e}).then((function(e){t.$message.success(e.message,1.5),setTimeout((function(){t.$router.push("./index")}),1500)})).catch((function(){t.isBtnLoading=!1})).finally((function(){return t.isLoading=!1}))}}},f=m,h=(a("7910"),a("2877")),v=Object(h["a"])(f,r,i,!1,null,null,null);t["default"]=v.exports},"3f9c":function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));var r=a("5c06"),i=new r["a"]([{key:"WECHAT",name:"微信支付",value:"wechat"},{key:"ALIPAY",name:"支付宝",value:"alipay"},{key:"BALANCE",name:"余额支付",value:"balance"}])},4606:function(e,t,a){"use strict";a.r(t);a("b0c0"),a("4e82");var r,i=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form-model",{ref:"myForm",staticClass:"my-form",attrs:{model:e.record,"label-col":e.labelCol,wrapperCol:e.wrapperCol}},[t("a-form-model-item",{attrs:{label:"支付模板名称",prop:"name",rules:[{required:!0,message:"请填写支付模板名称"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.name,callback:function(t){e.$set(e.record,"name",t)},expression:"record.name"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("仅用于后台管理使用，对前台用户不可见；例如：H5端-支付宝支付；微信小程序端-微信支付")])])],1),t("a-form-model-item",{attrs:{label:"管理员备注",prop:"remarks"}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.remarks,callback:function(t){e.$set(e.record,"remarks",t)},expression:"record.remarks"}})],1),t("a-form-model-item",{staticClass:"mb-30",attrs:{label:"排序",prop:"sort",rules:[{required:!0,message:"请填写排序数值"}]}},[t("a-input-number",{attrs:{min:0,autocomplete:"off"},model:{value:e.record.sort,callback:function(t){e.$set(e.record,"sort",t)},expression:"record.sort"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("数字越小越靠前")])])],1),t("a-form-model-item",{attrs:{label:"支付方式",prop:"method",rules:[{required:!0,message:"请选择支付方式"}]}},[t("a-radio-group",{attrs:{disabled:""},model:{value:e.record.method,callback:function(t){e.$set(e.record,"method",t)},expression:"record.method"}},[t("a-radio",{attrs:{value:e.PaymentMethodEnum.WECHAT.value}},[e._v(e._s(e.PaymentMethodEnum.WECHAT.name))]),t("a-radio",{attrs:{value:e.PaymentMethodEnum.ALIPAY.value}},[e._v(e._s(e.PaymentMethodEnum.ALIPAY.name))])],1),t("div",{staticClass:"form-item-help"},[e.record.method===e.PaymentMethodEnum.WECHAT.value?t("p",{staticClass:"extra"},[e._v(" 微信支付商户平台： "),t("a",{attrs:{href:"https://pay.weixin.qq.com",target:"_blank"}},[e._v("https://pay.weixin.qq.com")])]):e._e(),e.record.method===e.PaymentMethodEnum.ALIPAY.value?t("p",{staticClass:"extra"},[e._v(" 支付宝开发者平台： "),t("a",{attrs:{href:"https://open.alipay.com/dev/workspace",target:"_blank"}},[e._v("https://open.alipay.com/dev/workspace")])]):e._e()])],1),e.record.method===e.PaymentMethodEnum.WECHAT.value?t("div",{attrs:{method:e.record.method}},[t("a-form-model-item",{attrs:{label:"微信支付接口版本",prop:"config.wechat.version",rules:[{required:!0,message:"请选择微信支付接口版本"}]}},[t("a-radio-group",{on:{change:function(t){return e.clearValidate()}},model:{value:e.record.config.wechat.version,callback:function(t){e.$set(e.record.config.wechat,"version",t)},expression:"record.config.wechat.version"}},[t("a-radio",{attrs:{value:"v3"}},[t("span",[e._v("V3")]),t("a-tag",{staticClass:"ml-5",attrs:{color:"green"}},[e._v("推荐")])],1),t("a-radio",{attrs:{value:"v2"}},[e._v("V2")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("V2版本较老已经不再支持新出的API接口，强烈建议使用V3")])])],1),t("a-form-model-item",{attrs:{label:"微信商户号类型",prop:"config.wechat.mchType",rules:[{required:!0,message:"请选择微信商户号类型"}]}},[t("a-radio-group",{on:{change:function(t){return e.clearValidate()}},model:{value:e.record.config.wechat.mchType,callback:function(t){e.$set(e.record.config.wechat,"mchType",t)},expression:"record.config.wechat.mchType"}},[t("a-radio",{attrs:{value:"normal"}},[e._v("普通商户")]),t("a-radio",{attrs:{value:"provider"}},[e._v("子商户 (服务商模式)")])],1)],1),"normal"===e.record.config.wechat.mchType?t("div",{attrs:{mchType:e.record.config.wechat.mchType}},[t("a-form-model-item",{attrs:{label:"应用ID (AppID)",prop:"config.wechat.normal.appId",rules:[{required:!0,message:"请填写应用ID (AppID)"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.config.wechat.normal.appId,callback:function(t){e.$set(e.record.config.wechat.normal,"appId",t)},expression:"record.config.wechat.normal.appId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("微信小程序或者微信公众号的APPID，需要在哪个客户端支付就填写哪个，APP支付需要填写开放平台的应用APPID")])])],1),t("a-form-model-item",{attrs:{label:"微信商户号 (MchId)",prop:"config.wechat.normal.mchId",rules:[{required:!0,message:"请填写微信商户号 (MchId)"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.config.wechat.normal.mchId,callback:function(t){e.$set(e.record.config.wechat.normal,"mchId",t)},expression:"record.config.wechat.normal.mchId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("微信支付的商户号，纯数字格式；例如：**********")])])],1),t("a-form-model-item",{attrs:{label:"支付密钥 (APIKEY)",prop:"config.wechat.normal.apiKey",rules:[{required:!0,message:"请填写支付密钥 (APIKEY)"}]}},[t("a-input",{attrs:{type:"password",autocomplete:"off"},model:{value:e.record.config.wechat.normal.apiKey,callback:function(t){e.$set(e.record.config.wechat.normal,"apiKey",t)},expression:"record.config.wechat.normal.apiKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('"微信支付商户平台"" - "账户中心" - "API安全" - "设置API密钥"')])])],1),t("a-form-model-item",{attrs:{label:"证书文件 (CERT)",prop:"config.wechat.normal.apiclientCert",rules:[{required:!0,message:"需要上传该文件"}]}},[t("InputFile",{attrs:{accept:".pem"},on:{change:function(t){return e.onChangeInputFile(t,arguments,"apiclientCert")}},model:{value:e.record.config.wechat.normal.apiclientCert,callback:function(t){e.$set(e.record.config.wechat.normal,"apiclientCert",t)},expression:"record.config.wechat.normal.apiclientCert"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('请上传 "apiclient_cert.pem" 文件')])])],1),t("a-form-model-item",{attrs:{label:"证书文件 (KEY)",prop:"config.wechat.normal.apiclientKey",rules:[{required:!0,message:"需要上传该文件"}]}},[t("InputFile",{attrs:{accept:".pem"},on:{change:function(t){return e.onChangeInputFile(t,arguments,"apiclientKey")}},model:{value:e.record.config.wechat.normal.apiclientKey,callback:function(t){e.$set(e.record.config.wechat.normal,"apiclientKey",t)},expression:"record.config.wechat.normal.apiclientKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('请上传 "apiclient_key.pem" 文件')])])],1)],1):e._e(),"provider"===e.record.config.wechat.mchType?t("div",{attrs:{mchType:e.record.config.wechat.mchType}},[t("a-form-model-item",{attrs:{label:"服务商应用ID (AppID)",prop:"config.wechat.provider.spAppId",rules:[{required:!0,message:"请填写服务商应用ID (AppID)"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.config.wechat.provider.spAppId,callback:function(t){e.$set(e.record.config.wechat.provider,"spAppId",t)},expression:"record.config.wechat.provider.spAppId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("请填写微信支付服务商的AppID")])])],1),t("a-form-model-item",{attrs:{label:"服务商户号 (MchId)",prop:"config.wechat.provider.spMchId",rules:[{required:!0,message:"请填写服务商户号 (MchId)"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.config.wechat.provider.spMchId,callback:function(t){e.$set(e.record.config.wechat.provider,"spMchId",t)},expression:"record.config.wechat.provider.spMchId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("微信支付服务商的商户号，纯数字格式；例如：**********")])])],1),t("a-form-model-item",{attrs:{label:"服务商密钥 (APIKEY)",prop:"config.wechat.provider.spApiKey",rules:[{required:!0,message:"请填写服务商密钥 (APIKEY)"}]}},[t("a-input",{attrs:{type:"password",autocomplete:"off"},model:{value:e.record.config.wechat.provider.spApiKey,callback:function(t){e.$set(e.record.config.wechat.provider,"spApiKey",t)},expression:"record.config.wechat.provider.spApiKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('"微信支付商户平台"" - "账户中心" - "API安全" - "设置API密钥"')])])],1),t("a-form-model-item",{attrs:{label:"子商户应用ID (AppID)",prop:"config.wechat.provider.subAppId",rules:[{required:!0,message:"请填写子商户应用ID (AppID)"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.config.wechat.provider.subAppId,callback:function(t){e.$set(e.record.config.wechat.provider,"subAppId",t)},expression:"record.config.wechat.provider.subAppId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("微信小程序或者微信公众号的APPID，需要在哪个客户端支付就填写哪个，APP支付需要填写开放平台的应用APPID")])])],1),t("a-form-model-item",{attrs:{label:"子商户号 (MchId)",prop:"config.wechat.provider.subMchId",rules:[{required:!0,message:"请填写子商户号 (MchId)"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.config.wechat.provider.subMchId,callback:function(t){e.$set(e.record.config.wechat.provider,"subMchId",t)},expression:"record.config.wechat.provider.subMchId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("微信支付的商户号，纯数字格式；例如：**********")])])],1),t("a-form-model-item",{attrs:{label:"服务商证书文件 (CERT)",prop:"config.wechat.provider.spApiclientCert",rules:[{required:!0,message:"需要上传该文件"}]}},[t("InputFile",{attrs:{accept:".pem"},on:{change:function(t){return e.onChangeInputFile(t,arguments,"spApiclientCert")}},model:{value:e.record.config.wechat.provider.spApiclientCert,callback:function(t){e.$set(e.record.config.wechat.provider,"spApiclientCert",t)},expression:"record.config.wechat.provider.spApiclientCert"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('请上传 "apiclient_cert.pem" 文件')])])],1),t("a-form-model-item",{attrs:{label:"服务商证书文件 (KEY)",prop:"config.wechat.provider.spApiclientKey",rules:[{required:!0,message:"需要上传该文件"}]}},[t("InputFile",{attrs:{accept:".pem"},on:{change:function(t){return e.onChangeInputFile(t,arguments,"spApiclientKey")}},model:{value:e.record.config.wechat.provider.spApiclientKey,callback:function(t){e.$set(e.record.config.wechat.provider,"spApiclientKey",t)},expression:"record.config.wechat.provider.spApiclientKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('请上传 "apiclient_key.pem" 文件')])])],1)],1):e._e()],1):e._e(),e.record.method===e.PaymentMethodEnum.ALIPAY.value?t("div",{attrs:{method:e.record.method}},[t("a-form-model-item",{attrs:{label:"支付宝应用 (AppID)",prop:"config.alipay.appId",rules:[{required:!0,message:"请填写支付宝应用 (AppID)"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.config.alipay.appId,callback:function(t){e.$set(e.record.config.alipay,"appId",t)},expression:"record.config.alipay.appId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("支付宝分配给开发者的应用ID；例如：2021072300007148")])])],1),t("a-form-model-item",{attrs:{label:"签名算法 (signType)",prop:"config.alipay.signType",rules:[{required:!0,message:"请选择签名算法 (signType)"}]}},[t("a-radio-group",{model:{value:e.record.config.alipay.signType,callback:function(t){e.$set(e.record.config.alipay,"signType",t)},expression:"record.config.alipay.signType"}},[t("a-radio",{attrs:{value:"RSA2"}},[e._v("RSA2")]),t("a-radio",{attrs:{value:"RSA",disabled:!0}},[e._v("RSA")])],1)],1),t("a-form-model-item",{attrs:{label:"加签模式",prop:"config.alipay.signMode",rules:[{required:!0,message:"请选择加签模式"}]}},[t("a-radio-group",{model:{value:e.record.config.alipay.signMode,callback:function(t){e.$set(e.record.config.alipay,"signMode",t)},expression:"record.config.alipay.signMode"}},[t("a-radio",{attrs:{value:10}},[t("span",[e._v("公钥证书")]),t("a-tag",{staticClass:"ml-5",attrs:{color:"green"}},[e._v("推荐")])],1),t("a-radio",{attrs:{value:20}},[e._v("公钥")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("如需使用资金支出类的接口，则必须使用公钥证书模式")])])],1),20===e.record.config.alipay.signMode?t("div",{attrs:{method:e.record.method}},[t("a-form-model-item",{attrs:{label:"支付宝公钥 (alipayPublicKey)",prop:"config.alipay.alipayPublicKey",rules:[{required:!0,message:"请填写支付宝公钥 (alipayPublicKey)"}]}},[t("a-textarea",{attrs:{autoSize:{minRows:4,maxRows:6},autocomplete:"off"},model:{value:e.record.config.alipay.alipayPublicKey,callback:function(t){e.$set(e.record.config.alipay,"alipayPublicKey",t)},expression:"record.config.alipay.alipayPublicKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('可在 "支付宝开放平台" - "应用信息" - "接口加签方式" - "支付宝公钥" 中复制')])])],1)],1):e._e(),10===e.record.config.alipay.signMode?t("div",{attrs:{method:e.record.method}},[t("a-form-model-item",{attrs:{label:"应用公钥证书",prop:"config.alipay.appCertPublicKey",rules:[{required:!0,message:"需要上传该文件"}]}},[t("InputFile",{attrs:{accept:".crt"},on:{change:function(t){return e.onChangeInputFile(t,arguments,"appCertPublicKey")}},model:{value:e.record.config.alipay.appCertPublicKey,callback:function(t){e.$set(e.record.config.alipay,"appCertPublicKey",t)},expression:"record.config.alipay.appCertPublicKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('请上传 "appCertPublicKey_xxxxxxxx.crt" 文件')])])],1),t("a-form-model-item",{attrs:{label:"支付宝公钥证书",prop:"config.alipay.alipayCertPublicKey",rules:[{required:!0,message:"需要上传该文件"}]}},[t("InputFile",{attrs:{accept:".crt"},on:{change:function(t){return e.onChangeInputFile(t,arguments,"alipayCertPublicKey")}},model:{value:e.record.config.alipay.alipayCertPublicKey,callback:function(t){e.$set(e.record.config.alipay,"alipayCertPublicKey",t)},expression:"record.config.alipay.alipayCertPublicKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('请上传 "alipayCertPublicKey_RSA2.crt" 文件')])])],1),t("a-form-model-item",{attrs:{label:"支付宝根证书",prop:"config.alipay.alipayRootCert",rules:[{required:!0,message:"需要上传该文件"}]}},[t("InputFile",{attrs:{accept:".crt"},on:{change:function(t){return e.onChangeInputFile(t,arguments,"alipayRootCert")}},model:{value:e.record.config.alipay.alipayRootCert,callback:function(t){e.$set(e.record.config.alipay,"alipayRootCert",t)},expression:"record.config.alipay.alipayRootCert"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('请上传 "alipayRootCert.crt" 文件')])])],1)],1):e._e(),t("a-form-model-item",{attrs:{label:"应用私钥 (privateKey)",prop:"config.alipay.merchantPrivateKey",rules:[{required:!0,message:"请填写应用私钥 (privateKey)"}]}},[t("a-textarea",{attrs:{autoSize:{minRows:4,maxRows:6},autocomplete:"off"},model:{value:e.record.config.alipay.merchantPrivateKey,callback:function(t){e.$set(e.record.config.alipay,"merchantPrivateKey",t)},expression:"record.config.alipay.merchantPrivateKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('查看 "应用私钥RSA2048-敏感数据，请妥善保管.txt" 文件，将全部内容复制到此处')])])],1)],1):e._e(),t("a-form-model-item",{attrs:{wrapperCol:{offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary",loading:e.isBtnLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)],1)},n=[],o=a("5530"),l=a("ade3"),s=(a("d3b7"),a("2ef0")),c=a("67e5"),d=a("759f"),u=a("3f9c"),p={name:"",method:u["a"].WECHAT.value,sort:100,remarks:"",config:(r={},Object(l["a"])(r,u["a"].WECHAT.value,{mchType:"normal",version:"v3",normal:{appId:"",mchId:"",apiKey:"",apiclientCert:"",apiclientKey:""},provider:{spAppId:"",spMchId:"",spApiKey:"",subAppId:"",subMchId:"",spApiclientCert:"",spApiclientKey:""}}),Object(l["a"])(r,u["a"].ALIPAY.value,{appId:"",signType:"RSA2",signMode:10,alipayPublicKey:"",appCertPublicKey:"",alipayCertPublicKey:"",alipayRootCert:"",merchantPrivateKey:""}),r)},m={components:{InputFile:c["a"]},data:function(){return{isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},PaymentMethodEnum:u["a"],record:Object(s["cloneDeep"])(p),uploadFiles:{apiclientCert:null,apiclientKey:null,spApiclientCert:null,spApiclientKey:null,appCertPublicKey:null,alipayCertPublicKey:null,alipayRootCert:null},templateId:null}},watch:{"$route.query.templateId":{immediate:!0,handler:function(e){this.templateId=e,this.getDetail()}}},created:function(){},methods:{getDetail:function(){var e=this;this.isLoading=!0,d["d"](this.templateId).then((function(t){e.record=Object(o["a"])(Object(o["a"])({},p),t.data.detail)})).finally((function(){return e.isLoading=!1}))},clearValidate:function(){this.$refs.myForm.clearValidate()},handleSubmit:function(e){var t=this;e.preventDefault(),this.$refs.myForm.validate((function(e){if(!e)return!1;t.onSubmitForm()}))},onChangeInputFile:function(e,t,a){var r=this.uploadFiles;null!==t[1]&&(r[a]=t[1])},onSubmitForm:function(){var e=this;this.isLoading=!0,this.isBtnLoading=!0;var t=this.buildFormData();d["e"](t).then((function(t){e.$message.success(t.message,1.5),setTimeout((function(){e.$router.push("./index")}),1500)})).catch((function(){return e.isBtnLoading=!1})).finally((function(){return e.isLoading=!1}))},buildFormData:function(){var e=this.record,t=this.uploadFiles,a=this.templateId,r=new FormData;for(var i in r.append("templateId",a),r.append("name",e.name),r.append("sort",e.sort),r.append("method",e.method),r.append("remarks",e.remarks),r.append("config",JSON.stringify(e.config)),t)null!=t[i]&&r.append(i,t[i]);return r}}},f=m,h=(a("ea54"),a("2877")),v=Object(h["a"])(f,i,n,!1,null,"66391143",null);t["default"]=v.exports},"551e":function(e,t,a){"use strict";a("e77f")},"552c":function(e,t,a){},"554b":function(e,t,a){"use strict";a("791c")},"5c49":function(e,t,a){},"5d09":function(e,t,a){"use strict";a("b2dc")},"61e5":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"模版名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入模版名称"}})],1),t("a-form-item",{attrs:{label:"计费方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["method",{initialValue:10,rules:[{required:!0}]}],expression:"['method', { initialValue: 10, rules: [{ required: true }] }]"}],on:{change:e.onChangemMethod}},[t("a-radio",{attrs:{value:10}},[e._v("按件数")]),t("a-radio",{attrs:{value:20}},[e._v("按重量")])],1)],1),t("a-form-item",{attrs:{label:"配送区域及运费",labelCol:e.labelCol,wrapperCol:{span:15},required:""}},[t("a-table",{directives:[{name:"show",rawName:"v-show",value:e.ruleList.length,expression:"ruleList.length"}],staticClass:"table-rules",attrs:{columns:e.columns,dataSource:e.ruleList,pagination:!1,bordered:""},scopedSlots:e._u([{key:"region_text",fn:function(a,r,i){return[t("p",{staticClass:"content"},e._l(a,(function(a,r){return t("span",{key:r},[t("span",[e._v(e._s(a.name))]),a.citys.length?[t("span",[e._v("(")]),e._l(a.citys,(function(r,i){return t("span",{key:i,staticClass:"city-name"},[e._v(e._s(r.name)+e._s(a.citys.length>i+1?"、":""))])})),t("span",[e._v(")")])]:e._e(),t("span",[e._v(e._s(" "))])],2)})),0),t("p",{staticClass:"operation"},[t("a",{staticClass:"edit",attrs:{href:"javascript:void(0);"},on:{click:function(t){return e.handleEdit(i,r)}}},[e._v("编辑")]),t("a",{staticClass:"delete",attrs:{href:"javascript:void(0);"},on:{click:function(t){return e.handleDelete(i)}}},[e._v("删除")])])]}},{key:"first",fn:function(a,r){return[t("a-input-number",{attrs:{min:10==e.method?1:.01,precision:10==e.method?0:2},model:{value:r.first,callback:function(t){e.$set(r,"first",t)},expression:"item.first"}})]}},{key:"first_fee",fn:function(a,r){return[t("a-input-number",{attrs:{min:0,precision:2},model:{value:r.first_fee,callback:function(t){e.$set(r,"first_fee",t)},expression:"item.first_fee"}})]}},{key:"additional",fn:function(a,r){return[t("a-input-number",{attrs:{min:0,precision:10==e.method?0:2},model:{value:r.additional,callback:function(t){e.$set(r,"additional",t)},expression:"item.additional"}})]}},{key:"additional_fee",fn:function(a,r){return[t("a-input-number",{attrs:{min:0,precision:2},model:{value:r.additional_fee,callback:function(t){e.$set(r,"additional_fee",t)},expression:"item.additional_fee"}})]}}])}),t("a-button",{attrs:{icon:"environment"},on:{click:e.handleAdd}},[e._v("点击添加配送区域")])],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1),t("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1),t("AreasModal",{ref:"AreasModal",on:{handleSubmit:e.handleAreaSubmit}})],1)],1)},i=[],n=a("5530"),o=(a("d3b7"),a("159b"),a("a434"),a("fd0d")),l=a("967a"),s=a("caec"),c={key:0,first:1,first_fee:0,additional:0,additional_fee:0,region:[],region_text:[]},d={components:{AreasModal:o["a"]},data:function(){return{columns:[{title:"可配送区域",dataIndex:"region_text",width:"400px",scopedSlots:{customRender:"region_text"}},{title:"首件 (个)",dataIndex:"first",scopedSlots:{customRender:"first"}},{title:"运费(元)",dataIndex:"first_fee",scopedSlots:{customRender:"first_fee"}},{title:"续件 (个)",dataIndex:"additional",scopedSlots:{customRender:"additional"}},{title:"续费(元)",dataIndex:"additional_fee",scopedSlots:{customRender:"additional_fee"}}],ruleList:[],isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},form:this.$form.createForm(this),method:10,citysCount:null}},created:function(){var e=this;s["a"].getCitysCount().then((function(t){e.citysCount=t}))},methods:{onChangemMethod:function(e){this.method=e.target.value;var t={10:{first:"首件 (个)",additional:"续件 (个)"},20:{first:"首重 (Kg)",additional:"续重 (Kg)"}};this.columns[1].title=t[this.method].first,this.columns[3].title=t[this.method].additional},handleAdd:function(){var e=this.ruleList.length,t=Object(n["a"])(Object(n["a"])({},c),{},{key:e}),a=this.getExcludedCityIds();if(a.length===this.citysCount)return this.$message.error("已选择了所有的区域",.8),!1;this.handleAreasModal("add",e,t,a)},handleEdit:function(e,t){var a=this.getExcludedCityIds();this.handleAreasModal("edit",e,t,a)},handleAreasModal:function(e,t,a,r){this.$refs.AreasModal.handle({scene:e,index:t,item:a},a.region,r)},getExcludedCityIds:function(){var e=[];return this.ruleList.forEach((function(t){t.region.forEach((function(t){e.push(t)}))})),e},handleAreaSubmit:function(e){var t=e.custom,a=t.scene,r=t.item;r.region=e.selectedCityIds,r.region_text=e.selectedText,"add"===a&&this.ruleList.push(r)},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",onOk:function(){t.ruleList.splice(e,1),a.destroy()}})},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields,r=this.ruleList;a((function(e,a){return!e&&(0===r.length?(t.$message.error("您还没有添加配送区域及运费",.8),!1):(a.rules=r,void t.onFormSubmit(a)))}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,this.isBtnLoading=!0,l["a"]({form:e}).then((function(e){t.$message.success(e.message,1.5),setTimeout((function(){t.$router.push("./index")}),1500)})).catch((function(){t.isBtnLoading=!1})).finally((function(){return t.isLoading=!1}))}}},u=d,p=(a("6b65"),a("2877")),m=Object(p["a"])(u,r,i,!1,null,null,null);t["default"]=m.exports},"637f":function(e,t,a){"use strict";a("d3a6")},"67ac":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"缓存项目",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-checkbox-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["keys",{rules:[{required:!0,message:"配送方式至少选择一个"}]}],expression:"['keys', { rules: [{ required: true, message: '配送方式至少选择一个' }] }]"}]},e._l(e.items,(function(a,r){return t("a-checkbox",{key:r,attrs:{value:a.key}},[e._v(e._s(a.name))])})),1)],1),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},i=[],n=(a("d3b7"),a("d81d"),a("b775")),o={items:"/setting.cache/items",clear:"/setting.cache/clear"};function l(){return Object(n["b"])({url:o.items,method:"get"})}function s(e){return Object(n["b"])({url:o.clear,method:"post",data:e})}var c=a("71b5"),d={data:function(){return{labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),items:{},StorageEnum:c["a"]}},created:function(){this.getItems()},methods:{getItems:function(){var e=this;this.isLoading=!0,l().then((function(t){e.items=t.data.items,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this,t=this.form.setFieldsValue;this.$nextTick((function(){t({keys:e.items.map((function(e){return e.key}))})}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,s({form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},u=d,p=(a("34e6"),a("2877")),m=Object(p["a"])(u,r,i,!1,null,"74c84ca0",null);t["default"]=m.exports},"67e5":function(e,t,a){"use strict";var r=function(){var e=this,t=e._self._c;return t("a-upload",{attrs:{beforeUpload:e.beforeUpload,remove:e.handleRemove,accept:e.accept,multiple:!1,fileList:e.fileList}},[t("a-button",[t("a-icon",{attrs:{type:"upload"}}),e._v("选择文件 ")],1)],1)},i=[],n=(a("b0c0"),a("a434"),a("4d91")),o={name:"InputFile",model:{prop:"value",event:"change"},props:{value:n["a"].string.def(""),accept:n["a"].string.def("")},data:function(){return{fileList:[]}},watch:{value:{immediate:!0,handler:function(e){e&&0===this.fileList.length&&(this.fileList=[{uid:"default",name:e}])}},fileList:function(e){var t=e.length>0?e[0]:null,a=t?t.name:null;"default"!=t.uid&&this.$emit("change",a,t)}},methods:{beforeUpload:function(e){return this.fileList=[e],!1},handleRemove:function(e){var t=this.fileList,a=t.indexOf(e);a>-1&&t.splice(a,1)}}},l=o,s=a("2877"),c=Object(s["a"])(l,r,i,!1,null,"37881c5a",null),d=c.exports;t["a"]=d},"6b65":function(e,t,a){"use strict";a("fed5c")},"71b5":function(e,t,a){"use strict";var r=a("5c06");t["a"]=new r["a"]([{key:"LOCAL",name:"本地",value:"local"},{key:"QINIU",name:"七牛云",value:"qiniu"},{key:"ALIYUN",name:"阿里云",value:"aliyun"},{key:"QCLOUD",name:"腾讯云",value:"qcloud"},{key:"EXTERNAL",name:"外部链接",value:"external"}])},"759f":function(e,t,a){"use strict";a.d(t,"f",(function(){return o})),a.d(t,"b",(function(){return l})),a.d(t,"d",(function(){return s})),a.d(t,"a",(function(){return c})),a.d(t,"e",(function(){return d})),a.d(t,"c",(function(){return u}));var r=a("5530"),i=a("b775"),n={list:"/setting.payment.template/list",all:"/setting.payment.template/all",detail:"/setting.payment.template/detail",add:"/setting.payment.template/add",edit:"/setting.payment.template/edit",delete:"/setting.payment.template/delete"};function o(e){return Object(i["b"])({url:n.list,method:"get",params:e})}function l(e){return Object(i["b"])({url:n.all,method:"get",params:e})}function s(e,t){return Object(i["b"])({url:n.detail,method:"get",params:Object(r["a"])({templateId:e},t)})}function c(e){return Object(i["b"])({headers:{"Content-Type":"multipart/form-data"},url:n.add,method:"post",data:e})}function d(e){return Object(i["b"])({headers:{"Content-Type":"multipart/form-data"},url:n.edit,method:"post",data:e})}function u(e){return Object(i["b"])({url:n.delete,method:"post",data:e})}},7710:function(e,t,a){"use strict";a("7eae")},7910:function(e,t,a){"use strict";a("027d")},"791c":function(e,t,a){},"7eae":function(e,t,a){},8259:function(e,t,a){"use strict";a.r(t);a("ac1f"),a("841c"),a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{attrs:{span:6}},[t("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),t("a-col",{attrs:{span:8,offset:10}},[t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px",float:"right"},attrs:{placeholder:"请输入打印机名称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"printer_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"printer_type",fn:function(a){return t("span",{},[t("a-tag",[e._v(e._s(e.PrinterEnum[a].name))])],1)}},{key:"action",fn:function(a,r){return t("span",{},[t("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),t("AddForm",{ref:"AddForm",on:{handleSubmit:e.handleRefresh}}),t("EditForm",{ref:"EditForm",on:{handleSubmit:e.handleRefresh}})],1)},i=[],n=a("5530"),o=(a("d3b7"),a("5c06")),l=new o["a"]([{key:"FEI_E_YUN",name:"飞鹅打印机",value:"FEI_E_YUN"},{key:"PRINT_CENTER",name:"365云打印",value:"PRINT_CENTER"}]),s=a("f50c"),c=a("2af9"),d=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"打印机名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['printer_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),t("a-form-item",{attrs:{label:"打印机类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_type",{initialValue:e.PrinterEnum.FEI_E_YUN.value,rules:[{required:!0}]}],expression:"['printer_type', { initialValue: PrinterEnum.FEI_E_YUN.value, rules: [{ required: true }] }]"}]},e._l(e.PrinterEnum.data,(function(a,r){return t("a-radio",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1)],1),e.form.getFieldValue("printer_type")==e.PrinterEnum.FEI_E_YUN.value?t("div",{class:e.PrinterEnum.FEI_E_YUN.value},[t("a-form-item",{attrs:{label:"USER",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"飞鹅云后台注册用户名"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_config.USER",{rules:[{required:!0}]}],expression:"['printer_config.USER', { rules: [{ required: true }] }]"}]})],1),t("a-form-item",{attrs:{label:"UKEY",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"飞鹅云后台登录生成的UKEY"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_config.UKEY",{rules:[{required:!0}]}],expression:"['printer_config.UKEY', { rules: [{ required: true }] }]"}]})],1),t("a-form-item",{attrs:{label:"打印机编号",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"打印机编号为9位数字，查看飞鹅打印机底部贴纸上面的编号"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_config.SN",{rules:[{required:!0}]}],expression:"['printer_config.SN', { rules: [{ required: true } ] }]"}]})],1)],1):e._e(),e.form.getFieldValue("printer_type")==e.PrinterEnum.PRINT_CENTER.value?t("div",{class:e.PrinterEnum.PRINT_CENTER.value},[t("a-form-item",{attrs:{label:"打印机编号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_config.deviceNo",{rules:[{required:!0}]}],expression:"['printer_config.deviceNo', { rules: [{ required: true }] }]"}]})],1),t("a-form-item",{attrs:{label:"打印机秘钥",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_config.key",{rules:[{required:!0}]}],expression:"['printer_config.key', { rules: [{ required: true }] }]"}]})],1)],1):e._e(),t("a-form-item",{attrs:{label:"打印联数",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"同一订单，打印的次数"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["print_times",{initialValue:1,rules:[{required:!0,message:"请输入打印联数"}]}],expression:"['print_times', { initialValue: 1, rules: [{ required: true, message: '请输入打印联数' }] }]"}],attrs:{min:0}})],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},u=[],p=a("2ef0"),m=a.n(p),f={data:function(){return{title:"新增打印机",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),PrinterEnum:l}},methods:{add:function(){this.visible=!0,this.setFieldsValue()},setFieldsValue:function(){var e=this.form.setFieldsValue;this.$nextTick((function(){e({printer_type:l.FEI_E_YUN.value})}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,s["a"]({form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){return t.confirmLoading=!1}))}}},h=f,v=a("2877"),g=Object(v["a"])(h,d,u,!1,null,null,null),b=g.exports,y=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"打印机名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['printer_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),t("a-form-item",{attrs:{label:"打印机类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_type",{initialValue:e.PrinterEnum.FEI_E_YUN.value,rules:[{required:!0}]}],expression:"['printer_type', { initialValue: PrinterEnum.FEI_E_YUN.value, rules: [{ required: true }] }]"}]},e._l(e.PrinterEnum.data,(function(a,r){return t("a-radio",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1)],1),e.form.getFieldValue("printer_type")==e.PrinterEnum.FEI_E_YUN.value?t("div",{class:e.PrinterEnum.FEI_E_YUN.value},[t("a-form-item",{attrs:{label:"USER",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"飞鹅云后台注册用户名"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_config.USER",{rules:[{required:!0}]}],expression:"['printer_config.USER', { rules: [{ required: true }] }]"}]})],1),t("a-form-item",{attrs:{label:"UKEY",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"飞鹅云后台登录生成的UKEY"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_config.UKEY",{rules:[{required:!0}]}],expression:"['printer_config.UKEY', { rules: [{ required: true }] }]"}]})],1),t("a-form-item",{attrs:{label:"打印机编号",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"打印机编号为9位数字，查看飞鹅打印机底部贴纸上面的编号"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_config.SN",{rules:[{required:!0}]}],expression:"['printer_config.SN', { rules: [{ required: true }] }]"}]})],1)],1):e._e(),e.form.getFieldValue("printer_type")==e.PrinterEnum.PRINT_CENTER.value?t("div",{class:e.PrinterEnum.PRINT_CENTER.value},[t("a-form-item",{attrs:{label:"打印机编号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_config.deviceNo",{rules:[{required:!0}]}],expression:"['printer_config.deviceNo', { rules: [{ required: true }] }]"}]})],1),t("a-form-item",{attrs:{label:"打印机秘钥",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["printer_config.key",{rules:[{required:!0}]}],expression:"['printer_config.key', { rules: [{ required: true }] }]"}]})],1)],1):e._e(),t("a-form-item",{attrs:{label:"打印联数",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"同一订单，打印的次数"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["print_times",{initialValue:1,rules:[{required:!0,message:"请输入打印联数"}]}],expression:"['print_times', { initialValue: 1, rules: [{ required: true, message: '请输入打印联数' }] }]"}],attrs:{min:0}})],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},C=[],_={data:function(){return{title:"编辑打印机",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),PrinterEnum:l,record:{}}},methods:{edit:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this,t=this.form.setFieldsValue;this.$nextTick((function(){t(m.a.pick(e.record,["printer_name","printer_type","print_times","sort"])),e.$nextTick((function(){t(m.a.pick(e.record,["printer_config"]))}))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,s["d"]({printerId:this.record.printer_id,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){return t.confirmLoading=!1}))}}},w=_,x=Object(v["a"])(w,y,C,!1,null,null,null),k=x.exports,I={name:"Index",components:{STable:c["d"],AddForm:b,EditForm:k},data:function(){var e=this;return{queryParam:{},isLoading:!1,PrinterEnum:l,columns:[{title:"打印机ID",dataIndex:"printer_id"},{title:"打印机名称",dataIndex:"printer_name"},{title:"打印机类型",dataIndex:"printer_type",scopedSlots:{customRender:"printer_type"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(t){return s["e"](Object(n["a"])(Object(n["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return s["c"]({printerId:e["printer_id"]}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},A=I,E=Object(v["a"])(A,r,i,!1,null,null,null);t["default"]=E.exports},"88bc":function(e,t,a){(function(t){var a=1/0,r=9007199254740991,i="[object Arguments]",n="[object Function]",o="[object GeneratorFunction]",l="[object Symbol]",s="object"==typeof t&&t&&t.Object===Object&&t,c="object"==typeof self&&self&&self.Object===Object&&self,d=s||c||Function("return this")();function u(e,t,a){switch(a.length){case 0:return e.call(t);case 1:return e.call(t,a[0]);case 2:return e.call(t,a[0],a[1]);case 3:return e.call(t,a[0],a[1],a[2])}return e.apply(t,a)}function p(e,t){var a=-1,r=e?e.length:0,i=Array(r);while(++a<r)i[a]=t(e[a],a,e);return i}function m(e,t){var a=-1,r=t.length,i=e.length;while(++a<r)e[i+a]=t[a];return e}var f=Object.prototype,h=f.hasOwnProperty,v=f.toString,g=d.Symbol,b=f.propertyIsEnumerable,y=g?g.isConcatSpreadable:void 0,C=Math.max;function _(e,t,a,r,i){var n=-1,o=e.length;a||(a=I),i||(i=[]);while(++n<o){var l=e[n];t>0&&a(l)?t>1?_(l,t-1,a,r,i):m(i,l):r||(i[i.length]=l)}return i}function w(e,t){return e=Object(e),x(e,t,(function(t,a){return a in e}))}function x(e,t,a){var r=-1,i=t.length,n={};while(++r<i){var o=t[r],l=e[o];a(l,o)&&(n[o]=l)}return n}function k(e,t){return t=C(void 0===t?e.length-1:t,0),function(){var a=arguments,r=-1,i=C(a.length-t,0),n=Array(i);while(++r<i)n[r]=a[t+r];r=-1;var o=Array(t+1);while(++r<t)o[r]=a[r];return o[t]=n,u(e,this,o)}}function I(e){return L(e)||E(e)||!!(y&&e&&e[y])}function A(e){if("string"==typeof e||N(e))return e;var t=e+"";return"0"==t&&1/e==-a?"-0":t}function E(e){return q(e)&&h.call(e,"callee")&&(!b.call(e,"callee")||v.call(e)==i)}var L=Array.isArray;function S(e){return null!=e&&F(e.length)&&!P(e)}function q(e){return K(e)&&S(e)}function P(e){var t=$(e)?v.call(e):"";return t==n||t==o}function F(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=r}function $(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function K(e){return!!e&&"object"==typeof e}function N(e){return"symbol"==typeof e||K(e)&&v.call(e)==l}var O=k((function(e,t){return null==e?{}:w(e,p(_(t,1),A))}));e.exports=O}).call(this,a("c8ba"))},"938c":function(e,t,a){"use strict";a("c553")},"967a":function(e,t,a){"use strict";a.d(t,"f",(function(){return n})),a.d(t,"b",(function(){return o})),a.d(t,"d",(function(){return l})),a.d(t,"a",(function(){return s})),a.d(t,"e",(function(){return c})),a.d(t,"c",(function(){return d}));var r=a("b775"),i={list:"/setting.delivery/list",all:"/setting.delivery/all",detail:"/setting.delivery/detail",add:"/setting.delivery/add",edit:"/setting.delivery/edit",delete:"/setting.delivery/delete"};function n(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function o(e){return Object(r["b"])({url:i.all,method:"get",params:e})}function l(e){return Object(r["b"])({url:i.detail,method:"get",params:e})}function s(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function c(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function d(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},"96ef":function(e,t,a){"use strict";a.r(t);a("ac1f"),a("841c");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{attrs:{span:6}},[e.$auth("/setting/delivery/template/create")?t("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]):e._e()],1),t("a-col",{attrs:{span:8,offset:10}},[t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px",float:"right"},attrs:{placeholder:"请输入运费模板名称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"delivery_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"method",fn:function(a){return t("span",{},[t("span",[e._v(e._s(10==a?"按件数":"按重量"))])])}},{key:"action",fn:function(a,r){return t("span",{},[e.$auth("/setting/delivery/template/update")?t("a",{staticStyle:{"margin-right":"8px"},on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]):e._e(),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")])])}}])})],1)},i=[],n=a("5530"),o=(a("d3b7"),a("967a")),l=a("2af9"),s={name:"Index",components:{STable:l["d"]},data:function(){var e=this;return{queryParam:{},isLoading:!1,columns:[{title:"模板ID",dataIndex:"delivery_id"},{title:"模板名称",dataIndex:"name"},{title:"计费方式",dataIndex:"method",scopedSlots:{customRender:"method"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(t){return o["f"](Object(n["a"])(Object(n["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$router.push("./create")},handleEdit:function(e){this.$router.push({path:"./update",query:{deliveryId:e.delivery_id}})},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return o["c"]({deliveryId:e["delivery_id"]}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},c=s,d=a("2877"),u=Object(d["a"])(c,r,i,!1,null,null,null);t["default"]=u.exports},a6b6:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"未支付订单",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("div",{staticClass:"clearfix"},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["order.closeHours",{rules:[{required:!0,message:"不能为空"}]}],expression:"['order.closeHours', { rules: [{ required: true, message: '不能为空' }] }]"}],staticClass:"fl-l",attrs:{min:0,precision:0}}),t("span",{staticClass:"input-text_right"},[e._v("小时后自动关闭")])],1),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("如果在期间订单未付款，系统自动关闭，设置0小时不自动关闭")])])]),t("a-form-item",{attrs:{label:"已发货订单",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("div",{staticClass:"clearfix"},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["order.receive_days",{rules:[{required:!0,message:"不能为空"}]}],expression:"['order.receive_days', { rules: [{ required: true, message: '不能为空' }] }]"}],staticClass:"fl-l",attrs:{min:0,precision:0}}),t("span",{staticClass:"input-text_right"},[e._v("天后自动确认收货")])],1),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("如果在期间未确认收货，系统自动完成收货，设置0天不自动收货")])])]),t("a-form-item",{attrs:{label:"已完成订单",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("div",{staticClass:"clearfix"},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["order.refund_days",{rules:[{required:!0,message:"不能为空"}]}],expression:"['order.refund_days', { rules: [{ required: true, message: '不能为空' }] }]"}],staticClass:"fl-l",attrs:{min:0,precision:0}}),t("span",{staticClass:"input-text_right"},[e._v("天内允许申请售后")])],1),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("订单完成后，用户在指定期限内可申请售后，设置0天不允许申请")])])]),t("a-divider",{attrs:{orientation:"left"}},[e._v("运费设置")]),t("a-form-item",{attrs:{label:"运费组合策略",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["freight_rule",{rules:[{required:!0}]}],expression:"['freight_rule', { rules: [{ required: true }] }]"}]},[t("div",{staticClass:"radio-item"},[t("a-radio",{style:e.radioStyle,attrs:{value:"10"}},[e._v("叠加")]),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("订单中的商品有多个运费模板时，将每个商品的运费之和订为订单总运费")])])],1),t("div",{staticClass:"radio-item"},[t("a-radio",{style:e.radioStyle,attrs:{value:"20"}},[e._v("以最低运费结算")]),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("订单中的商品有多个运费模板时，取订单中运费最少的商品的运费计为订单总运费")])])],1),t("div",{staticClass:"radio-item"},[t("a-radio",{style:e.radioStyle,attrs:{value:"30"}},[e._v("以最高运费结算")]),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("订单中的商品有多个运费模板时，取订单中运费最多的商品的运费计为订单总运费")])])],1)])],1),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},i=[],n=(a("d3b7"),a("ddb0"),a("88bc")),o=a.n(n),l=a("f585"),s=a("ca00"),c={data:function(){return{key:"trade",labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),radioStyle:{display:"block",height:"30px",lineHeight:"30px"},record:{}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,l["a"](this.key).then((function(t){e.record=t.data.values,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,t=this.$nextTick,a=this.form;!Object(s["g"])(a.getFieldsValue())&&t((function(){a.setFieldsValue({freight_rule:e.freight_rule,order:o()(e.order,["closeHours","receive_days","refund_days"])})}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,l["b"](this.key,{form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},d=c,u=(a("554b"),a("2877")),p=Object(u["a"])(d,r,i,!1,null,"4a7d2c04",null);t["default"]=p.exports},a755:function(e,t,a){"use strict";a.r(t);a("b0c0"),a("4e82");var r,i=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form-model",{ref:"myForm",staticClass:"my-form",attrs:{model:e.record,"label-col":e.labelCol,wrapperCol:e.wrapperCol}},[t("a-form-model-item",{attrs:{label:"支付模板名称",prop:"name",rules:[{required:!0,message:"请填写支付模板名称"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.name,callback:function(t){e.$set(e.record,"name",t)},expression:"record.name"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("仅用于后台管理使用，对前台用户不可见；例如：H5端-支付宝支付；微信小程序端-微信支付")])])],1),t("a-form-model-item",{attrs:{label:"管理员备注",prop:"remarks"}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.remarks,callback:function(t){e.$set(e.record,"remarks",t)},expression:"record.remarks"}})],1),t("a-form-model-item",{staticClass:"mb-30",attrs:{label:"排序",prop:"sort",rules:[{required:!0,message:"请填写排序数值"}]}},[t("a-input-number",{attrs:{min:0,autocomplete:"off"},model:{value:e.record.sort,callback:function(t){e.$set(e.record,"sort",t)},expression:"record.sort"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("数字越小越靠前")])])],1),t("a-form-model-item",{attrs:{label:"支付方式",prop:"method",rules:[{required:!0,message:"请选择支付方式"}]}},[t("a-radio-group",{on:{change:function(t){return e.clearValidate()}},model:{value:e.record.method,callback:function(t){e.$set(e.record,"method",t)},expression:"record.method"}},[t("a-radio",{attrs:{value:e.PaymentMethodEnum.WECHAT.value}},[e._v(e._s(e.PaymentMethodEnum.WECHAT.name))]),t("a-radio",{attrs:{value:e.PaymentMethodEnum.ALIPAY.value}},[e._v(e._s(e.PaymentMethodEnum.ALIPAY.name))])],1),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("保存以后支付方式将不可修改，请谨慎操作")]),e.record.method===e.PaymentMethodEnum.WECHAT.value?t("p",{staticClass:"extra"},[e._v(" 微信支付商户平台： "),t("a",{attrs:{href:"https://pay.weixin.qq.com",target:"_blank"}},[e._v("https://pay.weixin.qq.com")])]):e._e(),e.record.method===e.PaymentMethodEnum.ALIPAY.value?t("p",{staticClass:"extra"},[e._v(" 支付宝开发者平台： "),t("a",{attrs:{href:"https://open.alipay.com/dev/workspace",target:"_blank"}},[e._v("https://open.alipay.com/dev/workspace")])]):e._e()])],1),e.record.method===e.PaymentMethodEnum.WECHAT.value?t("div",{attrs:{method:e.record.method}},[t("a-form-model-item",{attrs:{label:"微信支付接口版本",prop:"config.wechat.version",rules:[{required:!0,message:"请选择微信支付接口版本"}]}},[t("a-radio-group",{on:{change:function(t){return e.clearValidate()}},model:{value:e.record.config.wechat.version,callback:function(t){e.$set(e.record.config.wechat,"version",t)},expression:"record.config.wechat.version"}},[t("a-radio",{attrs:{value:"v3"}},[t("span",[e._v("V3")]),t("a-tag",{staticClass:"ml-5",attrs:{color:"green"}},[e._v("推荐")])],1),t("a-radio",{attrs:{value:"v2"}},[e._v("V2")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("V2版本较老已经不再支持新出的API接口，强烈建议使用V3")])])],1),t("a-form-model-item",{attrs:{label:"微信商户号类型",prop:"config.wechat.mchType",rules:[{required:!0,message:"请选择微信商户号类型"}]}},[t("a-radio-group",{on:{change:function(t){return e.clearValidate()}},model:{value:e.record.config.wechat.mchType,callback:function(t){e.$set(e.record.config.wechat,"mchType",t)},expression:"record.config.wechat.mchType"}},[t("a-radio",{attrs:{value:"normal"}},[e._v("普通商户")]),t("a-radio",{attrs:{value:"provider"}},[e._v("子商户 (服务商模式)")])],1)],1),"normal"===e.record.config.wechat.mchType?t("div",{attrs:{mchType:e.record.config.wechat.mchType}},[t("a-form-model-item",{attrs:{label:"应用ID (AppID)",prop:"config.wechat.normal.appId",rules:[{required:!0,message:"请填写应用ID (AppID)"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.config.wechat.normal.appId,callback:function(t){e.$set(e.record.config.wechat.normal,"appId",t)},expression:"record.config.wechat.normal.appId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("微信小程序或者微信公众号的APPID，需要在哪个客户端支付就填写哪个，APP支付需要填写开放平台的应用APPID")])])],1),t("a-form-model-item",{attrs:{label:"微信商户号 (MchId)",prop:"config.wechat.normal.mchId",rules:[{required:!0,message:"请填写微信商户号 (MchId)"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.config.wechat.normal.mchId,callback:function(t){e.$set(e.record.config.wechat.normal,"mchId",t)},expression:"record.config.wechat.normal.mchId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("微信支付的商户号，纯数字格式；例如：**********")])])],1),t("a-form-model-item",{attrs:{label:"支付密钥 (APIKEY)",prop:"config.wechat.normal.apiKey",rules:[{required:!0,message:"请填写支付密钥 (APIKEY)"}]}},[t("a-input",{attrs:{type:"password",autocomplete:"off"},model:{value:e.record.config.wechat.normal.apiKey,callback:function(t){e.$set(e.record.config.wechat.normal,"apiKey",t)},expression:"record.config.wechat.normal.apiKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('"微信支付商户平台"" - "账户中心" - "API安全" - "设置API密钥"')])])],1),t("a-form-model-item",{attrs:{label:"证书文件 (CERT)",prop:"config.wechat.normal.apiclientCert",rules:[{required:!0,message:"需要上传该文件"}]}},[t("InputFile",{attrs:{accept:".pem"},on:{change:function(t){return e.onChangeInputFile(t,arguments,"apiclientCert")}},model:{value:e.record.config.wechat.normal.apiclientCert,callback:function(t){e.$set(e.record.config.wechat.normal,"apiclientCert",t)},expression:"record.config.wechat.normal.apiclientCert"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('请上传 "apiclient_cert.pem" 文件')])])],1),t("a-form-model-item",{attrs:{label:"证书文件 (KEY)",prop:"config.wechat.normal.apiclientKey",rules:[{required:!0,message:"需要上传该文件"}]}},[t("InputFile",{attrs:{accept:".pem"},on:{change:function(t){return e.onChangeInputFile(t,arguments,"apiclientKey")}},model:{value:e.record.config.wechat.normal.apiclientKey,callback:function(t){e.$set(e.record.config.wechat.normal,"apiclientKey",t)},expression:"record.config.wechat.normal.apiclientKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('请上传 "apiclient_key.pem" 文件')])])],1)],1):e._e(),"provider"===e.record.config.wechat.mchType?t("div",{attrs:{mchType:e.record.config.wechat.mchType}},[t("a-form-model-item",{attrs:{label:"服务商应用ID (AppID)",prop:"config.wechat.provider.spAppId",rules:[{required:!0,message:"请填写服务商应用ID (AppID)"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.config.wechat.provider.spAppId,callback:function(t){e.$set(e.record.config.wechat.provider,"spAppId",t)},expression:"record.config.wechat.provider.spAppId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("请填写微信支付服务商的AppID")])])],1),t("a-form-model-item",{attrs:{label:"服务商户号 (MchId)",prop:"config.wechat.provider.spMchId",rules:[{required:!0,message:"请填写服务商户号 (MchId)"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.config.wechat.provider.spMchId,callback:function(t){e.$set(e.record.config.wechat.provider,"spMchId",t)},expression:"record.config.wechat.provider.spMchId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("微信支付服务商的商户号，纯数字格式；例如：**********")])])],1),t("a-form-model-item",{attrs:{label:"服务商密钥 (APIKEY)",prop:"config.wechat.provider.spApiKey",rules:[{required:!0,message:"请填写服务商密钥 (APIKEY)"}]}},[t("a-input",{attrs:{type:"password",autocomplete:"off"},model:{value:e.record.config.wechat.provider.spApiKey,callback:function(t){e.$set(e.record.config.wechat.provider,"spApiKey",t)},expression:"record.config.wechat.provider.spApiKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('"微信支付商户平台"" - "账户中心" - "API安全" - "设置API密钥"')])])],1),t("a-form-model-item",{attrs:{label:"子商户应用ID (AppID)",prop:"config.wechat.provider.subAppId",rules:[{required:!0,message:"请填写子商户应用ID (AppID)"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.config.wechat.provider.subAppId,callback:function(t){e.$set(e.record.config.wechat.provider,"subAppId",t)},expression:"record.config.wechat.provider.subAppId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("微信小程序或者微信公众号的APPID，需要在哪个客户端支付就填写哪个，APP支付需要填写开放平台的应用APPID")])])],1),t("a-form-model-item",{attrs:{label:"子商户号 (MchId)",prop:"config.wechat.provider.subMchId",rules:[{required:!0,message:"请填写子商户号 (MchId)"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.config.wechat.provider.subMchId,callback:function(t){e.$set(e.record.config.wechat.provider,"subMchId",t)},expression:"record.config.wechat.provider.subMchId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("微信支付的商户号，纯数字格式；例如：**********")])])],1),t("a-form-model-item",{attrs:{label:"服务商证书文件 (CERT)",prop:"config.wechat.provider.spApiclientCert",rules:[{required:!0,message:"需要上传该文件"}]}},[t("InputFile",{attrs:{accept:".pem"},on:{change:function(t){return e.onChangeInputFile(t,arguments,"spApiclientCert")}},model:{value:e.record.config.wechat.provider.spApiclientCert,callback:function(t){e.$set(e.record.config.wechat.provider,"spApiclientCert",t)},expression:"record.config.wechat.provider.spApiclientCert"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('请上传 "apiclient_cert.pem" 文件')])])],1),t("a-form-model-item",{attrs:{label:"服务商证书文件 (KEY)",prop:"config.wechat.provider.spApiclientKey",rules:[{required:!0,message:"需要上传该文件"}]}},[t("InputFile",{attrs:{accept:".pem"},on:{change:function(t){return e.onChangeInputFile(t,arguments,"spApiclientKey")}},model:{value:e.record.config.wechat.provider.spApiclientKey,callback:function(t){e.$set(e.record.config.wechat.provider,"spApiclientKey",t)},expression:"record.config.wechat.provider.spApiclientKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('请上传 "apiclient_key.pem" 文件')])])],1)],1):e._e()],1):e._e(),e.record.method===e.PaymentMethodEnum.ALIPAY.value?t("div",{attrs:{method:e.record.method}},[t("a-form-model-item",{attrs:{label:"支付宝应用 (AppID)",prop:"config.alipay.appId",rules:[{required:!0,message:"请填写支付宝应用 (AppID)"}]}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.record.config.alipay.appId,callback:function(t){e.$set(e.record.config.alipay,"appId",t)},expression:"record.config.alipay.appId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("支付宝分配给开发者的应用ID；例如：2021072300007148")])])],1),t("a-form-model-item",{attrs:{label:"签名算法 (signType)",prop:"config.alipay.signType",rules:[{required:!0,message:"请选择签名算法 (signType)"}]}},[t("a-radio-group",{model:{value:e.record.config.alipay.signType,callback:function(t){e.$set(e.record.config.alipay,"signType",t)},expression:"record.config.alipay.signType"}},[t("a-radio",{attrs:{value:"RSA2"}},[e._v("RSA2")]),t("a-radio",{attrs:{value:"RSA",disabled:!0}},[e._v("RSA")])],1)],1),t("a-form-model-item",{attrs:{label:"加签模式",prop:"config.alipay.signMode",rules:[{required:!0,message:"请选择加签模式"}]}},[t("a-radio-group",{model:{value:e.record.config.alipay.signMode,callback:function(t){e.$set(e.record.config.alipay,"signMode",t)},expression:"record.config.alipay.signMode"}},[t("a-radio",{attrs:{value:10}},[t("span",[e._v("公钥证书")]),t("a-tag",{staticClass:"ml-5",attrs:{color:"green"}},[e._v("推荐")])],1),t("a-radio",{attrs:{value:20}},[e._v("公钥")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("如需使用资金支出类的接口，则必须使用公钥证书模式")])])],1),20===e.record.config.alipay.signMode?t("div",{attrs:{method:e.record.method}},[t("a-form-model-item",{attrs:{label:"支付宝公钥 (alipayPublicKey)",prop:"config.alipay.alipayPublicKey",rules:[{required:!0,message:"请填写支付宝公钥 (alipayPublicKey)"}]}},[t("a-textarea",{attrs:{autoSize:{minRows:4,maxRows:6},autocomplete:"off"},model:{value:e.record.config.alipay.alipayPublicKey,callback:function(t){e.$set(e.record.config.alipay,"alipayPublicKey",t)},expression:"record.config.alipay.alipayPublicKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('可在 "支付宝开放平台" - "应用信息" - "接口加签方式" - "支付宝公钥" 中复制')])])],1)],1):e._e(),10===e.record.config.alipay.signMode?t("div",{attrs:{method:e.record.method}},[t("a-form-model-item",{attrs:{label:"应用公钥证书",prop:"config.alipay.appCertPublicKey",rules:[{required:!0,message:"需要上传该文件"}]}},[t("InputFile",{attrs:{accept:".crt"},on:{change:function(t){return e.onChangeInputFile(t,arguments,"appCertPublicKey")}},model:{value:e.record.config.alipay.appCertPublicKey,callback:function(t){e.$set(e.record.config.alipay,"appCertPublicKey",t)},expression:"record.config.alipay.appCertPublicKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('请上传 "appCertPublicKey_xxxxxxxx.crt" 文件')])])],1),t("a-form-model-item",{attrs:{label:"支付宝公钥证书",prop:"config.alipay.alipayCertPublicKey",rules:[{required:!0,message:"需要上传该文件"}]}},[t("InputFile",{attrs:{accept:".crt"},on:{change:function(t){return e.onChangeInputFile(t,arguments,"alipayCertPublicKey")}},model:{value:e.record.config.alipay.alipayCertPublicKey,callback:function(t){e.$set(e.record.config.alipay,"alipayCertPublicKey",t)},expression:"record.config.alipay.alipayCertPublicKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('请上传 "alipayCertPublicKey_RSA2.crt" 文件')])])],1),t("a-form-model-item",{attrs:{label:"支付宝根证书",prop:"config.alipay.alipayRootCert",rules:[{required:!0,message:"需要上传该文件"}]}},[t("InputFile",{attrs:{accept:".crt"},on:{change:function(t){return e.onChangeInputFile(t,arguments,"alipayRootCert")}},model:{value:e.record.config.alipay.alipayRootCert,callback:function(t){e.$set(e.record.config.alipay,"alipayRootCert",t)},expression:"record.config.alipay.alipayRootCert"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('请上传 "alipayRootCert.crt" 文件')])])],1)],1):e._e(),t("a-form-model-item",{attrs:{label:"应用私钥 (privateKey)",prop:"config.alipay.merchantPrivateKey",rules:[{required:!0,message:"请填写应用私钥 (privateKey)"}]}},[t("a-textarea",{attrs:{autoSize:{minRows:4,maxRows:6},autocomplete:"off"},model:{value:e.record.config.alipay.merchantPrivateKey,callback:function(t){e.$set(e.record.config.alipay,"merchantPrivateKey",t)},expression:"record.config.alipay.merchantPrivateKey"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v('查看 "应用私钥RSA2048-敏感数据，请妥善保管.txt" 文件，将全部内容复制到此处')])])],1)],1):e._e(),t("a-form-model-item",{attrs:{wrapperCol:{offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary",loading:e.isBtnLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)],1)},n=[],o=a("ade3"),l=(a("d3b7"),a("2ef0")),s=a("67e5"),c=a("759f"),d=a("3f9c"),u={name:"",method:d["a"].WECHAT.value,sort:100,remarks:"",config:(r={},Object(o["a"])(r,d["a"].WECHAT.value,{mchType:"normal",version:"v3",normal:{appId:"",mchId:"",apiKey:"",apiclientCert:"",apiclientKey:""},provider:{spAppId:"",spMchId:"",spApiKey:"",subAppId:"",subMchId:"",spApiclientCert:"",spApiclientKey:""}}),Object(o["a"])(r,d["a"].ALIPAY.value,{appId:"",signType:"RSA2",signMode:10,alipayPublicKey:"",appCertPublicKey:"",alipayCertPublicKey:"",alipayRootCert:"",merchantPrivateKey:""}),r)},p={components:{InputFile:s["a"]},data:function(){return{isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},PaymentMethodEnum:d["a"],record:Object(l["cloneDeep"])(u),uploadFiles:{apiclientCert:null,apiclientKey:null,spApiclientCert:null,spApiclientKey:null,appCertPublicKey:null,alipayCertPublicKey:null,alipayRootCert:null}}},created:function(){},methods:{clearValidate:function(){this.$refs.myForm.clearValidate()},handleSubmit:function(e){var t=this;e.preventDefault(),this.$refs.myForm.validate((function(e){if(!e)return!1;t.onSubmitForm()}))},onChangeInputFile:function(e,t,a){var r=this.uploadFiles;null!==t[1]&&(r[a]=t[1])},onSubmitForm:function(){var e=this;this.isLoading=!0,this.isBtnLoading=!0;var t=this.buildFormData();c["a"](t).then((function(t){e.$message.success(t.message,1.5),setTimeout((function(){e.$router.push("./index")}),1500)})).catch((function(){return e.isBtnLoading=!1})).finally((function(){return e.isLoading=!1}))},buildFormData:function(){var e=this.record,t=this.uploadFiles,a=new FormData;for(var r in a.append("name",e.name),a.append("sort",e.sort),a.append("method",e.method),a.append("remarks",e.remarks),a.append("config",JSON.stringify(e.config)),t)null!=t[r]&&a.append(r,t[r]);return a}}},m=p,f=(a("637f"),a("2877")),h=Object(f["a"])(m,i,n,!1,null,"7840e2d5",null);t["default"]=h.exports},a85e:function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{staticClass:"mb-20",attrs:{label:"短信平台",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["default",{rules:[{required:!0}]}],expression:"['default', { rules: [{ required: true }] }]"}],on:{change:e.onChangeEngine}},e._l(e.record.engine,(function(a,r){return t("a-radio",{key:r,attrs:{value:r}},[t("span",[e._v(e._s(a.name))]),"aliyun"===r?t("a-tag",{staticClass:"ml-5",attrs:{color:"green"}},[e._v("推荐")]):e._e()],1)})),1),e.form.getFieldValue("default")?t("div",{staticClass:"form-item-help"},[t("small",[e._v("短信平台管理地址：")]),t("a",{attrs:{href:e.record.engine[e.form.getFieldValue("default")].website,target:"_blank"}},[e._v(e._s(e.record.engine[e.form.getFieldValue("default")].website))])]):e._e()],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"aliyun"===e.form.getFieldValue("default"),expression:"form.getFieldValue('default') === 'aliyun'"}]},[t("a-form-item",{attrs:{label:"AccessKeyId",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.aliyun.AccessKeyId"],expression:"[`engine.aliyun.AccessKeyId`]"}]})],1),t("a-form-item",{attrs:{label:"AccessKeySecret",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.aliyun.AccessKeySecret"],expression:"[`engine.aliyun.AccessKeySecret`]"}]})],1),t("a-form-item",{attrs:{label:"短信签名 Sign",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.aliyun.sign"],expression:"[`engine.aliyun.sign`]"}]})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"qcloud"===e.form.getFieldValue("default"),expression:"form.getFieldValue('default') === 'qcloud'"}]},[t("a-form-item",{attrs:{label:"SdkAppID",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.qcloud.SdkAppID"],expression:"[`engine.qcloud.SdkAppID`]"}]})],1),t("a-form-item",{attrs:{label:"AccessKeyId",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.qcloud.AccessKeyId"],expression:"[`engine.qcloud.AccessKeyId`]"}]})],1),t("a-form-item",{attrs:{label:"AccessKeySecret",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.qcloud.AccessKeySecret"],expression:"[`engine.qcloud.AccessKeySecret`]"}]})],1),t("a-form-item",{attrs:{label:"短信签名 Sign",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.qcloud.sign"],expression:"[`engine.qcloud.sign`]"}]})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"qiniu"===e.form.getFieldValue("default"),expression:"form.getFieldValue('default') === 'qiniu'"}]},[t("a-form-item",{attrs:{label:"AccessKey",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.qiniu.AccessKey"],expression:"[`engine.qiniu.AccessKey`]"}]})],1),t("a-form-item",{attrs:{label:"SecretKey",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.qiniu.SecretKey"],expression:"[`engine.qiniu.SecretKey`]"}]})],1)],1),e._l(e.record["scene"],(function(a,r){return t("div",{key:r},[t("a-divider",{attrs:{orientation:"left"}},[e._v(e._s(a.name))]),t("a-form-item",{attrs:{label:"是否开启",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["scene.".concat(r,".isEnable"),{rules:[{required:!0}]}],expression:"[`scene.${index}.isEnable`, { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:!0}},[e._v("开启")]),t("a-radio",{attrs:{value:!1}},[e._v("关闭")])],1)],1),t("a-form-item",{attrs:{label:"模板内容",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("span",[e._v(e._s(e.record.scene[r].contentPractical))])]),t("a-form-item",{attrs:{label:"模板ID/Code",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["scene.".concat(r,".templateCode")],expression:"[`scene.${index}.templateCode`]"}]}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("例如：SMS_139800030")])])],1),void 0!==e.record.scene[r].acceptPhone?t("a-form-item",{attrs:{label:"接收手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["scene.".concat(r,".acceptPhone")],expression:"[`scene.${index}.acceptPhone`]"}]})],1):e._e()],1)})),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],2)],1)],1)},i=[],n=(a("d3b7"),a("ddb0"),a("4d63"),a("ac1f"),a("25f0"),a("5319"),a("2ef0")),o=a("ca00"),l=a("f585"),s=a("3267"),c={data:function(){return{SettingSmsSceneEnum:s["a"],key:"sms",labelCol:{span:3},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),record:{}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,l["a"](this.key).then((function(t){e.record=t.data.values,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},onChangeEngine:function(e){var t=this,a=e.target.value;for(var r in t.record.scene){var i=t.record.scene[r];i.contentPractical=t.onVsprintf(i.content,i.variables[a])}},onVsprintf:function(e,t){for(var a=new RegExp("%s"),r=0;r<t.length;r++)e=e.replace(a,t[r]);return e},setFieldsValue:function(){var e=this,t=e.record,a=e.$nextTick,r=e.form;!Object(o["g"])(r.getFieldsValue())&&a((function(){var a={};for(var i in t.scene){var o=t.scene[i],l=e.onVsprintf(o.content,o.variables[t.default]);e.$set(o,"contentPractical",l),a[i]=Object(n["pick"])(o,["isEnable","templateCode","acceptPhone"])}var s={};for(var c in t.engine)s[c]=Object(n["omit"])(t.engine[c],["name","website"]);r.setFieldsValue({default:t.default,engine:s,scene:a})}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,l["b"](this.key,{form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},d=c,u=(a("d6b8"),a("2877")),p=Object(u["a"])(d,r,i,!1,null,"2199021a",null);t["default"]=p.exports},b2dc:function(e,t,a){},bb50:function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{attrs:{span:5}},[e.$auth("/setting/payment/template/create")?t("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:function(t){return e.handleAdd()}}},[e._v("新增")]):e._e()],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"template_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"method",fn:function(a){return t("span",{},[t("a-tag",[e._v(e._s(e.PaymentMethodEnum[a].name))])],1)}},{key:"remarks",fn:function(a){return t("span",{},[e._v(e._s(a||"--"))])}},{key:"action",fn:function(a,r){return t("div",{staticClass:"actions"},[e.$auth("/setting/payment/template/update")?t("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]):e._e(),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")])])}}])})],1)},i=[],n=a("5530"),o=(a("d3b7"),a("3f9c")),l=a("759f"),s=a("2af9"),c={name:"Index",components:{STable:s["d"]},data:function(){var e=this;return{PaymentMethodEnum:o["a"],queryParam:{},isLoading:!1,columns:[{title:"模板ID",dataIndex:"template_id"},{title:"模板名称",dataIndex:"name"},{title:"支付方式",dataIndex:"method",scopedSlots:{customRender:"method"}},{title:"备注信息",dataIndex:"remarks",scopedSlots:{customRender:"remarks"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"更新时间",dataIndex:"update_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(t){return l["f"](Object(n["a"])(Object(n["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$router.push({path:"./create"})},handleEdit:function(e){this.$router.push({path:"./update",query:{templateId:e.template_id}})},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return l["c"]({templateId:e.template_id}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},d=c,u=a("2877"),p=Object(u["a"])(d,r,i,!1,null,null,null);t["default"]=p.exports},c553:function(e,t,a){},c5b8:function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"默认上传方式",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["default",{rules:[{required:!0}]}],expression:"['default', { rules: [{ required: true }] }]"}]},e._l(e.StorageEnum.data,(function(a,r){return t("a-radio",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name)+" "+e._s(a.value==e.StorageEnum.LOCAL.value?"(不推荐)":""))])})),1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("default")==e.StorageEnum.QINIU.value,expression:"form.getFieldValue('default') == StorageEnum.QINIU.value"}]},[t("a-form-item",{attrs:{label:"存储空间名称 Bucket",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.".concat(e.StorageEnum.QINIU.value,".bucket")],expression:"[`engine.${StorageEnum.QINIU.value}.bucket`]"}]})],1),t("a-form-item",{attrs:{label:"ACCESS_KEY AK",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.".concat(e.StorageEnum.QINIU.value,".access_key")],expression:"[`engine.${StorageEnum.QINIU.value}.access_key`]"}]})],1),t("a-form-item",{attrs:{label:"SECRET_KEY SK",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.".concat(e.StorageEnum.QINIU.value,".secret_key")],expression:"[`engine.${StorageEnum.QINIU.value}.secret_key`]"}]})],1),t("a-form-item",{attrs:{label:"空间域名 Domain",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.".concat(e.StorageEnum.QINIU.value,".domain")],expression:"[`engine.${StorageEnum.QINIU.value}.domain`]"}]})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("default")==e.StorageEnum.ALIYUN.value,expression:"form.getFieldValue('default') == StorageEnum.ALIYUN.value"}]},[t("a-form-item",{attrs:{label:"存储空间名称 Bucket",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.".concat(e.StorageEnum.ALIYUN.value,".bucket")],expression:"[`engine.${StorageEnum.ALIYUN.value}.bucket`]"}]})],1),t("a-form-item",{attrs:{label:"AccessKeyId",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.".concat(e.StorageEnum.ALIYUN.value,".access_key_id")],expression:"[`engine.${StorageEnum.ALIYUN.value}.access_key_id`]"}]})],1),t("a-form-item",{attrs:{label:"AccessKeySecret",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.".concat(e.StorageEnum.ALIYUN.value,".access_key_secret")],expression:"[`engine.${StorageEnum.ALIYUN.value}.access_key_secret`]"}]})],1),t("a-form-item",{attrs:{label:"空间域名 Domain",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.".concat(e.StorageEnum.ALIYUN.value,".domain")],expression:"[`engine.${StorageEnum.ALIYUN.value}.domain`]"}]})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("default")==e.StorageEnum.QCLOUD.value,expression:"form.getFieldValue('default') == StorageEnum.QCLOUD.value"}]},[t("a-form-item",{attrs:{label:"存储空间名称 Bucket",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.".concat(e.StorageEnum.QCLOUD.value,".bucket")],expression:"[`engine.${StorageEnum.QCLOUD.value}.bucket`]"}]})],1),t("a-form-item",{attrs:{label:"所属地域 Region",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.".concat(e.StorageEnum.QCLOUD.value,".region")],expression:"[`engine.${StorageEnum.QCLOUD.value}.region`]"}]})],1),t("a-form-item",{attrs:{label:"SecretId",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.".concat(e.StorageEnum.QCLOUD.value,".secret_id")],expression:"[`engine.${StorageEnum.QCLOUD.value}.secret_id`]"}]})],1),t("a-form-item",{attrs:{label:"SecretKey",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.".concat(e.StorageEnum.QCLOUD.value,".secret_key")],expression:"[`engine.${StorageEnum.QCLOUD.value}.secret_key`]"}]})],1),t("a-form-item",{attrs:{label:"空间域名 Domain",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["engine.".concat(e.StorageEnum.QCLOUD.value,".domain")],expression:"[`engine.${StorageEnum.QCLOUD.value}.domain`]"}]})],1)],1),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},i=[],n=(a("d3b7"),a("ddb0"),a("88bc")),o=a.n(n),l=a("f585"),s=a("71b5"),c=a("ca00"),d={data:function(){return{key:"storage",labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),record:{},StorageEnum:s["a"]}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,l["a"](this.key).then((function(t){e.record=t.data.values,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,t=this.$nextTick,a=this.form;!Object(c["g"])(a.getFieldsValue())&&t((function(){a.setFieldsValue(o()(e,["default","engine"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,l["b"](this.key,{form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},u=d,p=(a("7710"),a("2877")),m=Object(p["a"])(u,r,i,!1,null,"a6704cac",null);t["default"]=m.exports},c7eb:function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));a("a4d3"),a("e01a"),a("d3b7"),a("d28b"),a("3ca3"),a("ddb0"),a("b636"),a("944a"),a("0c47"),a("23dc"),a("3410"),a("159b"),a("b0c0"),a("131a"),a("fb6a");var r=a("53ca");function i(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
i=function(){return e};var e={},t=Object.prototype,a=t.hasOwnProperty,n=Object.defineProperty||function(e,t,a){e[t]=a.value},o="function"==typeof Symbol?Symbol:{},l=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function d(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(q){d=function(e,t,a){return e[t]=a}}function u(e,t,a,r){var i=t&&t.prototype instanceof f?t:f,o=Object.create(i.prototype),l=new E(r||[]);return n(o,"_invoke",{value:x(e,a,l)}),o}function p(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(q){return{type:"throw",arg:q}}}e.wrap=u;var m={};function f(){}function h(){}function v(){}var g={};d(g,l,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(L([])));y&&y!==t&&a.call(y,l)&&(g=y);var C=v.prototype=f.prototype=Object.create(g);function _(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function i(n,o,l,s){var c=p(e[n],e,o);if("throw"!==c.type){var d=c.arg,u=d.value;return u&&"object"==Object(r["a"])(u)&&a.call(u,"__await")?t.resolve(u.__await).then((function(e){i("next",e,l,s)}),(function(e){i("throw",e,l,s)})):t.resolve(u).then((function(e){d.value=e,l(d)}),(function(e){return i("throw",e,l,s)}))}s(c.arg)}var o;n(this,"_invoke",{value:function(e,a){function r(){return new t((function(t,r){i(e,a,t,r)}))}return o=o?o.then(r,r):r()}})}function x(e,t,a){var r="suspendedStart";return function(i,n){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw n;return S()}for(a.method=i,a.arg=n;;){var o=a.delegate;if(o){var l=k(o,a);if(l){if(l===m)continue;return l}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===r)throw r="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r="executing";var s=p(e,t,a);if("normal"===s.type){if(r=a.done?"completed":"suspendedYield",s.arg===m)continue;return{value:s.arg,done:a.done}}"throw"===s.type&&(r="completed",a.method="throw",a.arg=s.arg)}}}function k(e,t){var a=t.method,r=e.iterator[a];if(void 0===r)return t.delegate=null,"throw"===a&&e.iterator["return"]&&(t.method="return",t.arg=void 0,k(e,t),"throw"===t.method)||"return"!==a&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+a+"' method")),m;var i=p(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,m;var n=i.arg;return n?n.done?(t[e.resultName]=n.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,m):n:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,m)}function I(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(I,this),this.reset(!0)}function L(e){if(e){var t=e[l];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(a.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:S}}function S(){return{value:void 0,done:!0}}return h.prototype=v,n(C,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:h,configurable:!0}),h.displayName=d(v,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,d(e,c,"GeneratorFunction")),e.prototype=Object.create(C),e},e.awrap=function(e){return{__await:e}},_(w.prototype),d(w.prototype,s,(function(){return this})),e.AsyncIterator=w,e.async=function(t,a,r,i,n){void 0===n&&(n=Promise);var o=new w(u(t,a,r,i),n);return e.isGeneratorFunction(a)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},_(C),d(C,c,"Generator"),d(C,l,(function(){return this})),d(C,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),a=[];for(var r in t)a.push(r);return a.reverse(),function e(){for(;a.length;){var r=a.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=L,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(A),!e)for(var t in this)"t"===t.charAt(0)&&a.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(a,r){return o.type="throw",o.arg=e,t.next=a,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i],o=n.completion;if("root"===n.tryLoc)return r("end");if(n.tryLoc<=this.prev){var l=a.call(n,"catchLoc"),s=a.call(n,"finallyLoc");if(l&&s){if(this.prev<n.catchLoc)return r(n.catchLoc,!0);if(this.prev<n.finallyLoc)return r(n.finallyLoc)}else if(l){if(this.prev<n.catchLoc)return r(n.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return r(n.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&a.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=e,o.arg=t,n?(this.method="next",this.next=n.finallyLoc,m):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),A(a),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var r=a.completion;if("throw"===r.type){var i=r.arg;A(a)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,a){return this.delegate={iterator:L(e),resultName:t,nextLoc:a},"next"===this.method&&(this.arg=void 0),m}},e}},d3a6:function(e,t,a){},d6b8:function(e,t,a){"use strict";a("e8b0")},e77f:function(e,t,a){},e8b0:function(e,t,a){},ea54:function(e,t,a){"use strict";a("552c")},f4eb:function(e,t,a){"use strict";a.r(t);a("ac1f"),a("841c");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{attrs:{span:6}},[t("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),t("a-col",{attrs:{span:8,offset:10}},[t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px",float:"right"},attrs:{placeholder:"请输入物流公司名称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"express_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"action",fn:function(a,r){return t("span",{staticClass:"actions"},[t("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),t("AddForm",{ref:"AddForm",on:{handleSubmit:e.handleRefresh}}),t("EditForm",{ref:"EditForm",on:{handleSubmit:e.handleRefresh}})],1)},i=[],n=a("5530"),o=(a("d3b7"),a("2a66")),l=a("2af9"),s=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"物流公司名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["express_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['express_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),t("a-form-item",{attrs:{label:"公司编码（快递100）",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["kuaidi100_code",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['kuaidi100_code', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"用于快递100查询物流信息API"}}),t("p",{staticClass:"form-item-help"},[t("small",[t("span",[e._v("请参照")]),t("a",{attrs:{href:"https://api.kuaidi100.com/manager/page/document/kdbm",target:"_blank"}},[e._v("物流公司编码表")])])])],1),t("a-form-item",{attrs:{label:"公司编码（快递鸟）",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["kdniao_code",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['kdniao_code', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"用于快递鸟打印电子面单API"}}),t("p",{staticClass:"form-item-help"},[t("small",[t("span",[e._v("请参照")]),t("a",{attrs:{href:"http://www.kdniao.com/documents",target:"_blank"}},[e._v("快递鸟接口支持快递公司编码.xlsx")])])])],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},c=[],d={data:function(){return{title:"新增物流公司",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{add:function(){this.visible=!0},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,o["a"]({form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){return t.confirmLoading=!1}))}}},u=d,p=a("2877"),m=Object(p["a"])(u,s,c,!1,null,null,null),f=m.exports,h=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"物流公司名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["express_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['express_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),t("a-form-item",{attrs:{label:"公司编码（快递100）",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["kuaidi100_code",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['kuaidi100_code', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"用于快递100查询物流信息API"}}),t("p",{staticClass:"form-item-help"},[t("small",[t("span",[e._v("请参照")]),t("a",{attrs:{href:"https://api.kuaidi100.com/manager/page/document/kdbm",target:"_blank"}},[e._v("物流公司编码表")])])])],1),t("a-form-item",{attrs:{label:"公司编码（快递鸟）",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["kdniao_code",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['kdniao_code', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"用于快递鸟打印电子面单API"}}),t("p",{staticClass:"form-item-help"},[t("small",[t("span",[e._v("请参照")]),t("a",{attrs:{href:"http://www.kdniao.com/documents",target:"_blank"}},[e._v("快递鸟接口支持快递公司编码.xlsx")])])])],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},v=[],g=a("88bc"),b=a.n(g),y={data:function(){return{title:"编辑物流公司",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{edit:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this,t=this.form.setFieldsValue;this.$nextTick((function(){t(b()(e.record,["express_name","kuaidi100_code","kdniao_code","sort"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,o["d"]({expressId:this.record.express_id,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){return t.confirmLoading=!1}))}}},C=y,_=Object(p["a"])(C,h,v,!1,null,null,null),w=_.exports,x={name:"Index",components:{STable:l["d"],AddForm:f,EditForm:w},data:function(){var e=this;return{queryParam:{},isLoading:!1,columns:[{title:"物流公司ID",dataIndex:"express_id"},{title:"物流公司名称",dataIndex:"express_name"},{title:"公司编码（快递100）",dataIndex:"kuaidi100_code"},{title:"公司编码（快递鸟）",dataIndex:"kdniao_code"},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(t){return o["e"](Object(n["a"])(Object(n["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return o["c"]({expressId:e.express_id}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},k=x,I=Object(p["a"])(k,r,i,!1,null,null,null);t["default"]=I.exports},f50c:function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"b",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"d",(function(){return s})),a.d(t,"c",(function(){return c}));var r=a("b775"),i={list:"/setting.printer/list",all:"/setting.printer/all",add:"/setting.printer/add",edit:"/setting.printer/edit",delete:"/setting.printer/delete"};function n(e){return Object(r["b"])({url:i.list,method:"get",params:e})}function o(e){return Object(r["b"])({url:i.all,method:"get",params:e})}function l(e){return Object(r["b"])({url:i.add,method:"post",data:e})}function s(e){return Object(r["b"])({url:i.edit,method:"post",data:e})}function c(e){return Object(r["b"])({url:i.delete,method:"post",data:e})}},f585:function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return l}));var r=a("5530"),i=a("b775"),n={detail:"/setting/detail",update:"/setting/update"};function o(e){return Object(i["b"])({url:n.detail,method:"get",params:{key:e}})}function l(e,t){return Object(i["b"])({url:n.update,method:"post",data:Object(r["a"])({key:e},t)})}},fed5c:function(e,t,a){}}]);